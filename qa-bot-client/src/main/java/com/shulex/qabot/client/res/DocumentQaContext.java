package com.shulex.qabot.client.res;

import com.shulex.gpt.apisdk.dto.GetDocumentResponse;
import com.shulex.qabot.client.dto.EsSearchResult;
import com.shulex.qabot.client.dto.esentity.DocumentChunkEsEntity;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
public class DocumentQaContext {
    private List<EsSearchResult<DocumentChunkEsEntity>> searchChunkEsResults;
    private List<EsSearchResult<DocumentChunkEsEntity>> relevanceChunks;
    private List<List<DocumentChunkEsEntity>> usedChunks;
    private List<GetDocumentResponse> usedDocuments;
    private String chunkContents;
    private String systemContent;
}
