package com.shulex.qabot.feign.gpt;

import com.shulex.gpt.apisdk.IToolsApi;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Profile;

@FeignClient(name = "shulex-gpt", contextId = "IGptToolApiFeignClient")
@Profile({"test", "prod"})
public interface IGptToolApiFeignClient extends IToolsApi {
}

@FeignClient(name = "shulex-gpt", contextId = "IGptToolApiFeignClientDev", url = "http://10.7.4.9:8008")
@Profile({"dev"})
interface IGptToolApiFeignClientDev extends IToolsApi {
}
