package com.shulex.qabot.config;

import cn.hutool.core.collection.CollectionUtil;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

@Data
@Component
@ConfigurationProperties(prefix = "qa-chat")
public class QaChatConfig {
    Double kuDirectReturnUserQuestionCohereSimilarity;
    Double kuDirectReturnKnowledgeCohereSimilarity;
    Map<Long, Integer> kuRelevanceCountTenantMap;

    Double intentSampleThreshold = 0.9;
    Map<Long, Double> intentSampleThresholdTenantMap = new HashMap<>();
    Set<String> systemPrecedenceIntents = new HashSet<>();
    Map<Long, Boolean> userQuestionIntentIdentifyTenantMap = new HashMap<>();

    Map<Long, Integer> documentChunkContentEsSearchSizeTenantMap = new HashMap<>();
    Double documentChunkContentThreshold;
    Map<Long, Double> documentChunkContentThresholdTenantMap;
    Integer documentChunkExpandSize;
    Map<Long, Integer> documentChunkExpandSizeTenantMap;
    Integer productChunkExpandSize = 2;
    Map<Long, Integer> productChunkExpandSizeTenantMap;
    Integer documentRespWordsLimit;
    Integer documentRelevanceCount = 5;
    Map<Long, Integer> documentRelevanceCountTenantMap;
    Set<Long> needDocumentRelevanceTenantIds;

    Double productRecommendThreshold = 0.65;
    Map<Long, Double> productRecommendThresholdTenantMap = new HashMap<>();
    Map<Long, Boolean> productRecommendNeedRelevanceTenantMap = new HashMap<>();

    Integer productRecommendRelevanceCount = 5;
    Double productRecommendRelevanceMinValue = 6.0;

    Double esTextScoreMax = 100.0;
    Double esTextScoreMin = 0.0;
    Double embeddingScaleMax = 1.5;
    Double embeddingScaleMin = 1.2;
    Map<String, Float> fieldBoostMap = new HashMap<>();
    Integer intentBoostScale = 3;
    Map<Long, KnowledgeSearchConfig> knowledgeSearchConfigTenantMap = new HashMap<>();
    Set<Long> notExpandKeywordMatchScaleTenantIds = new HashSet<>();

    Integer algIntentSampleCount = 6;

    Map<Long, Boolean> useMetaDataConditionTagTenantMap = new HashMap<>();

    public Double getDocumentChunkContentThresholdBy(Long tenantId) {
        return documentChunkContentThresholdTenantMap.getOrDefault(tenantId, documentChunkContentThreshold);
    }

    public Integer getDocumentRelevanceCountBy(Long tenantId) {
        return documentRelevanceCountTenantMap.getOrDefault(tenantId, documentRelevanceCount);
    }

    public Integer getDocumentChunkExpandSizeBy(Long tenantId) {
        return documentChunkExpandSizeTenantMap.getOrDefault(tenantId, documentChunkExpandSize);
    }

    public Integer getProductChunkExpandSizeBy(Long tenantId) {
        return productChunkExpandSizeTenantMap.getOrDefault(tenantId, productChunkExpandSize);
    }

    public Integer getKuRelevanceCountBy(Long tenantId) {
        return kuRelevanceCountTenantMap.getOrDefault(tenantId, 5);
    }

    public Double getIntentSampleThreshold(Long tenantId) {
        return intentSampleThresholdTenantMap.getOrDefault(tenantId, intentSampleThreshold);
    }

    public Double getProductRecommendThresholdBy(Long tenantId) {
        return productRecommendThresholdTenantMap.getOrDefault(tenantId, productRecommendThreshold);
    }

    public Boolean getProductRecommendNeedRelevanceBy(Long tenantId) {
        return productRecommendNeedRelevanceTenantMap.getOrDefault(tenantId, true);
    }

    public Integer getDocumentChunkContentEsSearchSizeBy(Long tenantId) {
        return documentChunkContentEsSearchSizeTenantMap.getOrDefault(tenantId, 5);
    }

    public KnowledgeSearchConfig getKnowledgeSearchConfigBy(Long tenantId) {
        KnowledgeSearchConfig res;
        KnowledgeSearchConfig defaultConfig = knowledgeSearchConfigTenantMap.get(0L);
        KnowledgeSearchConfig knowledgeSearchConfig = knowledgeSearchConfigTenantMap.get(tenantId);
        if (knowledgeSearchConfig != null) {
            if (CollectionUtil.isEmpty(knowledgeSearchConfig.getQaFieldBoostMap())) {
                knowledgeSearchConfig.setQaFieldBoostMap(defaultConfig.getQaFieldBoostMap());
            }
            if (CollectionUtil.isEmpty(knowledgeSearchConfig.getDocFieldBoostMap())) {
                knowledgeSearchConfig.setDocFieldBoostMap(defaultConfig.getDocFieldBoostMap());
            }
            if (CollectionUtil.isEmpty(knowledgeSearchConfig.getProductFieldBoostMap())) {
                knowledgeSearchConfig.setProductFieldBoostMap(defaultConfig.getProductFieldBoostMap());
            }
            if (CollectionUtil.isEmpty(knowledgeSearchConfig.getNeedRelevanceTouchPointMap())) {
                knowledgeSearchConfig.setNeedRelevanceTouchPointMap(defaultConfig.getNeedRelevanceTouchPointMap());
            }
            res = knowledgeSearchConfig;
        } else {
            res = defaultConfig;
        }
        if (notExpandKeywordMatchScaleTenantIds.contains(tenantId)) {
            // 特殊租户不扩大倍数和分数
            res.setEmbeddingScaleMax(1.5);
            Map<String, Float> qaFieldBoostMap = res.getQaFieldBoostMap();
            Map<String, Float> docFieldBoostMap = res.getDocFieldBoostMap();
            Map<String, Float> productFieldBoostMap = res.getProductFieldBoostMap();
            qaFieldBoostMap.put("tags", 2.0F);
            docFieldBoostMap.put("tags", 2.0F);
            productFieldBoostMap.put("selfTags", 2.0F);
        }
        return res;
    }

    @Data
    @Accessors(chain = true)
    public static class KnowledgeSearchConfig {
        Double kuDirectReturnUserQuestionCohereSimilarity = 0.9;
        Double kuDirectReturnKnowledgeCohereSimilarity = 0.9;
        Map<String, Float> qaFieldBoostMap = new HashMap<>();
        Map<String, Float> docFieldBoostMap = new HashMap<>();
        Map<String, Float> productFieldBoostMap = new HashMap<>();
        Float intentBoost = 3.0F;
        Integer qaEsSearchSize = 10;
        Integer docEsSearchSize = 5;
        Integer productEsSearchSize = 5;
        Double qaEsThreshold = 0.5;
        Double docEsThreshold = 0.5;
        Double productEsThreshold = 0.5;
        Integer qaUsedSize = 6;
        Integer docUsedSize = 3;
        Integer productUsedSize = 3;
        Integer knowledgeMaxToken = 4096;
        Map<String, Boolean> needRelevanceTouchPointMap = new HashMap<>();
        Boolean livechatNeedRelevance = false;
        Boolean notLivechatNeedRelevance = true;
        Double esTextScoreMax = 100.0;
        Double esTextScoreMin = 0.0;
        Double embeddingScaleMax = 2.2;
        Double embeddingScaleMin = 1.2;
    }
}
