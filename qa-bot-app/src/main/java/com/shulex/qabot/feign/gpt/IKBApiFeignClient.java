package com.shulex.qabot.feign.gpt;

import com.shulex.gpt.apisdk.IKBApi;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Profile;

@FeignClient(name = "shulex-gpt", contextId = "IKBApiFeignClient")
@Profile({"test", "prod"})
public interface IKBApiFeignClient extends IKBApi {
}

@FeignClient(name = "shulex-gpt", contextId = "IKBApiFeignClientDev", url = "http://10.7.4.9:8008")
@Profile({"dev"})
interface IKBApiFeignClientDev extends IKBApi {

}
