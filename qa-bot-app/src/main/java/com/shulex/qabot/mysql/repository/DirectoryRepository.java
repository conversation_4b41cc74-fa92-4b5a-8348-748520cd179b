package com.shulex.qabot.mysql.repository;

import cn.hutool.core.lang.func.LambdaUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.shulex.gpt.apisdk.enums.DirectoryTypeEnum;
import com.shulex.qabot.client.dto.mysqlentity.Directory;
import com.shulex.qabot.mysql.mapper.DirectoryMapper;
import org.springframework.stereotype.Repository;

import java.util.List;

import static java.lang.String.format;

@Repository
public class DirectoryRepository extends ServiceImpl<DirectoryMapper, Directory> {
    public List<Directory> getDirectories(long tenantId, long projectId) {
        return lambdaQuery()
                .eq(Directory::getTenantId, tenantId)
                .eq(Directory::getProjectId, projectId)
                .orderByDesc(Directory::getId)
                .list();
    }

    public List<Directory> getOtherDirectories(long tenantId, long projectId) {
        return lambdaQuery()
                .eq(Directory::getTenantId, tenantId)
                .eq(Directory::getProjectId, projectId)
                .eq(Directory::getType, DirectoryTypeEnum.GENERIC)
                .apply(format("LOWER(`%s`) = '%s'", LambdaUtil.getFieldName(Directory::getName), "other"))
                .orderByDesc(Directory::getId)
                .list();
    }
}
