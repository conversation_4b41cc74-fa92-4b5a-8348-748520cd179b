package com.shulex.qabot.client.dto;

import com.shulex.gpt.apisdk.dto.MixedQuery;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;
import java.util.Map;

@Data
@Accessors(chain = true)
public class IntentSampleDTO {
    @ApiModelProperty(value = "检测样本的会话id")
    String chatId;
    @ApiModelProperty(value = "会话创建时间")
    Long chatCreatedAt;
    @ApiModelProperty(value = "tenantId")
    Long tenantId;
    @ApiModelProperty(value = "projectId")
    Long projectId;
    @ApiModelProperty(value = "会话首轮用户问题标题")
    String firstTurnTitle;
    @ApiModelProperty(value = "会话首轮用户问题内容")
    String firstTurnContent;
    @ApiModelProperty(value = "会话首轮系统回复内容")
    String firstTurnReply;
    @ApiModelProperty(value = "会话首轮系统识别的意图id")
    Long firstTurnIntentId;
    @ApiModelProperty(value = "会话首轮系统识别的意图名")
    String firstTurnIntentName;
    @ApiModelProperty(value = "会话首轮系统对用户问题的总结")
    MixedQuery firstTurnMixedQuery;
    @ApiModelProperty(value = "会话首轮系统对用户问题的实体抽取")
    Map<String, List<Object>> fistTurnEntities;
    @ApiModelProperty(value = "重新意图识别后的意图id")
    Long intentId;
    @ApiModelProperty(value = "重新意图识别后的意图名称")
    String intentName;
    @ApiModelProperty(value = "样本正负性,true代表正样本,false代表负样本")
    Boolean pnLabel = true;
}
