package com.shulex.qabot.client.req;

import com.shulex.gpt.apisdk.dto.AiReplyPromptInfo;
import com.shulex.gpt.apisdk.dto.Message;
import com.shulex.gpt.apisdk.dto.MixedQuery;
import com.shulex.gpt.apisdk.dto.RetrievedKu;
import com.shulex.gpt.apisdk.enums.LLMTypeEnum;
import lombok.Data;

import java.util.List;
import java.util.Map;
import java.util.Set;

@Data
public class QaChatReq {
    Long tenantId;
    String clientType;
    Long projectId;
    String touchPoint;
    List<RetrievedKu> specifiedKuList;
    String botName;
    String chatId;
    String intent;
    String originIntent;
    MixedQuery mixedQuery;
    List<Long> directoryIds;
    Map<String, List<?>> entities;
    List<Message> summarizedMessages;
    AiReplyOption aiReplyOption;
    Map<Long, Set<Long>> kuLabelDimensionId2ValueIds;
    String pdt;
    String roleDescription;
    LLMTypeEnum modelType;
    AiReplyPromptInfo aiReplyPromptInfo;

    @Data
    public static class AiReplyOption {
        String reply_type;
        String llm_model;
        Integer top_k = 5;
        Double retrieve_min_score = 0.75;
        Integer words_limit;
        String language;
    }
}
