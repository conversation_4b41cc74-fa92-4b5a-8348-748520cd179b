package com.shulex.qabot.kafka;

import cn.hutool.core.exceptions.ExceptionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.otter.canal.client.CanalMessageDeserializer;
import com.alibaba.otter.canal.protocol.CanalEntry;
import com.alibaba.otter.canal.protocol.Message;
import com.shulex.qabot.alg.enmus.agent.enums.DmsOperateEnum;
import com.shulex.qabot.client.dto.DmsMessage;
import com.shulex.qabot.client.dto.DmsMetadata;
import com.shulex.qabot.client.dto.consts.KafkaTopic;
import com.shulex.qabot.config.NoticeConfig;
import com.shulex.qabot.client.dto.mysqlentity.Directory;
import com.shulex.qabot.service.DirectoryService;
import com.shulex.qabot.util.canal.FlatMessage;
import com.shulex.qabot.util.canal.MessageUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
@Slf4j
public class DirectoryBinlogConsumer {
    @Autowired
    DirectoryService directoryService;
    @Autowired
    NoticeConfig noticeConfig;



    @KafkaListener(topics = {KafkaTopic.DMS_DIRECTORY_TOPIC}, groupId = KafkaTopic.DMS_GROUP, concurrency = "2", properties = {"max.poll.records=50"})
    public void dmsDirectoryListener(List<byte[]> records, Acknowledgment ack) {
        List<DmsMessage> dmsMessages = null;
        try {
            dmsMessages = records.stream()
                    .map(dat -> JSON.parseObject(new String(dat), DmsMessage.class))
                    .collect(Collectors.toList());
            log.info("dmsDirectoryListener recordsSize={}, msgSize={}, msgList:\n{}", records.size(), dmsMessages.size(), dmsMessages.stream().map(JSON::toJSONString).collect(Collectors.joining("\n")));
            List<Directory> insertDirectoryList = new ArrayList<>();
            List<Directory> deleteDirectoryList = new ArrayList<>();
            List<Directory> updateDirectoryList = new ArrayList<>();
            for (DmsMessage flatMessage : dmsMessages) {
                DmsMetadata metadata = flatMessage.getMetadata();
                String operation = metadata.getOperation();
                if (flatMessage.getData() != null) {
                    Directory directory = JSON.parseObject(JSON.toJSONString(flatMessage.getData()), Directory.class);
                    if (DmsOperateEnum.INSERT.getValue().equals(operation)) {
                        insertDirectoryList.add(directory);
                    } else if (DmsOperateEnum.DELETE.getValue().equals(operation)) {
                        deleteDirectoryList.add(directory);
                    } else if (DmsOperateEnum.UPDATE.getValue().equals(operation)) {
                        updateDirectoryList.add(directory);
                    }
                }
            }
            directoryService.insertDirectoryToEs(insertDirectoryList);
            directoryService.updateDirectoryInEs(updateDirectoryList);
            directoryService.deleteDirectoryInEs(deleteDirectoryList);
            ack.acknowledge();
        } catch (Exception e) {
            log.error("dmsDirectoryListener error, flatMessages={}, ", JSON.toJSONString(dmsMessages), e);
            noticeConfig.sendError("dmsDirectoryListener\n" + ExceptionUtil.stacktraceToString(e));
            ack.nack(0, 5000L);
        }
    }


    @KafkaListener(topics = {"directory-sync-topic"}, groupId = "qa-bot-group", concurrency = "2", properties = {"max.poll.records=50"})
    public void directoryBinlogListener(List<byte[]> records, Acknowledgment ack) {
        List<FlatMessage> flatMessages = null;
        try {
            flatMessages = records.stream().flatMap(record -> {
                Message message = CanalMessageDeserializer.deserializer(record);
                return MessageUtil.convert(message).stream();
            }).collect(Collectors.toList());
            log.info("directoryBinlogListener recordsSize={}, msgSize={}, msgList:\n{}", records.size(), flatMessages.size(), flatMessages.stream().map(JSON::toJSONString).collect(Collectors.joining("\n")));
            List<Directory> insertDirectoryList = new ArrayList<>();
            List<Directory> deleteDirectoryList = new ArrayList<>();
            List<Directory> updateDirectoryList = new ArrayList<>();
            for (FlatMessage flatMessage : flatMessages) {
                String type = flatMessage.getType();
                List<Map<String, Object>> dataList = flatMessage.getData();
                List<Directory> directoryList = dataList.stream().map(data -> JSON.parseObject(JSON.toJSONString(data), Directory.class)).collect(Collectors.toList());
                if (type.equals(CanalEntry.EventType.INSERT.name())) {
                    insertDirectoryList.addAll(directoryList);
                } else if (type.equals(CanalEntry.EventType.DELETE.name())) {
                    deleteDirectoryList.addAll(directoryList);
                } else if (type.equals(CanalEntry.EventType.UPDATE.name())) {
                    updateDirectoryList.addAll(directoryList);
                }
            }
            directoryService.insertDirectoryToEs(insertDirectoryList);
            directoryService.updateDirectoryInEs(updateDirectoryList);
            directoryService.deleteDirectoryInEs(deleteDirectoryList);
            ack.acknowledge();
        } catch (Exception e) {
            log.error("directoryBinlogListener error, flatMessages={}, ", JSON.toJSONString(flatMessages), e);
            noticeConfig.sendError("directoryBinlogListener\n" + ExceptionUtil.stacktraceToString(e));
            ack.nack(0, 5000L);
        }
    }
}
