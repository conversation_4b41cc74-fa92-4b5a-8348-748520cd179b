package com.shulex.qabot.mysql.repository;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.shulex.qabot.client.dto.mysqlentity.DocumentChunk;
import com.shulex.qabot.client.dto.mysqlentity.KuProduct;
import com.shulex.qabot.mysql.mapper.KuProductMapper;
import org.springframework.stereotype.Repository;

import java.util.Collection;

@Repository
public class KuProductRepository extends ServiceImpl<KuProductMapper, KuProduct> {
    public boolean exist(long tenantId) {
        return lambdaQuery().eq(KuProduct::getTenantId, tenantId).exists();
    }

    public void indexSucceed(Collection<Long> ids) {
        if (CollectionUtil.isNotEmpty(ids)) {
            lambdaUpdate()
                    .in(KuProduct::getId, ids)
                    .set(KuProduct::getIndexed, true)
                    .update();
        }
    }
}
