package com.shulex.qabot.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Data
@Component
@ConfigurationProperties(prefix = "general")
public class GeneralConfig {
    String productPrompt = "You are making a customer service, now you have to make up a sentense that your customer might say about product recommandation.\\nYou have to follow the following rules:\\n1. respond in the language {}.\\n2. the sentense should be asking for a recommandation for the product.\\n3. Speak concisely.\\n4. Pretending this customer doesn't know which product in this e-shop fits his/her demands.\\n5. Only return 1 recommand question.\\n\\ne.g.\\nProduct Title: '''Yoga pants'''\\nSentense: Can you recommand me a yoga pants? Which one is fit for a slim body.\\n\\n\\nHere's the product title:\\n'''\\n{}\\n'''\\n\\n\\nSentense:";
    Integer updateMaxRetry = 3;
}
