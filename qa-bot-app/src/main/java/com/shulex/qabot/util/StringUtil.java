package com.shulex.qabot.util;

import cn.hutool.core.lang.Pair;
import com.alibaba.fastjson.JSON;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

public class StringUtil {
    public static boolean containsIgnoreCase(String str, String searchStr) {
        return str.toLowerCase().contains(searchStr.toLowerCase());
    }

    public static List<String> extractUrl(String text) {
        if (text == null) {
            return new ArrayList<>();
        }
        List<String> urls = new ArrayList<>();
        String pattern = "(?i)\\b((https?|ftp|file)://|www\\.|ftp\\.)[-A-Z0-9+&@#/%?=~_|!:,.;]*[A-Z0-9+&@#/%=~_|]";
        Pattern urlPattern = Pattern.compile(pattern, Pattern.CASE_INSENSITIVE);
        Matcher urlMatcher = urlPattern.matcher(text);
        while (urlMatcher.find()) {
            String url = text.substring(urlMatcher.start(0), urlMatcher.end(0));
            urls.add(url);
        }
        return urls;
    }

    // 返回值第一个值为匹配到的整个image标签，第二个值为image标签括号中的内容（即链接）
    public static List<Pair<String, String>> extractMarkdownImages(String markdownText) {
        List<Pair<String, String>> imageUrls = new ArrayList<>();
        String regex = "!\\[.*?\\]\\((.*?)\\)";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(markdownText);
        while (matcher.find()) {
            imageUrls.add(Pair.of(matcher.group(0), matcher.group(1)));
        }
        return imageUrls;
    }

    public static String stringToJsonString(String str) {
        if (str == null) {
            return str;
        }
        String jsonString = JSON.toJSONString(str);
        return jsonString.substring(1, jsonString.length() - 1);
    }

    public static List<String> getProductNamesFromEntities(Map<String, List<?>> entities) {
        String productNameKey = "product_name";
        return getStringFromEntities(entities, productNameKey);
    }

    public static List<String> getProductNamesFromEntities2(Map<String, List<Object>> entities) {
        String productNameKey = "product_name";
        if (entities != null && entities.containsKey(productNameKey)) {
            List<String> productNames = entities.get(productNameKey).stream().filter(Objects::nonNull).map(Object::toString).collect(Collectors.toList());
            return productNames;
        }
        return new ArrayList<>();
    }

    public static List<String> getStringFromEntities(Map<String, List<?>> entities, String key) {
        if (entities != null && entities.containsKey(key)) {
            List<String> productNames = entities.get(key).stream().filter(Objects::nonNull).map(Object::toString).collect(Collectors.toList());
            return productNames;
        }
        return new ArrayList<>();
    }
}
