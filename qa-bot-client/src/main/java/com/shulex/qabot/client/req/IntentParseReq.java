package com.shulex.qabot.client.req;

import com.shulex.gpt.apisdk.dto.Message;
import com.shulex.gpt.apisdk.dto.MixedQuery;
import com.shulex.gpt.apisdk.enums.BotLanguageEnum;
import com.shulex.qabot.client.dto.TagDTO;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;
import java.util.Map;

@Data
@Accessors(chain = true)
public class IntentParseReq {
    String chatId;
    Long tenantId;
    String tenantVersion;
    Long projectId;
    String touchPoint;
    String title;
    String content;
    String originTitle;
    String originContent;
    String clientType;
    List<Message> history;
    BotLanguageEnum chatLanguage = BotLanguageEnum.ENGLISH;
    BotLanguageEnum retrieveLanguage = BotLanguageEnum.ENGLISH;
    Map<String, List<Object>> entities;
    Boolean excludeSub;
    List<TagDTO> tagList;
    Boolean userIntentPrecedence = true;
    Boolean isLivechatPath;
    // 如果传了该字段则不再进行summary
    MixedQuery mixedQuery;
    // 如果为false则只进行总结不进行意图识别
    Boolean needIntentIdentify = true;
}
