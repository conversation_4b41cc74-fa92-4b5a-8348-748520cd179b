package com.shulex.qabot.client.dto;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class AiAgentMessage {
    Role role;
    String content;
    String intent;
    Action action;
    JSONObject observation;
    int turn;


    public enum Role {
        USER,
        ASSISTANT,
        SYSTEM,
        AGENT,
    }

    @Data
    @Accessors(chain = true)
    public static class Action {
        ActionType action_type;
        String tool;
        JSONObject tool_input;
        JSONObject return_values;
        String log;
    }

    public enum ActionType {
        ASK_USER,
        CALL_TOOL,
        FINAL_ANSWER,
        UNKNOWN,
    }
}
