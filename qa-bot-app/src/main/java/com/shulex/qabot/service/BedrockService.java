package com.shulex.qabot.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.map.MapBuilder;
import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.shulex.gpt.apisdk.dto.LLMUsage;
import com.shulex.gpt.apisdk.dto.LLMUsages;
import com.shulex.gpt.apisdk.dto.RetrievedKu;
import com.shulex.qabot.client.dto.CohereRerankDTO;
import com.shulex.qabot.client.dto.esentity.DocumentChunkEsEntity;
import com.shulex.qabot.client.dto.esentity.ProductEsEntity;
import com.shulex.qabot.config.BedrockConfig;
import com.shulex.qabot.config.QaChatConfig;
import com.shulex.qabot.util.AsyncHttpUtil;
import com.shulex.qabot.util.ThreadUtil;
import lombok.Data;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials;
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider;
import software.amazon.awssdk.core.SdkBytes;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.bedrockagentruntime.BedrockAgentRuntimeClient;
import software.amazon.awssdk.services.bedrockagentruntime.model.*;
import software.amazon.awssdk.services.bedrockruntime.BedrockRuntimeAsyncClient;
import software.amazon.awssdk.services.bedrockruntime.BedrockRuntimeClient;
import software.amazon.awssdk.services.bedrockruntime.model.InvokeModelRequest;
import software.amazon.awssdk.services.bedrockruntime.model.InvokeModelResponse;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.stream.Collectors;

import static com.shulex.common.web.consts.EnvConst.isProd;

@Service
@Slf4j
public class BedrockService {
    List<BedrockRuntimeClient> bedrockRuntimeClients;
    List<BedrockRuntimeAsyncClient> bedrockRuntimeAsyncClients;
    List<BedrockAgentRuntimeClient> bedrockAgentRuntimeClients;

    public BedrockService(BedrockConfig bedrockConfig) {
        bedrockRuntimeClients = new ArrayList<>();
        bedrockRuntimeAsyncClients = new ArrayList<>();
        bedrockAgentRuntimeClients = new ArrayList<>();
        for (BedrockConfig.AkSkConfig akSkConfig : bedrockConfig.getAkSkConfigs()) {
            AwsBasicCredentials awsBasicCredentials = AwsBasicCredentials.create(akSkConfig.getAk(), akSkConfig.getSk());
            Region region = Region.of(akSkConfig.getRegion());
            bedrockRuntimeClients.add(BedrockRuntimeClient.builder()
                    .region(region)
                    .credentialsProvider(StaticCredentialsProvider.create(awsBasicCredentials))
                    .build());
            bedrockRuntimeAsyncClients.add(BedrockRuntimeAsyncClient.builder()
                    .region(region)
                    .credentialsProvider(StaticCredentialsProvider.create(awsBasicCredentials))
                    .build());
            bedrockAgentRuntimeClients.add(BedrockAgentRuntimeClient.builder()
                    .credentialsProvider(StaticCredentialsProvider.create(awsBasicCredentials))
                    .region(region)
                    .build()
            );
        }
    }

    public BedrockRuntimeClient getBedrockRuntimeClient() {
        return bedrockRuntimeClients.get(ThreadLocalRandom.current().nextInt(bedrockRuntimeClients.size()));
    }

    public BedrockRuntimeAsyncClient getBedrockRuntimeAsyncClient() {
        return bedrockRuntimeAsyncClients.get(ThreadLocalRandom.current().nextInt(bedrockRuntimeAsyncClients.size()));
    }

    public BedrockAgentRuntimeClient getBedrockAgentRuntimeClient() {
        return bedrockAgentRuntimeClients.get(ThreadLocalRandom.current().nextInt(bedrockAgentRuntimeClients.size()));
    }

    public List<BigDecimal> getCohereEmbedding(String text) {
        if (text == null) {
            text = "";
        }
        if (text.length() >= 2048) {
            text = text.substring(0, 2048);
        }
        JSONObject param = new JSONObject();
        param.put("texts", Collections.singletonList(text));
        param.put("input_type", "search_query");
        param.put("truncate", "END");
        InvokeModelRequest request = InvokeModelRequest.builder()
                .body(SdkBytes.fromUtf8String(param.toString()))
                .modelId("cohere.embed-multilingual-v3")
                .contentType("application/json")
                .accept("application/json")
                .build();
        InvokeModelResponse response = getBedrockRuntimeClient().invokeModel(request);
        JSONObject responseBody = JSON.parseObject(response.body().asUtf8String());
        List<BigDecimal> embeddings = responseBody.getJSONArray("embeddings").getJSONArray(0).toJavaList(BigDecimal.class);
        return embeddings;
    }

    public CompletableFuture<List<BigDecimal>> getCohereEmbeddingAsync(String text) {
        if (text == null) {
            text = "";
        }
        if (text.length() >= 2048) {
            text = text.substring(0, 2048);
        }
        JSONObject param = new JSONObject();
        param.put("texts", Collections.singletonList(text));
        param.put("input_type", "search_query");
        param.put("truncate", "END");
        InvokeModelRequest request = InvokeModelRequest.builder()
                .body(SdkBytes.fromUtf8String(param.toString()))
                .modelId("cohere.embed-multilingual-v3")
                .contentType("application/json")
                .accept("application/json")
                .build();
        return getBedrockRuntimeAsyncClient().invokeModel(request).thenApply(response -> {
            JSONObject responseBody = JSON.parseObject(response.body().asUtf8String());
            List<BigDecimal> embeddings = responseBody.getJSONArray("embeddings").getJSONArray(0).toJavaList(BigDecimal.class);
            return embeddings;
        });
    }

    public CompletableFuture<List<CohereEmbeddingResult>> getCohereEmbeddingsAsync(List<String> textList) {
        if (CollectionUtil.isEmpty(textList)) {
            return CompletableFuture.completedFuture(new ArrayList<>());
        }
        int textMaxLength = 2048;
        int textListMaxSize = 128;
        List<CompletableFuture<List<CohereEmbeddingResult>>> futures = new ArrayList<>();
        for (List<String> textListPartition : ListUtil.partition(textList, textListMaxSize)) {
            List<String> limitedTextList = textListPartition.stream().map(text -> {
                if (text.length() >= textMaxLength) {
                    return text.substring(0, textMaxLength);
                }
                return text;
            }).collect(Collectors.toList());
            JSONObject param = new JSONObject();
            param.put("texts", limitedTextList);
            param.put("input_type", "search_query");
            param.put("truncate", "END");
            InvokeModelRequest request = InvokeModelRequest.builder()
                    .body(SdkBytes.fromUtf8String(param.toString()))
                    .modelId("cohere.embed-multilingual-v3")
                    .contentType("application/json")
                    .accept("application/json")
                    .build();
            CompletableFuture<List<CohereEmbeddingResult>> future = getBedrockRuntimeAsyncClient().invokeModel(request).thenApply(response -> {
                JSONObject responseBody = JSON.parseObject(response.body().asUtf8String());
                JSONArray embeddings = responseBody.getJSONArray("embeddings");
                List<CohereEmbeddingResult> cohereEmbeddingResults = new ArrayList<>();
                for (int i = 0; i < embeddings.size(); i++) {
                    List<BigDecimal> embedding = embeddings.getJSONArray(i).toJavaList(BigDecimal.class);
                    String text = textListPartition.get(i);
                    String limitedText = limitedTextList.get(i);
                    CohereEmbeddingResult cohereEmbeddingResult = new CohereEmbeddingResult()
                            .setText(text)
                            .setLimitedText(limitedText)
                            .setEmbedding(embedding);
                    cohereEmbeddingResults.add(cohereEmbeddingResult);
                }
                return cohereEmbeddingResults;
            });
            futures.add(future);
        }
        return CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
                .thenApply(v -> futures.stream()
                        .flatMap(f -> f.join().stream())
                        .collect(Collectors.toList())
                );
    }

    @Data
    @Accessors(chain = true)
    public static class CohereEmbeddingResult {
        String text;
        String limitedText;
        List<BigDecimal> embedding;
    }

    public static Double cos(List<BigDecimal> embedding1, List<BigDecimal> embedding2) {
        double score = 0;
        for (int i = 0; i < embedding1.size(); i++) {
            double multi = embedding1.get(i).multiply(embedding2.get(i)).doubleValue();
            score += multi;
        }
        return score;
    }

    public List<KnowledgeService.KnowledgeSortResult> rerankKnowledge(String chatId, QaChatConfig.KnowledgeSearchConfig knowledgeSearchConfig, String query, List<RetrievedKu> kuList, List<List<DocumentChunkEsEntity>> chunkList, List<ProductEsEntity> productList, LLMUsages usages) {
        Map<Object, Object> params = MapBuilder.create().put("chatId", chatId).put("query", query).put("kuList", kuList).put("chunkList", chunkList).put("productList", productList).build();
        log.info("rerankKnowledge[{}]: rerankParam={}", chatId, JSON.toJSONString(params));
        int retry = 3;
        Exception exception = null;
        while (retry-- > 0) {
            try {
                int kuSize = CollectionUtil.isEmpty(kuList) ? 0 : kuList.size();
                int chunkSize = CollectionUtil.isEmpty(chunkList) ? 0 : chunkList.size();
                int productSize = CollectionUtil.isEmpty(productList) ? 0 : productList.size();
                if (kuSize + chunkSize + productSize == 0) {
                    return new ArrayList<>();
                }
                // 只有1个知识则不进行调用
                if (kuSize + chunkSize + productSize == 1) {
                    KnowledgeService.KnowledgeSortResult knowledgeSortResult = new KnowledgeService.KnowledgeSortResult().setRerankRank(1).setRerankScore(Double.MAX_VALUE);
                    if (kuSize == 1) {
                        knowledgeSortResult.setKu(kuList.get(0));
                    }
                    if (chunkSize == 1) {
                        knowledgeSortResult.setChunk(chunkList.get(0));
                    }
                    if (productSize == 1) {
                        knowledgeSortResult.setProduct(productList.get(0));
                    }
                    return ListUtil.toList(knowledgeSortResult);
                }
                List<String> contents = new ArrayList<>();
                final int rerankContentMaxLength = 32000;
                if (CollectionUtil.isNotEmpty(kuList)) {
                    for (RetrievedKu ku : kuList) {
                        String rerankContent = String.format("%s\n%s", ku.getTitle(), ku.getContent());
                        if (CollectionUtil.isNotEmpty(ku.getTags())) {
                            rerankContent = "Keywords:" + String.join(",", ku.getTags()) + "\n" + rerankContent;
                        }
                        if (rerankContent.length() > rerankContentMaxLength) {
                            rerankContent = rerankContent.substring(0, rerankContentMaxLength);
                        }
                        contents.add(rerankContent);
                    }
                }
                if (CollectionUtil.isNotEmpty(chunkList)) {
                    for (List<DocumentChunkEsEntity> docChunks : chunkList) {
                        String rerankContent = docChunks.stream().map(DocumentChunkEsEntity::getContent).collect(Collectors.joining("\n"));
                        if (!docChunks.isEmpty() && CollectionUtil.isNotEmpty(docChunks.get(0).getTags())) {
                            rerankContent = "Keywords:" + String.join(",", docChunks.get(0).getTags()) + "\n" + rerankContent;
                        }
                        if (rerankContent.length() > rerankContentMaxLength) {
                            rerankContent = rerankContent.substring(0, rerankContentMaxLength);
                        }
                        contents.add(rerankContent);
                    }
                }
                if (CollectionUtil.isNotEmpty(productList)) {
                    for (ProductEsEntity product : productList) {
                        String rerankContent = String.format("%s\n%s", product.getTitle(), product.getDescribe());
                        if (CollectionUtil.isNotEmpty(product.getSelfTags())) {
                            rerankContent = "Keywords:" + String.join(",", product.getSelfTags()) + "\n" + rerankContent;
                        }
                        if (rerankContent.length() > rerankContentMaxLength) {
                            rerankContent = rerankContent.substring(0, rerankContentMaxLength);
                        }
                        contents.add(rerankContent);
                    }
                }
                BedrockAgentRuntimeClient client = getBedrockAgentRuntimeClient();
                RerankQuery rerankQuery = RerankQuery.builder()
                        .type(RerankQueryContentType.TEXT)
                        .textQuery(RerankTextDocument.builder().text(query.length() <= 9000 ? query : query.substring(0, 9000)).build())
                        .build();
                List<RerankSource> rerankSources = contents.stream().map(content -> RerankSource.builder()
                        .type(RerankSourceType.INLINE)
                        .inlineDocumentSource(RerankDocument.builder()
                                .type(RerankDocumentType.TEXT)
                                .textDocument(RerankTextDocument.builder()
                                        .text(content)
                                        .build())
                                .build())
                        .build()).collect(Collectors.toList());
                RerankRequest rerankRequest = RerankRequest.builder()
                        .queries(rerankQuery)
                        .sources(rerankSources)
                        .rerankingConfiguration(RerankingConfiguration.builder()
                                .type(RerankingConfigurationType.BEDROCK_RERANKING_MODEL)
                                .bedrockRerankingConfiguration(BedrockRerankingConfiguration.builder()
                                        .numberOfResults(rerankSources.size())
                                        .modelConfiguration(BedrockRerankingModelConfiguration.builder()
                                                .modelArn("arn:aws:bedrock:us-west-2::foundation-model/cohere.rerank-v3-5:0")
                                                .build())
                                        .build())
                                .build())
                        .build();
                RerankResponse rerankResponse = client.rerank(rerankRequest);
                if (usages != null) {
                    usages.merge(LLMUsages.of("cohere-rerank", new LLMUsage(0, 0, 1)));
                }
                List<RerankResult> rerankResults = rerankResponse.results();
                List<KnowledgeService.KnowledgeSortResult> knowledgeSortResults = new ArrayList<>();
                int rank = 1;
                for (RerankResult rerankResult : rerankResults) {
                    Double relevanceScore = Double.valueOf(rerankResult.relevanceScore());
                    Integer index = rerankResult.index();
                    KnowledgeService.KnowledgeSortResult knowledgeSortResult = new KnowledgeService.KnowledgeSortResult()
                            .setRerankRank(rank++)
                            .setRerankScore(relevanceScore);
                    if (index < kuSize) {
                        knowledgeSortResult.setKu(kuList.get(index));
                    } else if (index < kuSize + chunkSize) {
                        knowledgeSortResult.setChunk(chunkList.get(index - kuSize));
                    } else if (index < kuSize + chunkSize + productSize) {
                        knowledgeSortResult.setProduct(productList.get(index - kuSize - chunkSize));
                    }
                    knowledgeSortResults.add(knowledgeSortResult);
                }
                log.info("rerankKnowledge[{}]: rerankResults={}, knowledgeSortResults={}, rerankQuery={}, rerankSources={}", chatId, rerankResults, JSON.toJSONString(knowledgeSortResults), rerankQuery, rerankSources);
                return knowledgeSortResults;
            } catch (Exception e) {
                log.warn("rerank fail", e);
                exception = e;
            }
        }
        throw new RuntimeException(exception);
    }
}
