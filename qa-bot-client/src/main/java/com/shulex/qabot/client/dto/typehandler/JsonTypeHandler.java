package com.shulex.qabot.client.dto.typehandler;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.extension.handlers.AbstractJsonTypeHandler;

public abstract class JsonTypeHandler<T> extends AbstractJsonTypeHandler<T> {
    protected TypeReference<T> type;

    @Override
    protected T parse(String json) {
        return json == null ? null : JSON.parseObject(json, type);
    }

    @Override
    protected String toJson(T obj) {
        return obj == null ? null : JSON.toJSONString(obj);
    }

    public static String getTypeHandlerMapping(Class<?> a) {
        return "typeHandler=" + a.getName();
    }
}
