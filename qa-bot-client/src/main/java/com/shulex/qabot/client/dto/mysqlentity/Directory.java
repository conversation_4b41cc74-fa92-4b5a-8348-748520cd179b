package com.shulex.qabot.client.dto.mysqlentity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.shulex.gpt.apisdk.enums.DirectoryTypeEnum;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@TableName("directory")
public class Directory {
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    private String name;
    private DirectoryTypeEnum type;
    private String metadata;
    private Long tenantId;
    private Long projectId;
    private Long parentId;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
}
