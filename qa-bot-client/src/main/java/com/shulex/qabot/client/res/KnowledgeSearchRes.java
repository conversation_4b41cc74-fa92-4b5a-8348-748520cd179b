package com.shulex.qabot.client.res;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Pair;
import com.shulex.gpt.apisdk.dto.GetDocumentResponse;
import com.shulex.gpt.apisdk.dto.LLMUsages;
import com.shulex.gpt.apisdk.dto.RetrievedKu;
import com.shulex.qabot.client.dto.EsSearchResult;
import com.shulex.qabot.client.dto.esentity.DocumentChunkEsEntity;
import com.shulex.qabot.client.dto.esentity.ProductEsEntity;
import com.shulex.qabot.client.req.KnowledgeSearchReq;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
@Accessors(chain = true)
public class KnowledgeSearchRes {
    String highSimilarityKuResponse;
    String contextContent;
    String systemContent;
    LLMUsages usages;
    // search context
    Boolean useQa;
    Boolean useDoc;
    Boolean useProduct;
    QaSearchContext qaSearchContext;
    DocSearchContext docSearchContext;
    ProductSearchContext productSearchContext;

    @Data
    @Accessors(chain = true)
    public static class QaSearchContext {
        Double retrieveMinScore;
        Double relevantMinScore;
        Integer relevantKusCount;
        Double relevantAvgScore;
        Integer usedKusCount;
        Double usedKusRelevantAvgScore;
        List<RetrievedKu> usedKus;
        List<RetrievedKu> rerankKus;
    }

    @Data
    @Accessors(chain = true)
    public static class DocSearchContext {
        List<EsSearchResult<DocumentChunkEsEntity>> esResults;
        List<EsSearchResult<List<DocumentChunkEsEntity>>> rerankResults;
        List<EsSearchResult<List<DocumentChunkEsEntity>>> relevanceResults;
        List<List<DocumentChunkEsEntity>> usedChunks;
        List<GetDocumentResponse> usedDocuments;
    }

    @Data
    @Accessors(chain = true)
    public static class ProductSearchContext {
        KnowledgeSearchReq.ProductSearchParam productSearchParam;
        Map<String, Pair<List<EsSearchResult<ProductEsEntity>>, List<EsSearchResult<ProductEsEntity>>>> esResults;
        Map<String, List<EsSearchResult<ProductEsEntity>>> rerankResults;
        Map<String, List<EsSearchResult<ProductEsEntity>>> relevanceResults;
        List<ProductEsEntity> usedProducts;

        public ProductSearchContext addEsResults(Map<String, Pair<List<EsSearchResult<ProductEsEntity>>, List<EsSearchResult<ProductEsEntity>>>> esSearchProducts) {
            if (CollectionUtil.isNotEmpty(esSearchProducts)) {
                if (this.esResults == null) {
                    this.esResults = new HashMap<>();
                }
                this.esResults.putAll(esSearchProducts);
            }
            return this;
        }
    }
}
