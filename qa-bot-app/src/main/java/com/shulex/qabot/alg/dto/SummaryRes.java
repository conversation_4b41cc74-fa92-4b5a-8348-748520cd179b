package com.shulex.qabot.alg.dto;

import com.shulex.gpt.apisdk.dto.LLMUsage;
import com.shulex.gpt.apisdk.dto.LLMUsages;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Map;

@Data
@Accessors(chain = true)
public class SummaryRes {
    Result result;
    LLMUsages usages;

    @Data
    @Accessors(chain = true)
    public static class Result {
        private String userQuery;
        private String userEnQuery;
        private String knowledgeQuery;
        private String knowledgeEnQuery;
    }
}
