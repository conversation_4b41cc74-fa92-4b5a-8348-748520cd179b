{
    "_source": {
        "excludes": ${excludeFields}
    },
    "size": ${size},
    "query": {
        "script_score": {
            "query": {
                "bool": {
                    "filter": [
                        {
                            "term": {
                                "tenantId": {
                                    "value": ${tenantId}
                                }
                            }
                        },
                        {
                            "term": {
                                "projectId": {
                                    "value": ${projectId}
                                }
                            }
                        }
                        <#if minSkuPrice?? || maxSkuPrice??>
                        ,
                        {
                            "nested": {
                                "path": "skuList",
                                "query": {
                                    "bool": {
                                        "filter": [
                                            {
                                                "range": {
                                                    "skuList.price": {
                                                        <#if minSkuPrice??>
                                                        "gte": ${minSkuPrice},
                                                        </#if>
                                                        <#if maxSkuPrice??>
                                                        "lte": ${maxSkuPrice}
                                                        </#if>
                                                    }
                                                }
                                            }
                                        ]
                                    }
                                }
                            }
                        }
                        </#if>
                    ],
                    "should": [
                        <#if matchShouldList??>
                        <#list matchShouldList as matchShould>
                                {
                                    "match": {
                                        "${matchShould.field}": {
                                            "query": "${matchShould.value}"
                                        }
                                    }
                                }
                                <#if matchShould_has_next>
                                    ,
                                </#if>
                        </#list>
                        </#if>
                    ]
                    <#if hasShould?? && hasShould>
                    ,
                    "minimum_should_match": 1
                    </#if>
                }
            },
            "script": {
                "source": "knn_score",
                "lang": "knn",
                "params": {
                    "space_type": "cosinesimil",

                    <#if productDetailEmbedding??>
                    "field": "productDetailEmbedding",
                    "query_value": ${productDetailEmbedding}
                    </#if>

                    <#if titleEmbedding??>
                    "field": "titleEmbedding",
                    "query_value": ${titleEmbedding}
                    </#if>

                    <#if describeEmbedding??>
                    "field": "describeEmbedding",
                    "query_value": ${describeEmbedding}
                    </#if>

                    <#if chunkContentEmbedding??>
                    "field": "chunkContentEmbedding",
                    "query_value": ${chunkContentEmbedding}
                    </#if>
                }
            }
        }
    }
}