package com.shulex.qabot.controller;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.shulex.common.user.token.Authed;
import com.shulex.gpt.apisdk.dto.MixedQuery;
import com.shulex.qabot.client.dto.IntentSampleDTO;
import com.shulex.qabot.client.dto.esentity.ProductChunkEsEntity;
import com.shulex.qabot.client.dto.esentity.ProductEsEntity;
import com.shulex.qabot.client.dto.mysqlentity.Directory;
import com.shulex.qabot.client.dto.mysqlentity.Ku;
import com.shulex.qabot.client.dto.mysqlentity.KuProduct;
import com.shulex.qabot.client.dto.mysqlentity.ProductChunk;
import com.shulex.qabot.es.EsService;
import com.shulex.qabot.mysql.repository.DirectoryRepository;
import com.shulex.qabot.mysql.repository.KuProductRepository;
import com.shulex.qabot.mysql.repository.KuRepository;
import com.shulex.qabot.mysql.repository.ProductChunkRepository;
import com.shulex.qabot.service.DirectoryService;
import com.shulex.qabot.service.IntentService;
import com.shulex.qabot.service.KuService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

@ApiIgnore
@RestController
public class TmpTaskController {
    @Autowired
    KuRepository kuRepository;
    @Autowired
    DirectoryService directoryService;
    @Autowired
    KuService kuService;
    @Autowired
    DirectoryRepository directoryRepository;
    @Autowired
    IntentService intentService;
    @Autowired
    KuProductRepository kuProductRepository;
    @Autowired
    ProductChunkRepository productChunkRepository;
    @Autowired
    EsService esService;

    @Authed(allowInner = true)
    @PostMapping("/importKuToEs")
    public int importKuToEs(@RequestBody List<Long> kuIds, @RequestParam(required = false) Integer threadPoolSize, @RequestParam(required = false) Boolean notInsertIfExist) {
        if (notInsertIfExist == null) {
            notInsertIfExist = false;
        }
        List<Ku> kuList = kuRepository.listByIds(kuIds);
        kuService.insertKuToEs(kuList, threadPoolSize, notInsertIfExist);
        return kuList.size();
    }

    @Authed(allowInner = true)
    @PostMapping("/importDirectoryToEs")
    public int importKuToEs(@RequestBody List<Long> directoryIds) {
        List<Directory> directories = directoryRepository.listByIds(directoryIds);
        directoryService.insertDirectoryToEs(directories);
        return directories.size();
    }

    @Authed(allowInner = true)
    @PostMapping("/manualInsertSample")
    public Object manualInsertSample(@RequestParam long tenantId, @RequestParam long projectId, @RequestParam long intentId, @RequestParam String intentName, @RequestBody List<String> sampleTexts) {
        List<IntentSampleDTO> intentSampleDTOList = sampleTexts.stream().map(sampleText -> {
            MixedQuery mixedQuery = new MixedQuery();
            mixedQuery.setUserEnQuery(sampleText);
            return new IntentSampleDTO()
                    .setChatId("manualInsertSample:" + UUID.randomUUID())
                    .setTenantId(tenantId)
                    .setProjectId(projectId)
                    .setFirstTurnTitle(null)
                    .setFirstTurnContent(sampleText)
                    .setFirstTurnReply(null)
                    .setFirstTurnIntentId(null)
                    .setFirstTurnIntentName(null)
                    .setFirstTurnMixedQuery(mixedQuery)
                    .setFistTurnEntities(null)
                    .setIntentId(intentId)
                    .setIntentName(intentName)
                    .setPnLabel(true);
        }).collect(Collectors.toList());
        intentService.saveIntentSample(intentSampleDTOList);
        return intentSampleDTOList.stream().map(IntentSampleDTO::getChatId).collect(Collectors.toList());
    }

    @Authed(allowInner = true)
    @PostMapping("/reloadKuProductSkuList")
    public Object reloadKuProductSkuList(@RequestBody List<Long> kuProductIds) {
        if (CollectionUtil.isNotEmpty(kuProductIds)) {
            List<KuProduct> kuProducts = kuProductRepository.listByIds(kuProductIds);
            Map<Long, KuProduct> kuProductMap = kuProducts.stream().collect(Collectors.toMap(e -> e.getId(), e -> e));
            Map<String, String> productId2JsonData = kuProducts.stream().map(kuProduct -> new ProductEsEntity().setId(kuProduct.getId().toString())
                    .setSkuList(ProductEsEntity.convertFromKuProductSku(kuProduct.getSkuList()))).collect(Collectors.toMap(e -> e.getId(), e -> JSON.toJSONString(e)));
            List<ProductChunk> productChunks = productChunkRepository.lambdaQuery().in(ProductChunk::getKuProductId, kuProductIds).select(ProductChunk::getId, ProductChunk::getKuProductId).list();
            Map<String, String> chunkId2JsonData = productChunks.stream().map(productChunk -> new ProductChunkEsEntity().setId(productChunk.getId().toString()).setSkuList(ProductEsEntity.convertFromKuProductSku(kuProductMap.get(productChunk.getKuProductId()).getSkuList())))
                    .collect(Collectors.toMap(e -> e.getId(), e -> JSON.toJSONString(e)));
            esService.update(ProductEsEntity.indexName, productId2JsonData, true);
            esService.update(ProductChunkEsEntity.indexName, chunkId2JsonData, true);
        }
        return "ok";
    }
}
