{
    "_source": {
        "excludes": [
            "productDetailEmbedding",
            "titleEmbedding",
            "describeEmbedding"
        ]
    },
    <#if from??>
    "from": ${from},
    </#if>
    <#if size??>
    "size": ${size},
    </#if>
    "query": {
        "bool": {
            "filter": [
                <#if tenantId??>
                {
                     "term": {
                        "tenantId": ${tenantId}
                     }
                },
                </#if>
                <#if projectId??>
                {
                     "term": {
                         "projectId": ${projectId}
                     }
                },
                </#if>
                <#if productIds??>
                {
                    "terms": {
                        "productId.keyword": ${productIds}
                    }
                },
                </#if>
                <#if documentId??>
                {
                    "term": {
                        "documentId": ${documentId}
                    }
                },
                </#if>
                <#if enable??>
                {
                    "term": {
                        "enable": ${enable}
                    }
                },
                </#if>
                <#if minSkuPrice?? || maxSkuPrice??>
                {
                    "nested": {
                         "path": "skuList",
                         "query": {
                              "bool": {
                                   "filter": [
                                            {
                                                "range": {
                                                    "skuList.price": {
                                                        <#if minSkuPrice??>
                                                        "gte": ${minSkuPrice},
                                                        </#if>
                                                        <#if maxSkuPrice??>
                                                        "lte": ${maxSkuPrice}
                                                        </#if>
                                                    }
                                                }
                                            }
                                   ]
                              }
                         }
                    }
                },
                </#if>
                {
                    "match_all": {}
                }
            ],
            "should": [
                    <#if matchShouldList??>
                    <#list matchShouldList as matchShould>
                                {
                                    "match": {
                                        "${matchShould.field}": {
                                            "query": "${matchShould.value}"
                                        }
                                    }
                                }
                                <#if matchShould_has_next>
                                    ,
                                </#if>
                    </#list>
                    </#if>
            ]
            <#if hasShould?? && hasShould>
            ,
            "minimum_should_match": 1
            </#if>
        }
    }
}