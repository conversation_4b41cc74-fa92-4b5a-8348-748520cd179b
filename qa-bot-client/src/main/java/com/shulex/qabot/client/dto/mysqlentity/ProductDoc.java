package com.shulex.qabot.client.dto.mysqlentity;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.shulex.gpt.apisdk.dto.IMetadataObj;
import com.shulex.gpt.apisdk.enums.ProductDocStatusEnum;
import com.shulex.gpt.apisdk.enums.ProductInfoSourceEnum;
import lombok.Data;
import lombok.ToString;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

@Data
@Accessors(chain = true)
@TableName(value = "product_doc", autoResultMap = true)
public class ProductDoc {
    private Long id;
    private String source;
    private String uri;
    private String status;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    private Long tenantId;
    private Long projectId;
    private Boolean disabled;
    private Long createdBy;
    private Long updatedBy;
    private String metadata;
    private String name;
    private LocalDateTime productUpdateAt;
    private String innerStatus;
    private Integer counter;

    @Data
    public static class Metadata {
        private String shopifyProducts;
        private String shopifyCollections;
        private String shopifyProductCollectionRelation;
        private String shopifyMeta;
        private String dataUrl;
        private Long directoryId;
        private Map<String, List<String>> conditionTag;
        private Boolean isVisible;
        private Boolean published;
        public List<String> tags;
        public Map<String, List<String>> specialTagMap;
    }

    public Class<Metadata> getMetadataClass() {
        return Metadata.class;
    }

    public Metadata getDefaultMetadataObj() {
        return new Metadata();
    }
}
