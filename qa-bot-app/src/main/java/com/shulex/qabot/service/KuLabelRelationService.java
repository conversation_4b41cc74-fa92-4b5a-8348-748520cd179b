package com.shulex.qabot.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.func.LambdaUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.otter.canal.protocol.CanalEntry;
import com.shulex.gpt.apisdk.enums.KuTypeEnum;
import com.shulex.qabot.client.dto.esentity.DocumentChunkEsEntity;
import com.shulex.qabot.client.dto.esentity.ProductChunkEsEntity;
import com.shulex.qabot.client.dto.esentity.ProductEsEntity;
import com.shulex.qabot.client.dto.mysqlentity.*;
import com.shulex.qabot.config.GeneralConfig;
import com.shulex.qabot.es.EsService;
import com.shulex.qabot.client.dto.esentity.KuEsEntity;
import com.shulex.qabot.mysql.repository.*;
import lombok.SneakyThrows;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class KuLabelRelationService {
    private static final Logger log = LoggerFactory.getLogger(KuLabelRelationService.class);
    @Autowired
    EsService esService;
    @Autowired
    KuRepository kuRepository;
    @Autowired
    KuLabelRelationRepository kuLabelRelationRepository;
    @Autowired
    GeneralConfig generalConfig;
    @Autowired
    DocumentChunkRepository documentChunkRepository;
    @Autowired
    private ProductChunkRepository productChunkRepository;
    @Autowired
    private KuProductRepository kuProductRepository;

    public enum KuLabelRelationToEsResult {
        SUCCESS_UPDATE_KU_IN_ES,
        NOT_FOUND_KU_IN_ES_BUT_IN_MYSQL,
        NOT_FOUND_KU_IN_ES_AND_MYSQL,
    }

    @SneakyThrows
    public KuLabelRelationToEsResult retryUpdateKuLabelRelationToEs(KuTypeEnum kuType, Long kuId, CanalEntry.EventType updateType) {
        int retryCount = 0;
        Exception updateException = null;
        Integer maxRetry = generalConfig.getUpdateMaxRetry();
        if (maxRetry == null || maxRetry < 1 || maxRetry > 10) {
            maxRetry = 3;
        }
        while (++retryCount < maxRetry) {
            try {
                return updateKuLabelRelationToEs(kuType, kuId, updateType);
            } catch (Exception e) {
                updateException = e;
                Thread.sleep(1000L);
            }
        }
        throw new RuntimeException(updateException);
    }

    public KuLabelRelationToEsResult updateKuLabelRelationToEs(KuTypeEnum kuType, Long kuId, CanalEntry.EventType updateType) {
        if (KuTypeEnum.QA.equals(kuType)) {
            // find ku in es
            KuEsEntity kuEsEntity = esService.getById(KuEsEntity.indexName, kuId + "", KuEsEntity.class, EsService.getEmbeddingFieldNames(KuEsEntity.class));
            if (kuEsEntity != null) {
                List<KuLabelRelation> allRelations = kuLabelRelationRepository.getRelations(kuEsEntity.getTenantId(), kuEsEntity.getProjectId(), kuType, Arrays.asList(kuId), null);
                Set<Long> allKuLabelValueIds = allRelations.stream().map(KuLabelRelation::getKuLabelValueId).collect(Collectors.toSet());
                KuEsEntity updateKuEsEntity = new KuEsEntity();
                updateKuEsEntity.setKuLabelValueIds(allKuLabelValueIds);
                Map<String, String> id2updateJsonData = new HashMap<>();
                id2updateJsonData.put(kuId + "", JSON.toJSONString(updateKuEsEntity));
                esService.update(KuEsEntity.indexName, id2updateJsonData, false);
                return KuLabelRelationToEsResult.SUCCESS_UPDATE_KU_IN_ES;
            }
            // check exist in mysql
            Ku ku = kuRepository.getById(kuId);
            if (ku != null) {
                return KuLabelRelationToEsResult.NOT_FOUND_KU_IN_ES_BUT_IN_MYSQL;
            } else {
                return KuLabelRelationToEsResult.NOT_FOUND_KU_IN_ES_AND_MYSQL;
            }
        } else if (KuTypeEnum.TC.equals(kuType)) {
            // find all chunks in mysql
            List<DocumentChunk> chunksInMysql = documentChunkRepository.getChunks(kuId, DocumentChunk::getId, DocumentChunk::getIndexed, DocumentChunk::getTenantId, DocumentChunk::getProjectId);
            if (CollectionUtil.isEmpty(chunksInMysql)) {
                return KuLabelRelationToEsResult.SUCCESS_UPDATE_KU_IN_ES;
            }
            // check if all chunks indexed succeed
            for (DocumentChunk chunk : chunksInMysql) {
                if (!Boolean.TRUE.equals(chunk.getIndexed())) {
                    return KuLabelRelationToEsResult.NOT_FOUND_KU_IN_ES_BUT_IN_MYSQL;
                }
            }
            List<String> chunkIds = chunksInMysql.stream().map(e -> e.getId().toString()).collect(Collectors.toList());
            // find all chunks in es
            List<String> excludeEsField = Arrays.asList(LambdaUtil.getFieldName(DocumentChunkEsEntity::getContent), LambdaUtil.getFieldName(DocumentChunkEsEntity::getContentEmbedding));
            Map<String, DocumentChunkEsEntity> chunksInEs = esService.getByIds(DocumentChunkEsEntity.indexName, chunkIds, DocumentChunkEsEntity.class, excludeEsField);
            // check if all chunks saved in es
            for (DocumentChunk chunk : chunksInMysql) {
                if (!chunksInEs.containsKey(chunk.getId().toString())) {
                    return KuLabelRelationToEsResult.NOT_FOUND_KU_IN_ES_BUT_IN_MYSQL;
                }
            }
            // search all relation
            List<KuLabelRelation> allRelations = kuLabelRelationRepository.getRelations(chunksInMysql.get(0).getTenantId(), chunksInMysql.get(0).getProjectId(), kuType, Arrays.asList(kuId), null);
            Set<Long> allKuLabelValueIds = allRelations.stream().map(KuLabelRelation::getKuLabelValueId).collect(Collectors.toSet());
            // update es
            Map<String, String> id2updateJsonData = new HashMap<>();
            chunksInEs.forEach((id, chunkEsEntity) -> {
                DocumentChunkEsEntity updateEntity = new DocumentChunkEsEntity().setKuLabelValueIds(allKuLabelValueIds);
                id2updateJsonData.put(id, JSON.toJSONString(updateEntity));
            });
            esService.update(DocumentChunkEsEntity.indexName, id2updateJsonData, false);
            return KuLabelRelationToEsResult.SUCCESS_UPDATE_KU_IN_ES;
        } else if (KuTypeEnum.PRODUCT.equals(kuType)) {
            List<ProductChunk> chunksInMysql = productChunkRepository.getChunks(kuId, ProductChunk::getId, ProductChunk::getIndexed, ProductChunk::getTenantId, ProductChunk::getProjectId);
            if (CollectionUtil.isEmpty(chunksInMysql)) {
                return KuLabelRelationToEsResult.SUCCESS_UPDATE_KU_IN_ES;
            }
            for (ProductChunk chunk : chunksInMysql) {
                if (!Boolean.TRUE.equals(chunk.getIndexed())) {
                    return KuLabelRelationToEsResult.NOT_FOUND_KU_IN_ES_BUT_IN_MYSQL;
                }
            }
            List<String> chunkIds = chunksInMysql.stream().map(e -> e.getId().toString()).collect(Collectors.toList());
            List<String> excludeEsField = Arrays.asList(LambdaUtil.getFieldName(ProductChunkEsEntity::getContent), LambdaUtil.getFieldName(ProductChunkEsEntity::getChunkContentEmbedding));
            Map<String, ProductChunkEsEntity> chunksInEs = esService.getByIds(ProductChunkEsEntity.indexName, chunkIds, ProductChunkEsEntity.class, excludeEsField);
            for (ProductChunk chunk : chunksInMysql) {
                if (!chunksInEs.containsKey(chunk.getId().toString())) {
                    return KuLabelRelationToEsResult.NOT_FOUND_KU_IN_ES_BUT_IN_MYSQL;
                }
            }

            List<KuLabelRelation> allRelations = kuLabelRelationRepository.getRelations(chunksInMysql.get(0).getTenantId(), chunksInMysql.get(0).getProjectId(), kuType, Arrays.asList(kuId), null);
            Set<Long> allKuLabelValueIds = allRelations.stream().map(KuLabelRelation::getKuLabelValueId).collect(Collectors.toSet());
            // update es
            Map<String, String> id2updateJsonData = new HashMap<>();
            chunksInEs.forEach((id, chunkEsEntity) -> {
                ProductChunkEsEntity updateEntity = new ProductChunkEsEntity().setKuLabelValueIds(allKuLabelValueIds);
                id2updateJsonData.put(id, JSON.toJSONString(updateEntity));
            });
            esService.update(ProductChunkEsEntity.indexName, id2updateJsonData, false);
            updateKuProduct(kuId);
            return KuLabelRelationToEsResult.SUCCESS_UPDATE_KU_IN_ES;
        } else {
            throw new RuntimeException("无法处理的KuTypeEnum: kuType=" + kuType);
        }
    }

    private void updateKuProduct(Long productId) {
        ProductEsEntity productEsEntity = esService.getById(ProductEsEntity.indexName, productId + "", ProductEsEntity.class, EsService.getEmbeddingFieldNames(ProductEsEntity.class));
        if (productEsEntity != null) {
            List<KuLabelRelation> allRelations = kuLabelRelationRepository.getRelations(productEsEntity.getTenantId(), productEsEntity.getProjectId(), KuTypeEnum.PRODUCT, Arrays.asList(productId), null);
            Set<Long> allKuLabelValueIds = allRelations.stream().map(KuLabelRelation::getKuLabelValueId).collect(Collectors.toSet());
            ProductEsEntity updateProductEsEntity = new ProductEsEntity();
            updateProductEsEntity.setKuLabelValueIds(allKuLabelValueIds);
            Map<String, String> id2updateJsonData = new HashMap<>();
            id2updateJsonData.put(productId + "", JSON.toJSONString(updateProductEsEntity));
            esService.update(ProductEsEntity.indexName, id2updateJsonData, false);
        }
    }
}
