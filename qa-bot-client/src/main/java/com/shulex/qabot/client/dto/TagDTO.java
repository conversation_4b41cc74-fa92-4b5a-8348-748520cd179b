package com.shulex.qabot.client.dto;

import cn.hutool.core.collection.CollectionUtil;
import com.shulex.gpt.apisdk.dto.Intent;
import com.shulex.gpt.apisdk.enums.TagSourceEnum;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.commons.jexl3.*;

import java.util.*;
import java.util.stream.Collectors;

import static java.lang.String.format;

@Data
@Accessors(chain = true)
public class TagDTO {
    Long tagId;
    String name;
    String description;
    String intentAiName;
    TagSourceEnum source;
    Boolean isFirstLevelTag;
    String keywordJexl;
    List<TagDTO> subTags;


    public static String generateKeywordJexlWithOrOperation(List<String> keywords) {
        String keywordJexl = null;
        if (CollectionUtil.isNotEmpty(keywords)) {
            keywords = keywords.stream().filter(Objects::nonNull).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(keywords)) {
                keywordJexl = keywords.stream().map(keyword -> format("userQuestion.toLowerCase().contains(\"%s\".toLowerCase())", keyword)).collect(Collectors.joining("||"));
            }
        }
        return keywordJexl;
    }
}
