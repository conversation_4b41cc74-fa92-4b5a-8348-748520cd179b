package com.shulex.qabot.client.dto.esentity;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.shulex.gpt.apisdk.dto.GetKuLabelResponse;
import com.shulex.gpt.apisdk.dto.UserInfo;
import com.shulex.qabot.client.dto.esentity.EsEmbeddingField;
import com.shulex.qabot.client.dto.mysqlentity.KuProduct;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Data
@Accessors(chain = true)
@NoArgsConstructor
public class ProductEsEntity {
    public static final String indexName = "ku-product";

    String id;
    Long tenantId;
    Long projectId;
    Boolean mainPush;
    Long documentId;
    Boolean enable;
    String productId;
    String handle;
    String title;
    String describe;
    String vendor;
    String category;
    LocalDateTime publishedAt;
    Boolean requiresShipping;
    List<String> tags;
    List<String> images;
    String source;
    String platform;
    String country;
    String currency;
    String spuUrl;
    List<ProductEsEntity.Sku> skuList;
    List<String> selfTags; // 加权tag
    Set<Long> kuLabelValueIds;
    String brief;
    @EsEmbeddingField
    List<BigDecimal> productDetailEmbedding;
    @EsEmbeddingField
    List<BigDecimal> titleEmbedding;
    @EsEmbeddingField
    List<BigDecimal> describeEmbedding;
    Long directoryId;
    Long knowledgeId;
    Boolean isOnline;
    Boolean isVisible;

    // GPT 服务用
    List<GetKuLabelResponse> kuLabelResponses;
    UserInfo createdBy;
    Long createdAt;

    String brand;
    String storeName;

    @Data
    @Accessors(chain = true)
    public static class Sku {
        String id;
        String productId;
        String title;
        String describe;
        BigDecimal price;
        BigDecimal compareAtPrice;
        String sku;
        String detailJson;
        String aboutJson;
        String spuUrl;
        String brand;
        String category;
        LocalDateTime publishedAt;
        Integer state;
        String country;
        String image;
        LocalDateTime createdAt;
        Long grams;
        Long inventoryQuantity;
        List<SkuOpt> opts;
    }

    @Data
    @Accessors(chain = true)
    public static class SkuOpt {
        String optName;
        String optValue;
    }

    public ProductEsEntity(KuProduct kuProduct) {
        this.setId(kuProduct.getId().toString())
                .setTenantId(kuProduct.getTenantId())
                .setProjectId(kuProduct.getProjectId())
                .setMainPush(kuProduct.getMainPush())
                .setDocumentId(kuProduct.getDocumentId())
                .setEnable(kuProduct.getEnable())
                .setProductId(kuProduct.getProductId())
                .setHandle(kuProduct.getHandle())
                .setTitle(kuProduct.getTitle())
                .setDescribe(kuProduct.getDescribe())
                .setVendor(kuProduct.getVendor())
                .setCategory(kuProduct.getCategory())
                .setPublishedAt(kuProduct.getPublishedAt())
                .setRequiresShipping(kuProduct.getRequiresShipping())
                .setTags(kuProduct.getTags())
                .setImages(kuProduct.getImages())
                .setSource(kuProduct.getSource())
                .setPlatform(kuProduct.getPlatform() == null ? null : kuProduct.getPlatform().name())
                .setCountry(kuProduct.getCountry())
                .setCurrency(kuProduct.getCurrency())
                .setDirectoryId(kuProduct.getDirectoryId())
//                .setSkuList(kuProduct.getSkuList())
                .setSkuList(convertFromKuProductSku(kuProduct.getSkuList()))
                .setIsVisible(kuProduct.getIsVisible())
                .setSpuUrl(kuProduct.getSpuUrl());
    }

    public static List<Sku> convertFromKuProductSku(List<KuProduct.Sku> skuList) {
        if (CollectionUtil.isNotEmpty(skuList)) {
            List<Sku> skus = skuList.stream().map(e -> {
                Sku sku = new Sku();
                BeanUtil.copyProperties(e, sku);
                if (CollectionUtil.isNotEmpty(e.getOpt())) {
                    List<SkuOpt> opts = new ArrayList<>();
                    e.getOpt().forEach((k, v) -> {
                        opts.add(new SkuOpt().setOptName(k).setOptValue(v.toString()));
                    });
                    sku.setOpts(opts);
                }
                return sku;
            }).collect(Collectors.toList());
            return skus;
        }
        return null;
    }

    public ProductEsEntity(ProductChunkEsEntity productChunkEsEntity) {
        this
                .setId(productChunkEsEntity.getKuProductId().toString())
                .setTenantId(productChunkEsEntity.getTenantId())
                .setProjectId(productChunkEsEntity.getProjectId())
                .setMainPush(productChunkEsEntity.getMainPush())
                .setDocumentId(productChunkEsEntity.getDocumentId())
                .setEnable(productChunkEsEntity.getEnable())
                .setProductId(productChunkEsEntity.getProductId())
                .setHandle(productChunkEsEntity.getHandle())
                .setTitle(productChunkEsEntity.getTitle())
                .setDescribe(null)
                .setVendor(productChunkEsEntity.getVendor())
                .setCategory(productChunkEsEntity.getCategory())
                .setPublishedAt(productChunkEsEntity.getPublishedAt())
                .setRequiresShipping(productChunkEsEntity.getRequiresShipping())
                .setTags(productChunkEsEntity.getTags())
                .setImages(productChunkEsEntity.getImages())
                .setSource(productChunkEsEntity.getSource())
                .setPlatform(productChunkEsEntity.getPlatform())
                .setCountry(productChunkEsEntity.getCountry())
                .setCurrency(productChunkEsEntity.getCurrency())
                .setSpuUrl(productChunkEsEntity.getSpuUrl())
                .setSkuList(productChunkEsEntity.getSkuList())
                .setSelfTags(productChunkEsEntity.getSelfTags())
                .setKuLabelValueIds(productChunkEsEntity.getKuLabelValueIds())
                .setBrief(productChunkEsEntity.getBrief())
                .setProductDetailEmbedding(null)
                .setTitleEmbedding(null)
                .setIsVisible(productChunkEsEntity.getIsVisible())
                .setDescribeEmbedding(null);
    }
}
