package com.shulex.qabot.service;

import cn.hutool.cache.CacheUtil;
import cn.hutool.cache.impl.TimedCache;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.lang.func.LambdaUtil;
import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSON;
import com.shulex.gpt.apisdk.IKBApi;
import com.shulex.gpt.apisdk.IToolsApi;
import com.shulex.gpt.apisdk.dto.*;
import com.shulex.gpt.apisdk.enums.*;
import com.shulex.qabot.alg.AlgClient;
import com.shulex.qabot.alg.dto.*;
import com.shulex.qabot.client.dto.EsSearchResult;
import com.shulex.qabot.client.dto.SearchProductParam;
import com.shulex.qabot.client.dto.esentity.*;
import com.shulex.qabot.client.dto.mysqlentity.Directory;
import com.shulex.qabot.client.dto.mysqlentity.KuLabelValue;
import com.shulex.qabot.client.req.KnowledgeSearchReq;
import com.shulex.qabot.client.res.KnowledgeSearchRes;
import com.shulex.qabot.client.res.TranslateTagKeywordRes;
import com.shulex.qabot.config.*;
import com.shulex.qabot.es.EsSearchService;
import com.shulex.qabot.es.EsService;
import com.shulex.qabot.feign.gpt.GptHttpClient;
import com.shulex.qabot.mysql.repository.*;
import com.shulex.qabot.service.product.aiagent.ProductSearchTool;
import com.shulex.qabot.util.MetadataUtil;
import com.shulex.qabot.util.ParallelUtil;
import com.shulex.qabot.util.StringUtil;
import com.shulex.qabot.util.TiktokenUtil;
import lombok.Data;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.apache.lucene.search.join.ScoreMode;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.MatchQueryBuilder;
import org.elasticsearch.index.query.NestedQueryBuilder;
import org.elasticsearch.index.query.RangeQueryBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static java.lang.String.format;

@Service
@Slf4j
public class KnowledgeService {
    @Autowired
    ProjectRepository projectRepository;
    @Autowired
    DirectoryRepository directoryRepository;
    @Autowired
    BedrockService bedrockService;
    @Autowired
    AlgClient algClient;
    @Autowired
    KuService kuService;
    @Autowired
    EsService esService;
    @Autowired
    EsSearchService esSearchService;
    @Autowired
    QaChatConfig qaChatConfig;
    @Autowired
    KuLabelValueRepository kuLabelValueRepository;
    @Autowired
    IKBApi kbApi;
    @Autowired
    GptHttpClient gptHttpClient;
    @Autowired
    DocumentChunkRepository documentChunkRepository;
    @Autowired
    KuRepository kuRepository;
    @Autowired
    IToolsApi gptToolApiFeignClient;
    @Autowired
    InvokeGptConfig invokeGptConfig;
    @Autowired
    PromptConfig promptConfig;
    @Autowired
    SpecialNeedConfig specialNeedConfig;
    @Autowired
    CohereService cohereService;
    @Autowired
    KuProductRepository kuProductRepository;
    @Autowired
    ProductSearchTool productSearchTool;
    @Autowired
    NoticeConfig noticeConfig;
    TimedCache<String, List<Directory>> tenantProject2DirsCache = CacheUtil.newTimedCache(20000);

    public KnowledgeSearchRes searchKnowledge(KnowledgeSearchReq searchReq) {
        KnowledgeSearchRes searchRes = new KnowledgeSearchRes()
                .setUseQa(searchReq.getUseQa())
                .setUseDoc(searchReq.getUseDoc())
                .setUseProduct(searchReq.getUseProduct())
                .setQaSearchContext(new KnowledgeSearchRes.QaSearchContext())
                .setDocSearchContext(new KnowledgeSearchRes.DocSearchContext())
                .setProductSearchContext(new KnowledgeSearchRes.ProductSearchContext());
        LLMUsages usages = new LLMUsages();
        QaChatConfig.KnowledgeSearchConfig knowledgeSearchConfig = qaChatConfig.getKnowledgeSearchConfigBy(searchReq.getTenantId());
        // 检索范围
        Map<Long, Set<Long>> kuLabelDimensionId2ValueIds = searchReq.getKuLabelDimensionId2ValueIds() == null ? new HashMap<>() : new HashMap<>(searchReq.getKuLabelDimensionId2ValueIds());
        {
            // ku label
            Map<Long, Boolean> useMetaDataConditionTagTenantMap = qaChatConfig.getUseMetaDataConditionTagTenantMap();
            Map<String, List<?>> entities = searchReq.getEntities();
            List<String> needMatchKuLabelValueMetadataKeys = ListUtil.toList("condition_tag");
            if (Boolean.FALSE.equals(useMetaDataConditionTagTenantMap.get(searchReq.getTenantId()))) {
                needMatchKuLabelValueMetadataKeys.remove("condition_tag");
            }
            List<String> values = new ArrayList<>();
            for (String needMatchKuLabelValueMetadataKey : needMatchKuLabelValueMetadataKeys) {
                values.addAll(StringUtil.getStringFromEntities(entities, needMatchKuLabelValueMetadataKey));
            }
            log.info("searchKnowledge[{}]: kuLabelRange, valuesFromMetadata={}", searchReq.getChatId(), values);
            if (CollectionUtil.isNotEmpty(values)) {
                List<KuLabelValue> kuLabelValues = kuLabelValueRepository.getKuLabelValuesByName(searchReq.getTenantId(), searchReq.getProjectId(), values);
                log.info("searchKnowledge[{}]: kuLabelRange, kuLabelValues={}", searchReq.getChatId(), kuLabelValues);
                if (CollectionUtil.isNotEmpty(kuLabelValues)) {
                    for (KuLabelValue kuLabelValue : kuLabelValues) {
                        Long dimensionId = kuLabelValue.getKuLabelDimensionId();
                        Long valueId = kuLabelValue.getId();
                        kuLabelDimensionId2ValueIds.computeIfAbsent(dimensionId, k -> new HashSet<>()).add(valueId);
                    }
                }
            }
            log.info("searchKnowledge[{}]: kuLabelRange, kuLabelDimensionId2ValueIds={}", searchReq.getChatId(), kuLabelDimensionId2ValueIds);
        }
        HashSet<Long> rangeDirIds = new HashSet<>();
        {
            // 策略配置指定的文件夹以及所有子文件夹
            HashSet<Long> strategySpecifiedDirAnsSubDirIds = new HashSet<>();
            List<Long> strategySpecifiedDirIds = searchReq.getDirectoryIds();
            if (CollectionUtil.isNotEmpty(strategySpecifiedDirIds)) {
                List<Directory> allDirList = tenantProject2DirsCache.get(String.format("%s_%s", searchReq.getTenantId(), searchReq.getProjectId()), false, () -> directoryRepository.getDirectories(searchReq.getTenantId(), searchReq.getProjectId()));
                Function<Long, List<Directory>> getDirsByParentId = parentId -> allDirList.stream().filter(d -> d.getParentId().equals(parentId)).collect(Collectors.toList());
                Function<Long, Set<Long>> findAllSubDirIds = dirId -> {
                    HashSet<Long> subIds = new HashSet<>();
                    List<Long> needFindChildrenDirIds = new ArrayList<>();
                    needFindChildrenDirIds.add(dirId);
                    while (!needFindChildrenDirIds.isEmpty()) {
                        Long needFindChildDirId = needFindChildrenDirIds.get(0);
                        subIds.add(needFindChildDirId);
                        List<Directory> children = getDirsByParentId.apply(needFindChildDirId);
                        if (CollectionUtil.isNotEmpty(children)) {
                            needFindChildrenDirIds.addAll(children.stream().map(Directory::getId).collect(Collectors.toList()));
                        }
                        needFindChildrenDirIds.remove(0);
                    }
                    subIds.remove(dirId);
                    return subIds;
                };
                strategySpecifiedDirAnsSubDirIds.addAll(strategySpecifiedDirIds);
                for (Long strategySpecifiedDirId : strategySpecifiedDirIds) {
                    Set<Long> subDirIds = findAllSubDirIds.apply(strategySpecifiedDirId);
                    strategySpecifiedDirAnsSubDirIds.addAll(subDirIds);
                }
            }
            rangeDirIds.addAll(strategySpecifiedDirAnsSubDirIds);
            log.info("searchKnowledge[{}]: search, search directory, rangeDirIds={}", searchReq.getChatId(), rangeDirIds);
        }
        // 知识检索，获取三种知识
        String highSimilarityKuResponse = null;
        List<RetrievedKu> esSearchedKuList = new ArrayList<>();
        List<List<DocumentChunkEsEntity>> esSearchedChunks = new ArrayList<>();
        List<ProductEsEntity> esSearchedProducts = new ArrayList<>();
        Map<String, List<EsSearchResult<ProductEsEntity>>> subQuery2EsSearchProducts = new HashMap<>();
        // 知识对应的es得分
        Map<Long, Double> docId2EsScore = new HashMap<>();
        Map<String, Double> productId2EsScore = new HashMap<>();
        // 检索qa
        if (searchReq.getUseQa()) {
            KnowledgeSearchRes.QaSearchContext qaSearchContext = searchRes.getQaSearchContext();
            if (!kuRepository.exits(searchReq.getTenantId(), searchReq.getProjectId())) {
                log.info("searchKnowledge[{}]: qa search, no ku, tenantId={}, projectId={}", searchReq.getChatId(), searchReq.getTenantId(), searchReq.getProjectId());
            } else {
                // 知识召回的目标结果为n个知识数组
                List<AiReplyReq.RetrievedKuData> nWayRetrievedKus = new ArrayList<>();
                log.info("searchKnowledge[{}]: qa search, start", searchReq.getChatId());
                if (CollectionUtil.isEmpty(searchReq.getSpecifiedKuList())) {
                    // 召回知识
                    List<Pair<String, Object>> termQueryFieldAndValue = new ArrayList<>();
                    termQueryFieldAndValue.add(Pair.of(LambdaUtil.getFieldName(KuEsEntity::getPublished), true));
                    if (Boolean.FALSE.equals(searchReq.getQamIsInnerUser())) {
                        termQueryFieldAndValue.add(Pair.of(LambdaUtil.getFieldName(KuEsEntity::getIsVisible), true));
                    }
                    termQueryFieldAndValue.add(Pair.of(LambdaUtil.getFieldName(KuEsEntity::getStatus), KuStatusEnum.INDEXED));
                    termQueryFieldAndValue.add(Pair.of(LambdaUtil.getFieldName(KuEsEntity::getTenantId), searchReq.getTenantId()));
                    termQueryFieldAndValue.add(Pair.of(LambdaUtil.getFieldName(KuEsEntity::getProjectId), searchReq.getProjectId()));
                    if (CollectionUtil.isNotEmpty(rangeDirIds)) {
                        termQueryFieldAndValue.add(Pair.of(LambdaUtil.getFieldName(KuEsEntity::getDirectoryId), rangeDirIds));
                    }
                    // 不同维度之间是 and 关系，同一个维度中的值是 or 关系
                    kuLabelDimensionId2ValueIds.forEach((dimensionId, valueIds) -> {
                        if (CollectionUtil.isNotEmpty(valueIds)) {
                            termQueryFieldAndValue.add(Pair.of(LambdaUtil.getFieldName(KuEsEntity::getKuLabelValueIds), valueIds));
                        }
                    });
                    log.info("searchKnowledge[{}]: qa search, termQueryFieldAndValue={}", searchReq.getChatId(), termQueryFieldAndValue);
                    List<String> excludeFields = EsService.getEmbeddingFieldNames(KuEsEntity.class);
                    // 高相似度知识-用户原始问题检验
                    String userQuestion = IntentService.joinTitleContent(searchReq.getMixedQuery().getOriginTitle(), searchReq.getMixedQuery().getOriginContent());
                    List<BigDecimal> userQuestionCohereEmbedding = bedrockService.getCohereEmbedding(userQuestion);
                    {
                        List<EsSearchResult<KuEsEntity>> userQuestionSearchTitleCohereEmbeddingKuHits = esSearchService.embeddingCosSearchAsync(KuEsEntity.indexName, userQuestionCohereEmbedding, LambdaUtil.getFieldName(KuEsEntity::getTitleEmbeddingCohere), searchReq.getAiReplyOption().getTop_k(), termQueryFieldAndValue, excludeFields, KuEsEntity.class).join();
                        if (CollectionUtil.isNotEmpty(userQuestionSearchTitleCohereEmbeddingKuHits)) {
                            EsSearchResult<KuEsEntity> mostSimilarKuPair = userQuestionSearchTitleCohereEmbeddingKuHits.get(0);
                            Double score = mostSimilarKuPair.getScore();
                            KuEsEntity mostSimilarKu = mostSimilarKuPair.getData();
                            log.info("searchKnowledge[{}]: qa search, first cohere embedding search(userQuestion search title), most similar ku score={}, kuId={}, kuTitle={}, ku={}", searchReq.getChatId(), score, mostSimilarKu.getId(), mostSimilarKu.getTitle(), JSON.toJSONString(mostSimilarKu));
                            if (score >= knowledgeSearchConfig.getKuDirectReturnUserQuestionCohereSimilarity()) {
                                List<RetrievedKu> retrievedKuList = AiReplyQaService.kuEsEntity2RetrievedKu(userQuestionSearchTitleCohereEmbeddingKuHits);
                                retrievedKuList.get(0).setEffect(true);
                                String response = mostSimilarKu.getContent();
                                try {
                                    DetectLanguageResponse responseLanguageRes = gptToolApiFeignClient.detectLanguage(new DetectLanguageRequest(response), invokeGptConfig.getXToken());
                                    if (!responseLanguageRes.getLanguage().equals(BotLanguageEnum.valueOf(searchReq.getAiReplyOption().getLanguage().toUpperCase()))) {
                                        BotLanguageEnum languageEnum = BotLanguageEnum.valueOf(searchReq.getAiReplyOption().getLanguage().toUpperCase());
                                        List<TranslateTagKeywordRes> translateResList = TranslateService.translate(Arrays.asList(mostSimilarKu.getContent()), Arrays.asList(languageEnum));
                                        if (CollectionUtil.isNotEmpty(translateResList)) {
                                            TranslateTagKeywordRes translateRes = translateResList.get(0);
                                            response = translateRes.getToLanguageKeyword();
                                        }
                                    }
                                } catch (Exception e) {
                                    log.warn("searchKnowledge[{}]: qa search, translate mostSimilarKu's content failed", searchReq.getChatId(), e);
                                }
                                highSimilarityKuResponse = response;
                                qaSearchContext.setRetrieveMinScore(searchReq.getAiReplyOption().getRetrieve_min_score())
                                        .setUsedKus(Collections.singletonList(retrievedKuList.get(0)))
                                        .setRerankKus(retrievedKuList);
                                log.info("searchKnowledge[{}]: qa search, first cohere embedding search(userQuestion search title) result satisfy so direct return, similarity={}, threshold={}, kuId={}, kuTitle={}, userQuestion={}", searchReq.getChatId(), score, knowledgeSearchConfig.getKuDirectReturnUserQuestionCohereSimilarity(), mostSimilarKu.getId(), mostSimilarKu.getTitle(), userQuestion);
                            }
                        }
                    }
                    // 高相似度知识-总结后的用户问题检验
                    List<BigDecimal> knowledgeQueryCohereEmbedding = null;
                    if (highSimilarityKuResponse == null) {
                        knowledgeQueryCohereEmbedding = bedrockService.getCohereEmbedding(searchReq.getMixedQuery().getKnowledgeQuery());
                        List<EsSearchResult<KuEsEntity>> knowledgeSearchTitleCohereEmbeddingKuHits = esSearchService.embeddingCosSearchAsync(KuEsEntity.indexName, knowledgeQueryCohereEmbedding, LambdaUtil.getFieldName(KuEsEntity::getTitleEmbeddingCohere), searchReq.getAiReplyOption().getTop_k(), termQueryFieldAndValue, excludeFields, KuEsEntity.class).join();
                        if (CollectionUtil.isNotEmpty(knowledgeSearchTitleCohereEmbeddingKuHits)) {
                            EsSearchResult<KuEsEntity> mostSimilarKuPair = knowledgeSearchTitleCohereEmbeddingKuHits.get(0);
                            Double score = mostSimilarKuPair.getScore();
                            KuEsEntity mostSimilarKu = mostSimilarKuPair.getData();
                            log.info("searchKnowledge[{}]: qa search, first cohere embedding search(knowledge search title), most similar ku score={}, kuId={}, kuTitle={}, ku={}", searchReq.getChatId(), score, mostSimilarKu.getId(), mostSimilarKu.getTitle(), JSON.toJSONString(mostSimilarKu));
                            if (score >= knowledgeSearchConfig.getKuDirectReturnKnowledgeCohereSimilarity()) {
                                List<RetrievedKu> retrievedKuList = AiReplyQaService.kuEsEntity2RetrievedKu(knowledgeSearchTitleCohereEmbeddingKuHits);
                                retrievedKuList.get(0).setEffect(true);
                                String response = mostSimilarKu.getContent();
                                try {
                                    DetectLanguageResponse responseLanguageRes = gptToolApiFeignClient.detectLanguage(new DetectLanguageRequest(response), invokeGptConfig.getXToken());
                                    if (!responseLanguageRes.getLanguage().equals(BotLanguageEnum.valueOf(searchReq.getAiReplyOption().getLanguage().toUpperCase()))) {
                                        BotLanguageEnum languageEnum = BotLanguageEnum.valueOf(searchReq.getAiReplyOption().getLanguage().toUpperCase());
                                        List<TranslateTagKeywordRes> translateResList = TranslateService.translate(Arrays.asList(mostSimilarKu.getContent()), Arrays.asList(languageEnum));
                                        if (CollectionUtil.isNotEmpty(translateResList)) {
                                            TranslateTagKeywordRes translateRes = translateResList.get(0);
                                            response = translateRes.getToLanguageKeyword();
                                        }
                                    }
                                } catch (Exception e) {
                                    log.warn("searchKnowledge[{}]: qa search, translate mostSimilarKu's content failed", searchReq.getChatId(), e);
                                }
                                highSimilarityKuResponse = response;
                                qaSearchContext.setRetrieveMinScore(searchReq.getAiReplyOption().getRetrieve_min_score())
                                        .setUsedKus(Collections.singletonList(retrievedKuList.get(0)))
                                        .setRerankKus(retrievedKuList);
                                log.info("searchKnowledge[{}]: qa search, first cohere embedding search(knowledge search title) result satisfy so direct return, similarity={}, threshold={}, kuId={}, kuTitle={}, knowledgeQuery={}", searchReq.getChatId(), score, knowledgeSearchConfig.getKuDirectReturnKnowledgeCohereSimilarity(), mostSimilarKu.getId(), mostSimilarKu.getTitle(), searchReq.getMixedQuery().getKnowledgeQuery());
                            }
                        }
                    }
                    // 混合搜索
                    if (highSimilarityKuResponse == null) {
                        CompletableFuture<List<EsSearchResult<KuEsEntity>>> future1 = hybridSearch(
                                searchReq.getChatId(),
                                knowledgeSearchConfig.getQaEsSearchSize(),
                                KuEsEntity.indexName,
                                termQueryFieldAndValue,
                                LambdaUtil.getFieldName(KuEsEntity::getTitleEmbeddingCohere),
                                knowledgeQueryCohereEmbedding,
                                searchReq.getExtractTexts(),
                                searchReq.getIntent(),
                                knowledgeSearchConfig.getQaFieldBoostMap(),
                                knowledgeSearchConfig.getIntentBoost(),
                                KuEsEntity.class, knowledgeSearchConfig, null
                        );
                        CompletableFuture<List<EsSearchResult<KuEsEntity>>> future2 = hybridSearch(
                                searchReq.getChatId(),
                                knowledgeSearchConfig.getQaEsSearchSize(),
                                KuEsEntity.indexName,
                                termQueryFieldAndValue,
                                LambdaUtil.getFieldName(KuEsEntity::getContentEmbeddingCohere),
                                knowledgeQueryCohereEmbedding,
                                searchReq.getExtractTexts(),
                                searchReq.getIntent(),
                                knowledgeSearchConfig.getQaFieldBoostMap(),
                                knowledgeSearchConfig.getIntentBoost(),
                                KuEsEntity.class, knowledgeSearchConfig, null
                        );
                        CompletableFuture<List<EsSearchResult<KuEsEntity>>> future3 = hybridSearch(
                                searchReq.getChatId(),
                                knowledgeSearchConfig.getQaEsSearchSize(),
                                KuEsEntity.indexName,
                                termQueryFieldAndValue,
                                LambdaUtil.getFieldName(KuEsEntity::getTitleEmbeddingCohere),
                                userQuestionCohereEmbedding,
                                searchReq.getExtractTexts(),
                                searchReq.getIntent(),
                                knowledgeSearchConfig.getQaFieldBoostMap(),
                                knowledgeSearchConfig.getIntentBoost(),
                                KuEsEntity.class, knowledgeSearchConfig, null
                        );
                        CompletableFuture<List<EsSearchResult<KuEsEntity>>> future4 = hybridSearch(
                                searchReq.getChatId(),
                                knowledgeSearchConfig.getQaEsSearchSize(),
                                KuEsEntity.indexName,
                                termQueryFieldAndValue,
                                LambdaUtil.getFieldName(KuEsEntity::getContentEmbeddingCohere),
                                userQuestionCohereEmbedding,
                                searchReq.getExtractTexts(),
                                searchReq.getIntent(),
                                knowledgeSearchConfig.getQaFieldBoostMap(),
                                knowledgeSearchConfig.getIntentBoost(),
                                KuEsEntity.class, knowledgeSearchConfig, null
                        );
                        List<EsSearchResult<KuEsEntity>> knowledgeQueryEsResults = AiReplyQaService.mergeTwoSearchList(future1.join(), future2.join(), 100);
                        List<EsSearchResult<KuEsEntity>> userQuestionEsResults = AiReplyQaService.mergeTwoSearchList(future3.join(), future4.join(), 100);
                        if (CollectionUtil.isNotEmpty(knowledgeQueryEsResults)) {
                            nWayRetrievedKus.add(new AiReplyReq.RetrievedKuData().setRetrieve_type("knowledgeQueryEsResults").setRetrieved_kus(AiReplyQaService.kuEsEntity2RetrievedKu(knowledgeQueryEsResults)));
                        }
                        if (CollectionUtil.isNotEmpty(userQuestionEsResults)) {
                            nWayRetrievedKus.add(new AiReplyReq.RetrievedKuData().setRetrieve_type("userQuestionEsResults").setRetrieved_kus(AiReplyQaService.kuEsEntity2RetrievedKu(userQuestionEsResults)));
                        }
                    }
                } else {
                    nWayRetrievedKus.add(new AiReplyReq.RetrievedKuData().setRetrieve_type("SpecifiedKu").setRetrieved_kus(searchReq.getSpecifiedKuList()));
                }
                if (highSimilarityKuResponse == null) {
                    // 去重
                    Map<String, RetrievedKu> kuId2Ku = new HashMap<>();
                    nWayRetrievedKus.forEach(list -> {
                        List<RetrievedKu> retrievedKus = list.getRetrieved_kus();
                        retrievedKus.forEach(ku -> {
                            String kuId = ku.getKuId().toString();
                            RetrievedKu existKu = kuId2Ku.get(kuId);
                            if (existKu == null || ku.getScore() > existKu.getScore()) {
                                kuId2Ku.put(kuId, ku);
                            }
                        });
                    });
                    List<RetrievedKu> distinctKuList = new ArrayList<>(kuId2Ku.values());
                    distinctKuList.sort((e1, e2) -> Float.compare(e2.getScore(), e1.getScore()));
                    qaSearchContext.setRetrieveMinScore(distinctKuList.stream().mapToDouble(RetrievedKu::getScore).min().orElse(-1))
                            .setRerankKus(distinctKuList);
                    // 阈值过滤
                    esSearchedKuList = distinctKuList.stream().filter(e -> e.getScore() > knowledgeSearchConfig.getQaEsThreshold()).collect(Collectors.toList());
                }
                log.info("searchKnowledge[{}]: qa search finish", searchReq.getChatId());
            }
        }
        // 检索doc
        if (highSimilarityKuResponse == null && searchReq.getUseDoc()) {
            KnowledgeSearchRes.DocSearchContext docSearchContext = searchRes.getDocSearchContext();
            if (!documentChunkRepository.existChunk(searchReq.getTenantId())) {
                log.info("searchKnowledge[{}]: doc search, no doc, tenantId={}, projectId={}", searchReq.getChatId(), searchReq.getTenantId(), searchReq.getProjectId());
            } else {
                String userQuestion = IntentService.joinTitleContent(searchReq.getMixedQuery().getOriginTitle(), searchReq.getMixedQuery().getOriginContent());
                List<Pair<String, Object>> termQueryFieldAndValue = new ArrayList<>();
                termQueryFieldAndValue.add(Pair.of(LambdaUtil.getFieldName(DocumentChunkEsEntity::getTenantId), searchReq.getTenantId()));
                termQueryFieldAndValue.add(Pair.of(LambdaUtil.getFieldName(DocumentChunkEsEntity::getProjectId), searchReq.getProjectId()));
                termQueryFieldAndValue.add(Pair.of(LambdaUtil.getFieldName(DocumentChunkEsEntity::getEnable), true));
                if (Boolean.FALSE.equals(searchReq.getQamIsInnerUser())) {
                    termQueryFieldAndValue.add(Pair.of(LambdaUtil.getFieldName(DocumentChunkEsEntity::getIsVisible), true));
                }
                if (CollectionUtil.isNotEmpty(rangeDirIds)) {
                    termQueryFieldAndValue.add(Pair.of(LambdaUtil.getFieldName(DocumentChunkEsEntity::getDirectoryId), rangeDirIds));
                }
                kuLabelDimensionId2ValueIds.forEach((dimensionId, valueIds) -> {
                    if (CollectionUtil.isNotEmpty(valueIds)) {
                        termQueryFieldAndValue.add(Pair.of(LambdaUtil.getFieldName(DocumentChunkEsEntity::getKuLabelValueIds), valueIds));
                    }
                });
                // embedding user input
                List<BigDecimal> userQueryEmbedding, userQuestionEmbedding;
                {
                    CompletableFuture<List<BigDecimal>> userQueryFuture = bedrockService.getCohereEmbeddingAsync(searchReq.getMixedQuery().getUserQuery());
                    CompletableFuture<List<BigDecimal>> userQuestionFuture = bedrockService.getCohereEmbeddingAsync(userQuestion);
                    userQueryEmbedding = userQueryFuture.join();
                    userQuestionEmbedding = userQuestionFuture.join();
                }
                // 混合搜索
                CompletableFuture<List<EsSearchResult<DocumentChunkEsEntity>>> userQueryFuture = hybridSearch(
                        searchReq.getChatId(),
                        knowledgeSearchConfig.getDocEsSearchSize(),
                        DocumentChunkEsEntity.indexName,
                        termQueryFieldAndValue,
                        LambdaUtil.getFieldName(DocumentChunkEsEntity::getContentEmbedding),
                        userQueryEmbedding,
                        searchReq.getExtractTexts(),
                        searchReq.getIntent(),
                        knowledgeSearchConfig.getDocFieldBoostMap(),
                        knowledgeSearchConfig.getIntentBoost(),
                        DocumentChunkEsEntity.class, knowledgeSearchConfig, null
                );
                CompletableFuture<List<EsSearchResult<DocumentChunkEsEntity>>> userQuestionFuture = hybridSearch(
                        searchReq.getChatId(),
                        knowledgeSearchConfig.getDocEsSearchSize(),
                        DocumentChunkEsEntity.indexName,
                        termQueryFieldAndValue,
                        LambdaUtil.getFieldName(DocumentChunkEsEntity::getContentEmbedding),
                        userQuestionEmbedding,
                        searchReq.getExtractTexts(),
                        searchReq.getIntent(),
                        knowledgeSearchConfig.getDocFieldBoostMap(),
                        knowledgeSearchConfig.getIntentBoost(),
                        DocumentChunkEsEntity.class, knowledgeSearchConfig, null
                );
                List<EsSearchResult<DocumentChunkEsEntity>> userQueryEsResults = userQueryFuture.join();
                List<EsSearchResult<DocumentChunkEsEntity>> userQuestionEsResults = userQuestionFuture.join();
                List<EsSearchResult<DocumentChunkEsEntity>> esSearchChunks = Stream.concat(userQueryEsResults.stream(), userQuestionEsResults.stream()).collect(Collectors.toList());
                docSearchContext.setEsResults(esSearchChunks);
                log.info("searchKnowledge[{}]: doc search,esSearchChunks={}", searchReq.getChatId(), JSON.toJSONString(esSearchChunks));
                // 阈值过滤
                List<EsSearchResult<DocumentChunkEsEntity>> afterThresholdEsSearchChunks = esSearchChunks.stream().filter(e -> e.getScore() >= knowledgeSearchConfig.getDocEsThreshold()).collect(Collectors.toList());
                // chunk去重
                Map<Long, EsSearchResult<DocumentChunkEsEntity>> searchedChunksDistinct = new LinkedHashMap<>();
                afterThresholdEsSearchChunks.forEach(searchedChunk -> {
                    Long id = searchedChunk.getData().getId();
                    EsSearchResult<DocumentChunkEsEntity> oldChunk = searchedChunksDistinct.get(id);
                    if (oldChunk == null || oldChunk.getScore() < searchedChunk.getScore()) {
                        searchedChunksDistinct.put(id, searchedChunk);
                    }
                });
                afterThresholdEsSearchChunks = new ArrayList<>(searchedChunksDistinct.values());
                for (EsSearchResult<DocumentChunkEsEntity> afterThresholdEsSearchChunk : afterThresholdEsSearchChunks) {
                    Double score = afterThresholdEsSearchChunk.getScore();
                    Long documentId = afterThresholdEsSearchChunk.getData().getDocumentId();
                    Double oldScore = docId2EsScore.get(documentId);
                    if (oldScore == null || oldScore < score) {
                        docId2EsScore.put(documentId, score);
                    }
                }
                // expand chunk
                int expandSize = qaChatConfig.getDocumentChunkExpandSizeBy(searchReq.getTenantId());
                Map<Long, EsSearchResult<DocumentChunkEsEntity>> searchedChunkIdMap = new HashMap<>();
                Map<Long, List<DocumentChunkEsEntity>> searchedChunkId2ExpandChunks = new HashMap<>();
                Map<Long, Set<Long>> searchedChunkId2ExpandChunkIds = new HashMap<>();
                afterThresholdEsSearchChunks.stream()
                        .map(chunk -> {
                            if (expandSize > 0) {
                                Integer currentSeq = chunk.getData().getChunkSeq();
                                List<Integer> querySeq = new ArrayList<>();
                                for (int i = 1; i <= expandSize; i++) {
                                    querySeq.add(currentSeq - i);
                                    querySeq.add(currentSeq + i);
                                }
                                List<Pair<String, Object>> expandChunkSearchQuery = new ArrayList<>();
                                expandChunkSearchQuery.add(Pair.of(LambdaUtil.getFieldName(DocumentChunkEsEntity::getTenantId), searchReq.getTenantId()));
                                expandChunkSearchQuery.add(Pair.of(LambdaUtil.getFieldName(DocumentChunkEsEntity::getProjectId), searchReq.getProjectId()));
                                expandChunkSearchQuery.add(Pair.of(LambdaUtil.getFieldName(DocumentChunkEsEntity::getDocumentId), chunk.getData().getDocumentId()));
                                expandChunkSearchQuery.add(Pair.of(LambdaUtil.getFieldName(DocumentChunkEsEntity::getResourceId), chunk.getData().getResourceId()));
                                expandChunkSearchQuery.add(Pair.of(LambdaUtil.getFieldName(DocumentChunkEsEntity::getChunkSeq), querySeq));
                                return esSearchService.termQueryAsync(DocumentChunkEsEntity.indexName, querySeq.size(), expandChunkSearchQuery, EsService.getEmbeddingFieldNames(DocumentChunkEsEntity.class),
                                        DocumentChunkEsEntity.class).thenApply(esResults -> {
                                    List<DocumentChunkEsEntity> expandChunks = esResults.stream().map(EsSearchResult::getData).collect(Collectors.toList());
                                    return Pair.of(chunk, expandChunks);
                                });
                            } else {
                                List<DocumentChunkEsEntity> expandChunks = new ArrayList<>();
                                return CompletableFuture.completedFuture(Pair.of(chunk, expandChunks));
                            }
                        }).collect(Collectors.toList())
                        .forEach(future -> {
                            Pair<EsSearchResult<DocumentChunkEsEntity>, List<DocumentChunkEsEntity>> pair = future.join();
                            DocumentChunkEsEntity chunk = pair.getKey().getData();
                            List<DocumentChunkEsEntity> expandChunks = pair.getValue();
                            searchedChunkIdMap.put(chunk.getId(), pair.getKey());
                            searchedChunkId2ExpandChunks.put(chunk.getId(), expandChunks);
                            searchedChunkId2ExpandChunkIds.put(chunk.getId(), expandChunks.stream().map(DocumentChunkEsEntity::getId).collect(Collectors.toSet()));
                        });
                // merge chunk & token limit
                List<DocumentChunkEsEntity> allChunks = new ArrayList<>();
                searchedChunkId2ExpandChunks.forEach((id, expandChunks) -> allChunks.addAll(expandChunks));
                allChunks.addAll(afterThresholdEsSearchChunks.stream().map(EsSearchResult::getData).collect(Collectors.toList()));
                Map<Long, List<DocumentChunkEsEntity>> chunksGroupByDocId = allChunks.stream().collect(Collectors.groupingBy(DocumentChunkEsEntity::getDocumentId));
                Function<List<DocumentChunkEsEntity>, String> getChunksPromptFunc = chunks -> new KnowledgeSortResult().setChunk(chunks).getDocumentPrompt(null);
                chunksGroupByDocId.forEach((docId, chunks) -> {
                    // 去重
                    List<DocumentChunkEsEntity> distinctChunks = new ArrayList<>();
                    for (DocumentChunkEsEntity chunk : chunks) {
                        if (distinctChunks.stream().noneMatch(e -> e.getId().equals(chunk.getId()))) {
                            distinctChunks.add(chunk);
                        }
                    }
                    distinctChunks = distinctChunks.stream().sorted(Comparator.comparing(DocumentChunkEsEntity::getChunkSeq)).collect(Collectors.toList());
                    // 查询chunk的关系
                    Map<Long, EsSearchResult<DocumentChunkEsEntity>> searchedFromEsChunkIdMap = new HashMap<>();
                    Map<Long, Set<Long>> searchedFromEsChunkId2ExpandChunkIds = new HashMap<>();
                    for (DocumentChunkEsEntity chunk : distinctChunks) {
                        if (searchedChunkIdMap.containsKey(chunk.getId())) {
                            searchedFromEsChunkIdMap.put(chunk.getId(), searchedChunkIdMap.get(chunk.getId()));
                        }
                    }
                    for (DocumentChunkEsEntity chunk : distinctChunks) {
                        if (!searchedFromEsChunkIdMap.containsKey(chunk.getId())) {
                            // 说明是expand出来的，查找所属得分最高的chunk
                            Set<Long> searchedFromEsChunkIds = searchedFromEsChunkIdMap.keySet();
                            Long belongedChunkId = null;
                            Double belongedChunkScore = null;
                            for (Long searchedFromEsChunkId : searchedFromEsChunkIds) {
                                Double score = searchedFromEsChunkIdMap.get(searchedFromEsChunkId).getScore();
                                Set<Long> allExpandChunkIds = searchedChunkId2ExpandChunkIds.get(searchedFromEsChunkId);
                                if (CollectionUtil.isNotEmpty(allExpandChunkIds) && allExpandChunkIds.contains(chunk.getId())) {
                                    if (belongedChunkScore == null || score > belongedChunkScore) {
                                        belongedChunkScore = score;
                                        belongedChunkId = searchedFromEsChunkId;
                                    }
                                }
                            }
                            searchedFromEsChunkId2ExpandChunkIds.computeIfAbsent(belongedChunkId, k -> new HashSet<>()).add(chunk.getId());
                        }
                    }
                    // token限制
                    int currentToken = TiktokenUtil.size(getChunksPromptFunc.apply(distinctChunks));
                    Integer knowledgeMaxToken = knowledgeSearchConfig.getKnowledgeMaxToken();
                    log.info("searchKnowledge[{}]: maxToken, cut docChunk, docId={}, currentToken={}, chunkIds={}", searchReq.getChatId(), docId, currentToken, distinctChunks.stream().map(DocumentChunkEsEntity::getId).collect(Collectors.toList()));
                    while (currentToken >= knowledgeMaxToken) {
                        // 裁剪chunk：优先取低得分chunk的扩展chunk，如果没有则取低得分chunk
                        Long minScoreChunkId = null;
                        Double minScore = null;
                        for (DocumentChunkEsEntity chunk : distinctChunks) {
                            if (searchedFromEsChunkIdMap.containsKey(chunk.getId())) {
                                Double score = searchedFromEsChunkIdMap.get(chunk.getId()).getScore();
                                if (minScore == null || score < minScore) {
                                    minScore = score;
                                    minScoreChunkId = chunk.getId();
                                }
                            }
                        }
                        int removeIndex = -1;
                        Set<Long> minScoreChunkExpandedChunkIds = searchedFromEsChunkId2ExpandChunkIds.get(minScoreChunkId);
                        if (CollectionUtil.isNotEmpty(minScoreChunkExpandedChunkIds)) {
                            for (int i = 0; i < distinctChunks.size(); i++) {
                                DocumentChunkEsEntity chunk = distinctChunks.get(i);
                                if (!searchedFromEsChunkIdMap.containsKey(chunk.getId())) {
                                    if (minScoreChunkExpandedChunkIds.contains(chunk.getId())) {
                                        removeIndex = i;
                                        break;
                                    }
                                }
                            }
                        }
                        if (removeIndex == -1) {
                            for (int i = 0; i < distinctChunks.size(); i++) {
                                if (distinctChunks.get(i).getId().equals(minScoreChunkId)) {
                                    removeIndex = i;
                                    break;
                                }
                            }
                        }
                        distinctChunks.remove(removeIndex);
                        currentToken = TiktokenUtil.size(getChunksPromptFunc.apply(distinctChunks));
                        log.info("searchKnowledge[{}]: maxToken, after cut docChunk, docId={}, currentToken={}, chunkIds={}", searchReq.getChatId(), docId, currentToken, distinctChunks.stream().map(DocumentChunkEsEntity::getId).collect(Collectors.toList()));
                    }
                    List<DocumentChunkEsEntity> oneDocChunks = distinctChunks.stream().sorted(Comparator.comparing(DocumentChunkEsEntity::getChunkSeq)).collect(Collectors.toList());
                    if (CollectionUtil.isNotEmpty(oneDocChunks)) {
                        esSearchedChunks.add(oneDocChunks);
                    }
                });
            }
        }
        // 检索product
        if (highSimilarityKuResponse == null && searchReq.getUseProduct()) {
            KnowledgeSearchRes.ProductSearchContext productSearchContext = searchRes.getProductSearchContext();
            productSearchContext.setProductSearchParam(searchReq.getProductSearchParam());
            if (!kuProductRepository.exist(searchReq.getTenantId())) {
                log.info("searchKnowledge[{}]: product search, tenantId[{}] has no product", searchReq.getChatId(), searchReq.getTenantId());
            } else {
                ProductSearchTool.ToolInput toolInput = new ProductSearchTool.ToolInput()
                        .setTenantId(searchReq.getTenantId())
                        .setProjectId(searchReq.getProjectId())
                        .setSummary(searchReq.getProductSearchParam() != null ? searchReq.getProductSearchParam().getSummary() : null)
                        .setSubqueries(searchReq.getProductSearchParam() != null ? searchReq.getProductSearchParam().getSubqueries() : null)
                        .setProduct(searchReq.getProductSearchParam() != null ? searchReq.getProductSearchParam().getProduct() : null)
                        .setRequirement(searchReq.getProductSearchParam() != null ? searchReq.getProductSearchParam().getRequirement() : null)
                        .setPrice_range(searchReq.getProductSearchParam() != null ? searchReq.getProductSearchParam().getPrice_range() : null);
                Double minSkuPrice, maxSkuPrice;
                if (toolInput.getPrice_range() != null && toolInput.getPrice_range().size() >= 2) {
                    minSkuPrice = toolInput.getPrice_range().get(0);
                    maxSkuPrice = toolInput.getPrice_range().get(1);
                } else {
                    maxSkuPrice = null;
                    minSkuPrice = null;
                }
                // 混合搜索
                List<Pair<String, Object>> termQueryFieldAndValue = new ArrayList<>();
                termQueryFieldAndValue.add(Pair.of(LambdaUtil.getFieldName(ProductEsEntity::getTenantId), searchReq.getTenantId()));
                termQueryFieldAndValue.add(Pair.of(LambdaUtil.getFieldName(ProductEsEntity::getProjectId), searchReq.getProjectId()));
                termQueryFieldAndValue.add(Pair.of(LambdaUtil.getFieldName(ProductEsEntity::getEnable), true));
                if (Boolean.FALSE.equals(searchReq.getQamIsInnerUser())) {
                    termQueryFieldAndValue.add(Pair.of(LambdaUtil.getFieldName(ProductEsEntity::getIsVisible), true));
                }
                if (CollectionUtil.isNotEmpty(rangeDirIds)) {
                    termQueryFieldAndValue.add(Pair.of(LambdaUtil.getFieldName(ProductEsEntity::getDirectoryId), rangeDirIds));
                }

                kuLabelDimensionId2ValueIds.forEach((dimensionId, valueIds) -> {
                    if (CollectionUtil.isNotEmpty(valueIds)) {
                        termQueryFieldAndValue.add(Pair.of(LambdaUtil.getFieldName(ProductEsEntity::getKuLabelValueIds), valueIds));
                    }
                });
                SearchProductParam searchProductParam = MetadataUtil.getMetadataFirstValue(searchReq.getEntities(), "search_product_param", SearchProductParam.class);
                log.info("searchKnowledge[{}]: product search, searchProductParam={}", searchReq.getChatId(), JSON.toJSONString(searchProductParam));
                if (searchProductParam != null) {
                    if (CollectionUtil.isNotEmpty(searchProductParam.getBrands())) {
                        termQueryFieldAndValue.add(Pair.of(LambdaUtil.getFieldName(ProductEsEntity::getBrand) + ".keyword", searchProductParam.getBrands()));
                    }
                    if (CollectionUtil.isNotEmpty(searchProductParam.getStoreNames())) {
                        termQueryFieldAndValue.add(Pair.of(LambdaUtil.getFieldName(ProductEsEntity::getStoreName) + ".keyword", searchProductParam.getStoreNames()));
                    }
                }
                log.info("searchKnowledge[{}]: product search, termQueryFieldAndValue={}", searchReq.getChatId(), JSON.toJSONString(termQueryFieldAndValue));
                Map<String, List<EsSearchResult<ProductEsEntity>>> esSearchedQuery2Products = new HashMap<>();
                List<String> queries = new ArrayList<>();
                if (CollectionUtil.isNotEmpty(toolInput.getSubqueries())) {
                    queries.addAll(toolInput.getSubqueries());
                } else {
                    queries.add(searchReq.getMixedQuery().getKnowledgeQuery());
                }
                ParallelUtil.parallelAsyncCall(5, false, queries, query -> {
                    return bedrockService
                            .getCohereEmbeddingAsync(query)
                            .thenCompose(embedding -> {
                                CompletableFuture<List<EsSearchResult<ProductEsEntity>>> titleFuture = hybridSearch(
                                        searchReq.getChatId(),
                                        knowledgeSearchConfig.getProductEsSearchSize(),
                                        ProductEsEntity.indexName,
                                        termQueryFieldAndValue,
                                        LambdaUtil.getFieldName(ProductEsEntity::getTitleEmbedding),
                                        embedding,
                                        searchReq.getExtractTexts(),
                                        searchReq.getIntent(),
                                        knowledgeSearchConfig.getProductFieldBoostMap(),
                                        knowledgeSearchConfig.getIntentBoost(),
                                        ProductEsEntity.class,
                                        knowledgeSearchConfig,
                                        boolQueryBuilder -> {
                                            if (minSkuPrice != null || maxSkuPrice != null) {
                                                RangeQueryBuilder rangeQueryBuilder = new RangeQueryBuilder("skuList.price");
                                                if (minSkuPrice != null) {
                                                    rangeQueryBuilder.gte(minSkuPrice);
                                                }
                                                if (maxSkuPrice != null) {
                                                    rangeQueryBuilder.lte(maxSkuPrice);
                                                }
                                                NestedQueryBuilder nestedQueryBuilder = new NestedQueryBuilder("skuList", rangeQueryBuilder, ScoreMode.None);
                                                boolQueryBuilder.filter(nestedQueryBuilder);
                                            }
                                        }
                                );
                                CompletableFuture<List<EsSearchResult<ProductEsEntity>>> describeFuture = hybridSearch(
                                        searchReq.getChatId(),
                                        knowledgeSearchConfig.getProductEsSearchSize() * 2,
                                        ProductChunkEsEntity.indexName,
                                        termQueryFieldAndValue,
                                        LambdaUtil.getFieldName(ProductChunkEsEntity::getChunkContentEmbedding),
                                        embedding,
                                        searchReq.getExtractTexts(),
                                        searchReq.getIntent(),
                                        knowledgeSearchConfig.getProductFieldBoostMap(),
                                        knowledgeSearchConfig.getIntentBoost(),
                                        ProductChunkEsEntity.class,
                                        knowledgeSearchConfig,
                                        boolQueryBuilder -> {
                                            if (minSkuPrice != null || maxSkuPrice != null) {
                                                RangeQueryBuilder rangeQueryBuilder = new RangeQueryBuilder("skuList.price");
                                                if (minSkuPrice != null) {
                                                    rangeQueryBuilder.gte(minSkuPrice);
                                                }
                                                if (maxSkuPrice != null) {
                                                    rangeQueryBuilder.lte(maxSkuPrice);
                                                }
                                                NestedQueryBuilder nestedQueryBuilder = new NestedQueryBuilder("skuList", rangeQueryBuilder, ScoreMode.None);
                                                boolQueryBuilder.filter(nestedQueryBuilder);
                                            }
                                        }
                                )
                                        .thenCompose(searchedChunks -> {
                                            // 扩展chunk
                                            int expandSize = qaChatConfig.getProductChunkExpandSizeBy(searchReq.getTenantId());
                                            if (expandSize > 0) {
                                                List<EsSearchResult<ProductChunkEsEntity>> allChunks = Collections.synchronizedList(new ArrayList<>());
                                                allChunks.addAll(searchedChunks);
                                                List<CompletableFuture<Void>> futures = new ArrayList<>();
                                                for (EsSearchResult<ProductChunkEsEntity> chunk : searchedChunks) {
                                                    Integer currentSeq = chunk.getData().getChunkSeq();
                                                    List<Integer> querySeq = new ArrayList<>();
                                                    for (int i = 1; i <= expandSize; i++) {
                                                        querySeq.add(currentSeq - i);
                                                        querySeq.add(currentSeq + i);
                                                    }
                                                    List<Pair<String, Object>> expandChunkSearchQuery = new ArrayList<>();
                                                    expandChunkSearchQuery.add(Pair.of(LambdaUtil.getFieldName(ProductChunkEsEntity::getTenantId), searchReq.getTenantId()));
                                                    expandChunkSearchQuery.add(Pair.of(LambdaUtil.getFieldName(ProductChunkEsEntity::getProjectId), searchReq.getProjectId()));
                                                    if (Boolean.FALSE.equals(searchReq.getQamIsInnerUser())) {
                                                        expandChunkSearchQuery.add(Pair.of(LambdaUtil.getFieldName(ProductChunkEsEntity::getIsVisible), true));
                                                    }
                                                    expandChunkSearchQuery.add(Pair.of(LambdaUtil.getFieldName(ProductChunkEsEntity::getKuProductId), chunk.getData().getKuProductId()));
                                                    expandChunkSearchQuery.add(Pair.of(LambdaUtil.getFieldName(ProductChunkEsEntity::getChunkSeq), querySeq));
                                                    CompletableFuture<Void> expandChunkFuture = esSearchService.termQueryAsync(ProductChunkEsEntity.indexName, querySeq.size(), expandChunkSearchQuery, EsService.getEmbeddingFieldNames(ProductChunkEsEntity.class),
                                                                    ProductChunkEsEntity.class)
                                                            .thenApply(expandedChunks -> {
                                                                for (EsSearchResult<ProductChunkEsEntity> expandedChunk : expandedChunks) {
                                                                    expandedChunk.setScore(chunk.getScore());
                                                                }
                                                                return expandedChunks;
                                                            })
                                                            .thenAccept(expandedChunks -> allChunks.addAll(expandedChunks));
                                                    futures.add(expandChunkFuture);
                                                }
                                                return CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).thenApply(v -> {
                                                    // 去重allChunks，相同的chunk score取大的
                                                    Map<String, EsSearchResult<ProductChunkEsEntity>> chunkMap = new HashMap<>();
                                                    for (EsSearchResult<ProductChunkEsEntity> chunk : allChunks) {
                                                        Double score = chunk.getScore();
                                                        String chunkId = chunk.getData().getId();
                                                        EsSearchResult<ProductChunkEsEntity> oldChunk = chunkMap.get(chunkId);
                                                        if (oldChunk == null || oldChunk.getScore() < score) {
                                                            chunkMap.put(chunkId, chunk);
                                                        }
                                                    }
                                                    ArrayList<EsSearchResult<ProductChunkEsEntity>> productChunks = new ArrayList<>(chunkMap.values());
                                                    productChunks.sort((e1, e2) -> e2.getScore().compareTo(e1.getScore()));
                                                    return productChunks;
                                                });
                                            }
                                            return CompletableFuture.completedFuture(searchedChunks);
                                        })
                                        .thenApply(searchedChunks -> {
                                            // 将chunk合并为product
                                            Map<String, List<EsSearchResult<ProductChunkEsEntity>>> kuProductId2Chunks = searchedChunks.stream().collect(Collectors.groupingBy(e -> e.getData().getKuProductId().toString()));
                                            List<EsSearchResult<ProductEsEntity>> productEsResults = new ArrayList<>();
                                            for (String kuProductId : kuProductId2Chunks.keySet()) {
                                                List<EsSearchResult<ProductChunkEsEntity>> chunks = kuProductId2Chunks.get(kuProductId);
                                                chunks.sort(Comparator.comparing(e -> e.getData().getChunkSeq()));
                                                Double maxScore = Double.MIN_VALUE;
                                                StringBuilder newDescription = new StringBuilder();
                                                for (EsSearchResult<ProductChunkEsEntity> chunk : chunks) {
                                                    if (chunk.getScore() > maxScore) {
                                                        maxScore = chunk.getScore();
                                                    }
                                                    newDescription.append(chunk.getData().getContent()).append("\n");
                                                }
                                                ProductEsEntity productEsEntity = new ProductEsEntity(chunks.get(0).getData());
                                                productEsEntity.setDescribe(newDescription.toString());
                                                productEsResults.add(new EsSearchResult<ProductEsEntity>().setScore(maxScore).setData(productEsEntity));
                                            }
                                            return productEsResults;
                                        });
                                return titleFuture.thenCombine(describeFuture, (r1, r2) -> {
                                    Map<String, EsSearchResult<ProductEsEntity>> esSearchResultMap = new LinkedHashMap<>();
                                    Stream.concat(r1.stream(), r2.stream()).forEach(r -> {
                                        String id = r.getData().getId();
                                        EsSearchResult<ProductEsEntity> exist = esSearchResultMap.get(id);
                                        if (exist == null || r.getScore() > exist.getScore()) {
                                            esSearchResultMap.put(id, r);
                                        }
                                    });
                                    synchronized (esSearchedQuery2Products) {
                                        esSearchedQuery2Products.put(query, new ArrayList<>(esSearchResultMap.values()));
                                    }
                                    return null;
                                });
                            });
                });
                // 去重
                Map<String, EsSearchResult<ProductEsEntity>> distinctProducts = new HashMap<>();
                esSearchedQuery2Products.forEach((query, products) -> {
                    for (EsSearchResult<ProductEsEntity> product : products) {
                        EsSearchResult<ProductEsEntity> exist = distinctProducts.get(product.getData().getId());
                        if (exist == null || product.getScore() > exist.getScore()) {
                            distinctProducts.put(product.getData().getId(), product);
                        }
                    }
                    subQuery2EsSearchProducts.computeIfAbsent(query, k -> new ArrayList<>())
                            .addAll(
                                    products.stream().filter(e -> e.getScore() > knowledgeSearchConfig.getProductEsThreshold()).collect(Collectors.toList())
                            );
                });
                // 阈值过滤
                List<EsSearchResult<ProductEsEntity>> afterThresholdProducts = new ArrayList<>(distinctProducts.values()).stream().filter(e -> e.getScore() > knowledgeSearchConfig.getProductEsThreshold()).collect(Collectors.toList());
                for (EsSearchResult<ProductEsEntity> afterThresholdProduct : afterThresholdProducts) {
                    Double score = afterThresholdProduct.getScore();
                    String id = afterThresholdProduct.getData().getId();
                    Double oldScore = productId2EsScore.get(id);
                    if (oldScore == null || oldScore < score) {
                        productId2EsScore.put(id, score);
                    }
                }
                esSearchedProducts = afterThresholdProducts.stream().map(EsSearchResult::getData).collect(Collectors.toList());
                // 赋值context
                Map<String, Pair<List<EsSearchResult<ProductEsEntity>>, List<EsSearchResult<ProductEsEntity>>>> esResults = new HashMap<>();
                esSearchedQuery2Products.forEach((query, products) -> {
                    esResults.put(query, Pair.of(products, null));
                });
                productSearchContext.addEsResults(esResults);
            }
        }
        // rerank
        List<KnowledgeSortResult> knowledgeSortResults;
        try {
//            knowledgeSortResults = cohereService.rerankKnowledge(searchReq.getMixedQuery().getUserQuery(), esSearchedKuList, esSearchedChunks, esSearchedProducts, usages);
            knowledgeSortResults = bedrockService.rerankKnowledge(searchReq.getChatId(), knowledgeSearchConfig, searchReq.getMixedQuery().getUserQuery(), esSearchedKuList, esSearchedChunks, esSearchedProducts, usages);
        } catch (Exception e) {
            String errMsg = String.format("cohere rerank error[%s], so ues es score to rerank", searchReq.getChatId());
            noticeConfig.sendError(errMsg + "\n\n" + ExceptionUtil.stacktraceToString(e));
            log.error(errMsg, e);
            log.info("esScoreToRerank[{}]: docId2EsScore={},productId2EsScore={},esSearchedKuList={},esSearchedChunks={},esSearchedProducts={}",
                    searchReq.getChatId(), JSON.toJSONString(docId2EsScore), JSON.toJSONString(productId2EsScore), JSON.toJSONString(esSearchedKuList), JSON.toJSONString(esSearchedChunks), JSON.toJSONString(esSearchedProducts));
            List<Pair<Double, Object>> knowledgeAndScores = new ArrayList<>();
            for (RetrievedKu ku : esSearchedKuList) {
                knowledgeAndScores.add(Pair.of((double) ku.getScore(), ku));
            }
            for (List<DocumentChunkEsEntity> oneDocChunks : esSearchedChunks) {
                Long documentId = oneDocChunks.get(0).getDocumentId();
                Double score = docId2EsScore.get(documentId);
                knowledgeAndScores.add(Pair.of(score, oneDocChunks));
            }
            for (ProductEsEntity esSearchedProduct : esSearchedProducts) {
                String id = esSearchedProduct.getId();
                Double score = productId2EsScore.get(id);
                knowledgeAndScores.add(Pair.of(score, esSearchedProduct));
            }
            log.info("esScoreToRerank[{}]: knowledgeAndScores={}", searchReq.getChatId(), JSON.toJSONString(knowledgeAndScores));
            knowledgeAndScores.sort((v1, v2) -> v2.getKey().compareTo(v1.getKey()));
            knowledgeSortResults = new ArrayList<>();
            for (int i = 0; i < knowledgeAndScores.size(); i++) {
                Pair<Double, Object> knowledgeAndScore = knowledgeAndScores.get(i);
                Double score = knowledgeAndScore.getKey();
                Object knowledge = knowledgeAndScore.getValue();
                KnowledgeSortResult knowledgeSortResult = new KnowledgeSortResult()
                        .setRerankRank(i + 1)
                        .setRerankScore(score);
                if (knowledge instanceof RetrievedKu) {
                    knowledgeSortResult.setKu((RetrievedKu) knowledge);
                } else if (knowledge instanceof List) {
                    knowledgeSortResult.setChunk((List<DocumentChunkEsEntity>) knowledge);
                } else if (knowledge instanceof ProductEsEntity) {
                    knowledgeSortResult.setProduct((ProductEsEntity) knowledge);
                }
                knowledgeSortResults.add(knowledgeSortResult);
            }
            log.info("esScoreToRerank[{}]: knowledgeSortResults={}", searchReq.getChatId(), JSON.toJSONString(knowledgeSortResults));
        }
        List<KnowledgeSortResult> rerankKuList = knowledgeSortResults.stream().filter(e -> e.getKu() != null).collect(Collectors.toList());
        List<KnowledgeSortResult> rerankChunks = knowledgeSortResults.stream().filter(e -> e.getChunk() != null).collect(Collectors.toList());
        List<KnowledgeSortResult> rerankProducts = knowledgeSortResults.stream().filter(e -> e.getProduct() != null).collect(Collectors.toList());
        rerankKuList.forEach(e -> e.getKu().setRerankScore((float) e.getRerankScore()));
        List<KnowledgeSortResult> kuTopNList = rerankKuList.subList(0, Math.min(rerankKuList.size(), knowledgeSearchConfig.getQaUsedSize()));
        List<KnowledgeSortResult> chunkTopNList = rerankChunks.subList(0, Math.min(rerankChunks.size(), knowledgeSearchConfig.getDocUsedSize()));
        List<KnowledgeSortResult> productTopNList = new ArrayList<>();
        {
            // 商品数据每个子句都尽量保证有一个商品，总数还是受到配置约束
            List<String> chooseProductIds = new ArrayList<>();
            // choose top1 es result for each subquery
            subQuery2EsSearchProducts.forEach((subQuery, products) -> {
                if (CollectionUtil.isNotEmpty(products)) {
                    EsSearchResult<ProductEsEntity> product = products.get(0);
                    if (!chooseProductIds.contains(product.getData().getId())) {
                        chooseProductIds.add(product.getData().getId());
                    }
                }
            });
            if (chooseProductIds.size() < knowledgeSearchConfig.getProductUsedSize()) {
                // 如果没够那么再挑选一些
                for (KnowledgeSortResult rerankProduct : rerankProducts) {
                    if (!chooseProductIds.contains(rerankProduct.getProduct().getId())) {
                        chooseProductIds.add(rerankProduct.getProduct().getId());
                    }
                    if (chooseProductIds.size() >= knowledgeSearchConfig.getProductUsedSize()) {
                        break;
                    }
                }
            }
            List<KnowledgeSortResult> chooseProducts = rerankProducts.stream().filter(e -> chooseProductIds.contains(e.getProduct().getId())).collect(Collectors.toList());
            productTopNList.addAll(chooseProducts.subList(0, Math.min(chooseProducts.size(), knowledgeSearchConfig.getProductUsedSize())));
        }
        // 相关性判断
        Boolean needRelevance = Boolean.TRUE.equals(searchReq.getIsLivechatPath()) ?
                knowledgeSearchConfig.getLivechatNeedRelevance() : knowledgeSearchConfig.getNotLivechatNeedRelevance();
        List<KnowledgeSortResult> usedKuList;
        List<KnowledgeSortResult> usedChunks;
        List<KnowledgeSortResult> usedProducts;
        List<KnowledgeSortResult> relevanceChunks = Collections.synchronizedList(new ArrayList<>());
        List<KnowledgeSortResult> relevanceProducts = Collections.synchronizedList(new ArrayList<>());
        if (Boolean.TRUE.equals(needRelevance)) {
            RelevantScoreReq baseRelevanceReq = new RelevantScoreReq().setClient(searchReq.getClientType())
                    .setTenant_id(searchReq.getTenantId())
                    .setChat_id(searchReq.getChatId())
                    .setTouch_point(searchReq.getTouchPoint())
                    .setPdt(searchReq.getPdt())
                    .setIntent(searchReq.getIntent())
                    .setLanguage(searchReq.getAiReplyOption().getLanguage());
            // ku
            ParallelUtil.parallelAsyncCall(5, false, kuTopNList, ku -> {
                RelevantScoreReq req = baseRelevanceReq.copy()
                        .setQuestion(searchReq.getMixedQuery().getKnowledgeEnQuery())
                        .setContext(ku.getKu().getTitle() + " " + ku.getKu().getContent());
                return algClient.relevantScore(req).thenAccept(relevantScoreRes -> {
                    usages.merge(relevantScoreRes.getUsages());
                    Float relevantScore = relevantScoreRes.getResult().getRelevant_score();
                    Float relevantMinScore = relevantScoreRes.getResult().getRelevant_min_score();
                    ku.getKu().setRelevantScore(relevantScore);
                    ku.getKu().setEffect(relevantScore >= relevantMinScore);
                    ku.setRelevanceScore(Double.valueOf(relevantScore));
                });
            });
            usedKuList = kuTopNList.stream().filter(e -> e.getKu().isEffect()).collect(Collectors.toList());
            // doc
            AtomicReference<Float> docMinScore = new AtomicReference<>(Float.MAX_VALUE);
            ParallelUtil.parallelAsyncCall(5, false, chunkTopNList, chunks -> {
                RelevantScoreReq req = baseRelevanceReq.copy()
                        .setQuestion(searchReq.getMixedQuery().getUserEnQuery())
                        .setContext(chunks.getChunk().stream().map(DocumentChunkEsEntity::getContent).collect(Collectors.joining("\n")));
                return algClient.relevantScore(req).thenAccept(relevantScoreRes -> {
                    usages.merge(relevantScoreRes.getUsages());
                    Float relevantScore = relevantScoreRes.getResult().getRelevant_score();
                    Float relevantMinScore = relevantScoreRes.getResult().getRelevant_min_score();
                    docMinScore.set(relevantMinScore);
                    chunks.setRelevanceScore(Double.valueOf(relevantScore));
                    relevanceChunks.add(chunks);
                });
            });
            usedChunks = relevanceChunks.stream().filter(e -> e.getRelevanceScore() >= docMinScore.get()).collect(Collectors.toList());
            usedChunks.sort(Comparator.comparingInt(KnowledgeSortResult::getRerankRank));
            relevanceChunks.sort((e1, e2) -> e2.getRelevanceScore().compareTo(e1.getRelevanceScore()));
            // product
            AtomicReference<Float> productMinScore = new AtomicReference<>(Float.MAX_VALUE);
            ParallelUtil.parallelAsyncCall(5, false, productTopNList, product -> {
                RelevantScoreReq req = baseRelevanceReq.copy()
                        .setQuestion(searchReq.getMixedQuery().getUserEnQuery().toLowerCase())
                        .setContext((product.getProduct().getTitle() + "\n" + product.getProduct().getDescribe()).toLowerCase());
                return algClient.relevantScore(req).thenAccept(relevantScoreRes -> {
                    usages.merge(relevantScoreRes.getUsages());
                    Float relevantScore = relevantScoreRes.getResult().getRelevant_score();
                    Float relevantMinScore = relevantScoreRes.getResult().getRelevant_min_score();
                    if (relevantMinScore != null) {
                        productMinScore.set(relevantMinScore);
                    }
                    product.setRelevanceScore(Double.valueOf(relevantScore));
                    relevanceProducts.add(product);
                });
            });
            usedProducts = relevanceProducts.stream().filter(e -> {
                if (e == null || e.getRelevanceScore() == null || productMinScore.get() == null) {
                    log.error("searchKnowledge[{}]: 商品相关性检测空指针, e={}, relevanceProducts={}, productMinScore={}", searchReq.getChatId(), JSON.toJSONString(e), JSON.toJSONString(relevanceProducts), productMinScore.get());
                }
                return e.getRelevanceScore() >= productMinScore.get();
            }).collect(Collectors.toList());
            usedProducts.sort(Comparator.comparingInt(KnowledgeSortResult::getRerankRank));
            relevanceProducts.sort((e1, e2) -> e2.getRelevanceScore().compareTo(e1.getRelevanceScore()));
        } else {
            usedKuList = kuTopNList;
            usedChunks = chunkTopNList;
            usedProducts = productTopNList;
        }
        // 查询documentInfo
        Map<Long, GetDocumentResponse> id2Document;
        List<Long> documentIds = usedChunks.stream().map(e -> e.getChunk().get(0).getDocumentId()).distinct().collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(documentIds)) {
            List<GetDocumentResponse> documents = kbApi.getDocuments(searchReq.getTenantId(), searchReq.getProjectId(), null, documentIds, null);
            id2Document = documents.stream().collect(Collectors.toMap(GetDocumentResponse::getId, e -> e, (e1, e2) -> e1));
        } else {
            id2Document = new HashMap<>();
        }
        // 限制知识最大的token数
        List<KnowledgeSortResult> allUsedKnowledge = new ArrayList<>();
        allUsedKnowledge.addAll(usedKuList);
        allUsedKnowledge.addAll(usedChunks);
        allUsedKnowledge.addAll(usedProducts);
        if (CollectionUtil.isNotEmpty(allUsedKnowledge)) {
            Function<KnowledgeSortResult, String> getKnowledgePromptFunc = knowledge -> {
                GetDocumentResponse documentInfo = knowledge.getChunk() == null ? null : id2Document.get(knowledge.getChunk().get(0).getDocumentId());
                return knowledge.getKnowledgePrompt(documentInfo);
            };
            allUsedKnowledge.sort(Comparator.comparingInt(KnowledgeSortResult::getRerankRank));
            int currentToken = TiktokenUtil.competeToken(allUsedKnowledge, getKnowledgePromptFunc);
            log.info("searchKnowledge[{}]: maxToken, currentToken={}, knowledgeIdInfos={}", searchReq.getChatId(), currentToken, allUsedKnowledge.stream().map(KnowledgeSortResult::getIdInfo).collect(Collectors.toList()));
            List<KnowledgeSortResult> cutKnowledgeList = new ArrayList<>(allUsedKnowledge);
            Integer knowledgeMaxToken = knowledgeSearchConfig.getKnowledgeMaxToken();
            while (currentToken > knowledgeMaxToken) {
                int firstRankKnowledgeToken = TiktokenUtil.competeToken(ListUtil.toList(cutKnowledgeList.get(0)), getKnowledgePromptFunc);
                int cutIndex;
                if (firstRankKnowledgeToken > knowledgeMaxToken) {
                    cutIndex = 0;
                    log.info("searchKnowledge[{}]: maxToken, 排序第一的知识[idInfo={},token={}]超过限制[token={}]，因此去除该知识", searchReq.getChatId(), cutKnowledgeList.get(0).getIdInfo(), firstRankKnowledgeToken, knowledgeMaxToken);
                } else {
                    cutIndex = cutKnowledgeList.size() - 1;
                    log.info("searchKnowledge[{}]: maxToken, 排序第一的知识[idInfo={},token={}]没超过限制[token={}]，因此去除排名最后的知识[idInfo={}]", searchReq.getChatId(), cutKnowledgeList.get(0).getIdInfo(), firstRankKnowledgeToken, knowledgeMaxToken, cutKnowledgeList.get(cutIndex).getIdInfo());
                }
                cutKnowledgeList.remove(cutIndex);
                currentToken = TiktokenUtil.competeToken(cutKnowledgeList, getKnowledgePromptFunc);
                log.info("searchKnowledge[{}]: maxToken, 去除知识后, currentToken={}, knowledgeIdInfos={}", searchReq.getChatId(), currentToken, cutKnowledgeList.stream().map(KnowledgeSortResult::getIdInfo).collect(Collectors.toList()));
            }
            // 重置qa的effect
            usedKuList.forEach(e -> e.getKu().setEffect(false));
            usedKuList = cutKnowledgeList.stream().filter(e -> e.getKu() != null).collect(Collectors.toList());
            usedChunks = cutKnowledgeList.stream().filter(e -> e.getChunk() != null).collect(Collectors.toList());
            usedProducts = cutKnowledgeList.stream().filter(e -> e.getProduct() != null).collect(Collectors.toList());
        }
        // 设置context
        if (CollectionUtil.isNotEmpty(usedKuList)) {
            usedKuList.forEach(e -> e.getKu().setEffect(true));
        }
        searchRes.getQaSearchContext()
                .setUsedKus(usedKuList.stream().map(KnowledgeSortResult::getKu).collect(Collectors.toList()))
                .setUsedKusCount(usedKuList.size());
        if (CollectionUtil.isNotEmpty(searchRes.getQaSearchContext().getRerankKus())) {
            searchRes.getQaSearchContext().getRerankKus().sort((e1, e2) -> {
//                // relevant score
//                if (e2.getRelevantScore() != null && e1.getRelevantScore() != null) {
//                    if (!e2.getRelevantScore().equals(e1.getRelevantScore())) {
//                        return e2.getRelevantScore().compareTo(e1.getRelevantScore());
//                    }
//                }
//                if (e1.getRelevantScore() != null && e2.getRelevantScore() == null) {
//                    return -1;
//                }
//                if (e2.getRelevantScore() != null && e1.getRelevantScore() == null) {
//                    return 1;
//                }
                // rerank score
                if (e2.getRerankScore() != null && e1.getRerankScore() != null) {
                    if (!e2.getRerankScore().equals(e1.getRerankScore())) {
                        return e2.getRerankScore().compareTo(e1.getRerankScore());
                    }
                }
                if (e1.getRerankScore() != null && e2.getRerankScore() == null) {
                    return -1;
                }
                if (e2.getRerankScore() != null && e1.getRerankScore() == null) {
                    return 1;
                }
                // es score
                return Float.compare(e2.getScore(), e1.getScore());
            });
            for (int i = 0; i < searchRes.getQaSearchContext().getRerankKus().size(); i++) {
                searchRes.getQaSearchContext().getRerankKus().get(i).setRank(i + 1);
            }
        }
        searchRes.getDocSearchContext()
                .setRerankResults(rerankChunks.stream().map(e -> new EsSearchResult<List<DocumentChunkEsEntity>>().setScore(e.getRerankScore()).setData(e.getChunk())).collect(Collectors.toList()))
                .setRelevanceResults(relevanceChunks.stream().map(e -> new EsSearchResult<List<DocumentChunkEsEntity>>().setScore(e.getRelevanceScore()).setData(e.getChunk())).collect(Collectors.toList()))
                .setUsedChunks(usedChunks.stream().map(KnowledgeSortResult::getChunk).collect(Collectors.toList()))
                .setUsedDocuments(usedChunks.stream().map(e -> id2Document.get(e.getChunk().get(0).getDocumentId())).collect(Collectors.toList()));
        searchRes.getProductSearchContext()
                .setRerankResults(MapUtil.of("all", rerankProducts.stream().map(e -> new EsSearchResult<ProductEsEntity>().setScore(e.getRerankScore()).setData(e.getProduct())).collect(Collectors.toList())))
                .setRelevanceResults(MapUtil.of("all", relevanceProducts.stream().map(e -> new EsSearchResult<ProductEsEntity>().setScore(e.getRelevanceScore()).setData(e.getProduct())).collect(Collectors.toList())))
                .setUsedProducts(usedProducts.stream().map(KnowledgeSortResult::getProduct).collect(Collectors.toList()));
        // 组装prompt
        StringBuilder contextContentBuilder = new StringBuilder();
        if ("greeting".equalsIgnoreCase(searchReq.getIntent()) || "Customer Say Good".equalsIgnoreCase(searchReq.getIntent())
                || "greeting".equalsIgnoreCase(searchReq.getOriginIntent()) || "Customer Say Good".equalsIgnoreCase(searchReq.getOriginIntent())
        ) {
            contextContentBuilder.append(String.format("\n%s\n", searchReq.getAiReplyPromptInfo().getRoleDescription()));
        }
        if (CollectionUtil.isNotEmpty(usedKuList)) {
            for (KnowledgeSortResult ku : usedKuList) {
                contextContentBuilder.append(ku.getKuPrompt());
            }
        }
        if (CollectionUtil.isNotEmpty(usedChunks)) {
            for (KnowledgeSortResult usedChunk : usedChunks) {
                Long documentId = usedChunk.getChunk().get(0).getDocumentId();
                GetDocumentResponse document = id2Document.get(documentId);
                contextContentBuilder.append(usedChunk.getDocumentPrompt(document));
            }
        }
        if (CollectionUtil.isNotEmpty(usedProducts)) {
            for (KnowledgeSortResult product : usedProducts) {
                contextContentBuilder.append(product.getProductPrompt());
            }
        }
        String contextContent = contextContentBuilder.toString();
        String systemContent = promptConfig.getSystemContent(searchReq.getTenantId(), searchReq.getAiReplyPromptInfo(), contextContent, searchReq.getLanguage().name(), searchReq.getWordsLimit(), searchReq.getModelType());
        searchRes.setHighSimilarityKuResponse(highSimilarityKuResponse)
                .setContextContent(contextContent)
                .setSystemContent(systemContent)
                .setUsages(usages);
        log.info("searchKnowledge[{}]: tokenCount contextContentToken={} systemContentToken={}", searchReq.getChatId(), TiktokenUtil.size(contextContent), TiktokenUtil.size(systemContent));
        return searchRes;
    }

    public <T> CompletableFuture<List<EsSearchResult<T>>> hybridSearch(String chatId,
                                                                       Integer size,
                                                                       String indexName,
                                                                       List<Pair<String, Object>> filterFieldValues,
                                                                       String embeddingField,
                                                                       List<BigDecimal> embedding,
                                                                       List<String> keywords,
                                                                       String intent,
                                                                       Map<String, Float> matchField2Boost,
                                                                       Float intentBoost,
                                                                       Class<T> esEntityClass,
                                                                       QaChatConfig.KnowledgeSearchConfig knowledgeSearchConfig,
                                                                       Consumer<BoolQueryBuilder> boolQueryBuilderConsumer) {
        if (size == null || size < 0) {
            size = 5;
        }
        return esSearchService.embeddingSearchAsync(
                chatId,
                indexName,
                embedding,
                embeddingField,
                size,
                knowledgeSearchConfig.getEsTextScoreMax(),
                knowledgeSearchConfig.getEsTextScoreMin(),
                knowledgeSearchConfig.getEmbeddingScaleMax(),
                knowledgeSearchConfig.getEmbeddingScaleMin(),
                boolQueryBuilder -> {
                    if (boolQueryBuilderConsumer != null) {
                        boolQueryBuilderConsumer.accept(boolQueryBuilder);
                    }
                    EsSearchService.addTermQueryToBuilder(filterFieldValues, boolQueryBuilder);
                    if (CollectionUtil.isNotEmpty(matchField2Boost)) {
                        if (CollectionUtil.isNotEmpty(keywords)) {
                            for (String keyword : keywords) {
                                matchField2Boost.forEach((field, boost) -> {
                                    MatchQueryBuilder matchQueryBuilder = new MatchQueryBuilder(field, keyword);
                                    if (boost != null) {
                                        matchQueryBuilder.boost(boost);
                                    }
                                    boolQueryBuilder.should(matchQueryBuilder);
                                });
                            }
                        }
                        if (StringUtils.hasText(intent)) {
                            matchField2Boost.forEach((field, boost) -> {
                                MatchQueryBuilder matchQueryBuilder = new MatchQueryBuilder(field, intent);
                                if (intentBoost != null) {
                                    matchQueryBuilder.boost(intentBoost);
                                }
                                boolQueryBuilder.should(matchQueryBuilder);
                            });
                        }
                    }
                    boolQueryBuilder.minimumShouldMatch(0);
                },
                EsService.getEmbeddingFieldNames(esEntityClass),
                esEntityClass
        );
    }

    @Data
    @Accessors(chain = true)
    public static class KnowledgeSortResult {
        int rerankRank;
        double rerankScore;
        Double relevanceScore;
        // 一个对象只会有一种知识存在
        RetrievedKu ku;
        List<DocumentChunkEsEntity> chunk;
        ProductEsEntity product;

        public String getIdInfo() {
            if (ku != null) {
                return String.format("ku[id=%s]", ku.getKuId());
            }
            if (chunk != null) {
                return String.format("document[id=%s]", chunk.get(0).getDocumentId());
            }
            if (product != null) {
                return String.format("product[id=%s]", product.getId());
            }
            throw new RuntimeException("KnowledgeSortResult: impossible condition");
        }

        public String getKnowledgePrompt(GetDocumentResponse documentResponse) {
            if (ku != null) {
                return getKuPrompt();
            }
            if (chunk != null) {
                return getDocumentPrompt(documentResponse);
            }
            if (product != null) {
                return getProductPrompt();
            }
            throw new RuntimeException("KnowledgeSortResult: impossible condition");
        }

        public String getKuPrompt() {
            return String.format("\n<chunk>\nQuestion: %s\nAnswer: %s\n</chunk>\n", ku.getTitle(), ku.getContent());
        }

        public String getDocumentPrompt(GetDocumentResponse documentResponse) {
            String chunkContents = chunk.stream().map(DocumentChunkEsEntity::getContent).collect(Collectors.joining("\n"));
            String docTitle = documentResponse == null ? "Document" : documentResponse.getFileName();
            return String.format("\n<chunk>\n## %s\n\n### Content\n\n%s\n</chunk>\n", docTitle, chunkContents);
        }

        public String getProductPrompt() {
            String title = product.getTitle();
            String spuUrl = product.getSpuUrl();
            String describe = product.getDescribe();
            Double price = null;
            if (CollectionUtil.isNotEmpty(product.getSkuList())) {
                for (ProductEsEntity.Sku sku : product.getSkuList()) {
                    if (sku.getPrice() != null && sku.getPrice().doubleValue() > 0) {
                        if (price == null || price > sku.getPrice().doubleValue()) {
                            price = sku.getPrice().doubleValue();
                        }
                    }
                }
            }
            return String.format("\n<chunk>\n## %s\n\n### Product Detail Page\n%s\n\n### Product Price\n%s%s\n\n### Product Description\n%s\n</chunk>\n", title, spuUrl, "$", price, describe);
        }
    }
}
