package com.shulex.qabot.util;

import org.springframework.util.StopWatch;

public class StopWatchUtil {
    public static String summary(StopWatch stopWatch) {
        StringBuilder sb = new StringBuilder("StopWatch '" + stopWatch.getId() + "': running time = " + stopWatch.getTotalTimeSeconds() + " s");
        for (StopWatch.TaskInfo task : stopWatch.getTaskInfo()) {
            sb.append("; [").append(task.getTaskName()).append("] took ").append(task.getTimeSeconds()).append(" s");
            long percent = Math.round(100.0 * task.getTimeNanos() / stopWatch.getTotalTimeNanos());
            sb.append(" = ").append(percent).append('%');
        }
        return sb.toString();
    }
}
