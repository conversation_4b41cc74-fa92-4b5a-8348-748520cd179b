package com.shulex.qabot.client.dto;

import com.shulex.gpt.apisdk.dto.LLMUsages;
import com.shulex.gpt.apisdk.dto.Message;
import com.shulex.gpt.apisdk.enums.BotLanguageEnum;
import com.shulex.qabot.client.dto.esentity.LabelSampleEsEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
@Accessors(chain = true)
public class SearchLabelSampleDTO {
    LLMUsages usages;
    Map<String, List<EsSearchResult<LabelSampleEsEntity>>> searchResults;
    Map<String, String> userEnQueryMap = new HashMap<>();
}