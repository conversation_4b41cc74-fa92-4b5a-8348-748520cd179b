package com.shulex.qabot.feign.voc;

import cn.hutool.cache.CacheUtil;
import cn.hutool.cache.impl.TimedCache;
import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.shulex.qabot.config.VocClientConfig;
import com.shulex.qabot.util.AsyncHttpUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.common.protocol.types.Field;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class VocHttpClient {
    @Autowired
    VocClientConfig vocClientConfig;
    TimedCache<Long, String> tenantVersionCache = CacheUtil.newTimedCache(1000 * 60 * 3);

    public String queryTenantVersion(String chatId, long tenantId) {
        try {
            String version = tenantVersionCache.get(tenantId, false, () -> {
                String url = String.format("%s/category/userAvailableCount/inner?group=GPT&accountId=%s", vocClientConfig.getHost(), tenantId);
                JSONObject res = AsyncHttpUtil.get(url, MapUtil.of("Allow-Token", vocClientConfig.getToken()), new TypeReference<JSONObject>() {
                }).join();
                log.info("queryTenantVersion[{}]: url={}, res={}", chatId, url, res);
                if (res != null) {
                    JSONObject data = res.getJSONObject("data");
                    if (data != null) {
                        return data.getString("subscribeType");
                    }
                }
                return null;
            });
            log.info("queryTenantVersion[{}]: tenantId={}, version={}", chatId, tenantId, version);
            if (version == null) {
                tenantVersionCache.remove(tenantId);
            }
            return version;
        } catch (Exception e) {
            log.error("queryTenantVersion[{}]: query error, so use default version=BASIC.", chatId, e);
            return "BASIC";
        }
    }
}
