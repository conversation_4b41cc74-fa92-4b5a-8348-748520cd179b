package com.shulex.qabot.kafka;

import cn.hutool.core.exceptions.ExceptionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.otter.canal.client.CanalMessageDeserializer;
import com.alibaba.otter.canal.protocol.CanalEntry;
import com.alibaba.otter.canal.protocol.Message;
import com.shulex.common.util.utils.EmptyUtil;
import com.shulex.qabot.alg.enmus.agent.enums.DmsOperateEnum;
import com.shulex.qabot.client.dto.DmsMessage;
import com.shulex.qabot.client.dto.consts.KafkaTopic;
import com.shulex.qabot.client.dto.esentity.DocumentChunkEsEntity;
import com.shulex.qabot.config.NoticeConfig;
import com.shulex.qabot.client.dto.mysqlentity.DocumentChunk;
import com.shulex.qabot.es.EsService;
import com.shulex.qabot.mysql.repository.DocumentChunkRepository;
import com.shulex.qabot.service.DocumentChunkService;
import com.shulex.qabot.util.JsonUtil;
import com.shulex.qabot.util.canal.FlatMessage;
import com.shulex.qabot.util.canal.MessageUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.bootstrap.encrypt.KeyProperties;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
public class DocumentChunkBinlogConsumer {
    @Autowired
    NoticeConfig noticeConfig;
    @Autowired
    DocumentChunkService documentChunkService;
    @Autowired
    DocumentChunkRepository documentChunkRepository;
    @Autowired
    EsService esService;



    @KafkaListener(topics = {KafkaTopic.DMS_DOCUMENT_CHUNK_TOPIC}, groupId = KafkaTopic.DMS_GROUP, concurrency = "2", properties = {"max.poll.records=5"})
    public void dmsDocumentChunkListener(List<byte[]> records, Acknowledgment ack) {
        List<DmsMessage> dmsMessages = null;
        try {
            dmsMessages = records.stream()
                    .map(dat -> JSON.parseObject(new String(dat), DmsMessage.class))
                    .collect(Collectors.toList());
            log.info("dmsDocumentChunkListener recordsSize={}, msgSize={}, msgList:\n{}", records.size(), dmsMessages.size(), dmsMessages.stream().map(JSON::toJSONString).collect(Collectors.joining("\n")));
            List<DocumentChunk> insertDocumentChunkList = new ArrayList<>();
            List<DocumentChunk> deleteDocumentChunkList = new ArrayList<>();
            Map<Boolean, List<DocumentChunk>> needReEmbedding2UpdateDocumentChunkList = new HashMap<>();
            needReEmbedding2UpdateDocumentChunkList.put(true, new ArrayList<>());
            needReEmbedding2UpdateDocumentChunkList.put(false, new ArrayList<>());

            Map<String, DocumentChunkEsEntity> chunkEsEntityHashMap = new HashMap<>();
            List<String> idList = dmsMessages.stream().filter(p -> p != null && p.getData() != null && p.getData().get("id") != null).map(p -> p.getData().get("id").toString()).distinct().collect(Collectors.toList());
            if (EmptyUtil.isNotEmpty(idList)){
                chunkEsEntityHashMap = esService.getByIds(DocumentChunkEsEntity.indexName, idList, DocumentChunkEsEntity.class, EsService.getEmbeddingFieldNames(DocumentChunkEsEntity.class));
            }

            for (DmsMessage flatMessage : dmsMessages) {
                String operation = flatMessage.getMetadata().getOperation();
                Map<String, Object> data = flatMessage.getData();
                if (data != null) {
                    JSONObject jsonObject = JSON.parseObject(JSON.toJSONString(data));
                    for (String key : jsonObject.keySet()) {
                        Object value = jsonObject.get(key);
                        if (value instanceof String && JsonUtil.isJsonArray(value.toString())) {
                            try {
                                jsonObject.put(key, JSON.parseArray(value.toString()));
                            } catch (Exception e) {
                                log.warn("documentChunkBinlogListener parse json warning: key={}, value={}", key, value, e);
                            }
                        }
                    }
                    DocumentChunk documentChunk = jsonObject.toJavaObject(DocumentChunk.class);
                    if (DmsOperateEnum.INSERT.getValue().equals(operation)) {
                        insertDocumentChunkList.add(documentChunk);
                    } else if (DmsOperateEnum.DELETE.getValue().equals(operation)) {
                        deleteDocumentChunkList.add(documentChunk);
                    } else if (DmsOperateEnum.UPDATE.getValue().equals(operation)) {
                        //先查出老数据
                        String chunkIdStr = documentChunk.getId().toString();
                        if (chunkEsEntityHashMap.containsKey(chunkIdStr)) {
                            DocumentChunkEsEntity documentChunkEsEntity = chunkEsEntityHashMap.get(chunkIdStr);
                            if (documentChunkEsEntity != null && documentChunkEsEntity.getContent() != null && !documentChunkEsEntity.getContent().equals(documentChunk.getContent())) {
                                needReEmbedding2UpdateDocumentChunkList.get(true).add(documentChunk);
                            }else {
                                needReEmbedding2UpdateDocumentChunkList.get(false).add(documentChunk);
                            }
                        }
                    }
                }
            }

            // 暂时去除274806L
//            insertDocumentChunkList = insertDocumentChunkList.stream().filter(p->!p.getTenantId().equals(274806L)).collect(Collectors.toList());

            documentChunkService.insertDocumentChunkToEs(insertDocumentChunkList);
            needReEmbedding2UpdateDocumentChunkList.forEach((needReEmbedding, updateDocumentChunkList) -> {
                documentChunkService.updateDocumentChunkInEs(updateDocumentChunkList, needReEmbedding);
            });
            documentChunkService.deleteDocumentChunkInEs(deleteDocumentChunkList);
            ack.acknowledge();
        } catch (Exception e) {
            log.error("dmsDocumentChunkListener error, flatMessages={}, ", JSON.toJSONString(dmsMessages), e);
            noticeConfig.sendError("dmsDocumentChunkListener\n" + ExceptionUtil.stacktraceToString(e));
            ack.nack(0, 5000L);
        }
    }


    @KafkaListener(topics = {"document-chunk-sync-topic"}, groupId = "qa-bot-group", concurrency = "2", properties = {"max.poll.records=5"})
    public void documentChunkBinlogListener(List<byte[]> records, Acknowledgment ack) {
        List<FlatMessage> flatMessages = null;
        try {
            flatMessages = records.stream().flatMap(record -> {
                Message message = CanalMessageDeserializer.deserializer(record);
                return MessageUtil.convert(message).stream();
            }).collect(Collectors.toList());
            log.info("documentChunkBinlogListener recordsSize={}, msgSize={}, msgList:\n{}", records.size(), flatMessages.size(), flatMessages.stream().map(JSON::toJSONString).collect(Collectors.joining("\n")));
            List<DocumentChunk> insertDocumentChunkList = new ArrayList<>();
            List<DocumentChunk> deleteDocumentChunkList = new ArrayList<>();
            Map<Boolean, List<DocumentChunk>> needReEmbedding2UpdateDocumentChunkList = new HashMap<>();
            needReEmbedding2UpdateDocumentChunkList.put(true, new ArrayList<>());
            needReEmbedding2UpdateDocumentChunkList.put(false, new ArrayList<>());
            for (FlatMessage flatMessage : flatMessages) {
                String type = flatMessage.getType();
                List<Map<String, Object>> dataList = flatMessage.getData();
                List<DocumentChunk> documentChunkList = dataList == null ? new ArrayList<>() :
                        dataList.stream().map(data -> {
                            JSONObject jsonObject = JSON.parseObject(JSON.toJSONString(data));
                            for (String key : jsonObject.keySet()) {
                                Object value = jsonObject.get(key);
                                if (value instanceof String && JsonUtil.isJsonArray(value.toString())) {
                                    try {
                                        jsonObject.put(key, JSON.parseArray(value.toString()));
                                    } catch (Exception e) {
                                        log.warn("documentChunkBinlogListener parse json warning: key={}, value={}", key, value, e);
                                    }
                                }
                            }
                            return jsonObject.toJavaObject(DocumentChunk.class);
                        }).collect(Collectors.toList());
                if (type.equals(CanalEntry.EventType.INSERT.name())) {
                    insertDocumentChunkList.addAll(documentChunkList);
                } else if (type.equals(CanalEntry.EventType.DELETE.name())) {
                    deleteDocumentChunkList.addAll(documentChunkList);
                } else if (type.equals(CanalEntry.EventType.UPDATE.name())) {
                    List<Map<String, Object>> old = flatMessage.getOld();
                    for (int i = 0; i < documentChunkList.size(); i++) {
                        DocumentChunk newDocumentChunk = documentChunkList.get(i);
                        DocumentChunk oldDocumentChunk = JSON.parseObject(JSON.toJSONString(old.get(i)), DocumentChunk.class);
                        boolean needEmbedding = oldDocumentChunk.getContent() != null;
                        needReEmbedding2UpdateDocumentChunkList.get(needEmbedding).add(newDocumentChunk);
                    }
                }
            }

            // 暂时去除274806L
//            insertDocumentChunkList = insertDocumentChunkList.stream().filter(p->!p.getTenantId().equals(274806L)).collect(Collectors.toList());

            documentChunkService.insertDocumentChunkToEs(insertDocumentChunkList);
            needReEmbedding2UpdateDocumentChunkList.forEach((needReEmbedding, updateDocumentChunkList) -> {
                documentChunkService.updateDocumentChunkInEs(updateDocumentChunkList, needReEmbedding);
            });
            documentChunkService.deleteDocumentChunkInEs(deleteDocumentChunkList);
            ack.acknowledge();
        } catch (Exception e) {
            log.error("documentChunkBinlogListener error, flatMessages={}, ", JSON.toJSONString(flatMessages), e);
            noticeConfig.sendError("documentChunkBinlogListener\n" + ExceptionUtil.stacktraceToString(e));
            ack.nack(0, 5000L);
        }
    }
}
