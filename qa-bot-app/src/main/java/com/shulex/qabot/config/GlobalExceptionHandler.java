package com.shulex.qabot.config;

import cn.hutool.core.exceptions.ExceptionUtil;
import com.alibaba.fastjson.JSON;
import com.shulex.common.util.exception.BusinessException;
import com.shulex.common.util.utils.Result;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.util.ContentCachingRequestWrapper;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.Reader;
import java.util.UUID;

import static java.lang.String.format;

@ControllerAdvice
@Slf4j
public class GlobalExceptionHandler {
    @Autowired
    NoticeConfig noticeConfig;

    @SneakyThrows
    @ExceptionHandler(Exception.class)
    public void exceptionHandler(HttpServletRequest req, HttpServletResponse res, Exception exception, HandlerMethod handlerMethod) {
        String traceId = UUID.randomUUID().toString();
        ContentCachingRequestWrapper wrapperReq = (ContentCachingRequestWrapper) req;
        String method = wrapperReq.getMethod();
        String requestURI = wrapperReq.getRequestURI();
        String queryString = wrapperReq.getQueryString() == null ? "" : wrapperReq.getQueryString();
        String reqBody = new String(wrapperReq.getContentAsByteArray(), wrapperReq.getCharacterEncoding());
        String requestInfo = format("RequestPath: [%s]%s?%s\nRequestBody: %s", method, requestURI, queryString, reqBody);
        boolean needDingNotice = true;
        if (exception instanceof BusinessException) {
            BusinessException businessException = (BusinessException) exception;
            if (!businessException.isSilent()) {
                log.error("traceId={}, requestInfo={}", traceId, requestInfo, businessException);
            }
            if (businessException.isSilent()) {
                needDingNotice = false;
            }
        } else {
            log.error("traceId={}, requestInfo={}", traceId, requestInfo, exception);
        }
        Result<String> result = new Result<>(500, format("traceId=%s, exceptionMsg=%s", traceId, exception.getMessage()));
        res.setStatus(500);
        res.getWriter().write(JSON.toJSONString(result));
        if (needDingNotice) {
            noticeConfig.sendError("GlobalExceptionHandler\n" + format("traceId: %s\n%s\n\nException: %s", traceId, requestInfo, ExceptionUtil.stacktraceToString(exception)));
        }
    }
}
