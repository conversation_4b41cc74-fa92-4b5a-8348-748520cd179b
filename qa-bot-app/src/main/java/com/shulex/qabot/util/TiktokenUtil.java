package com.shulex.qabot.util;


import com.knuddels.jtokkit.Encodings;
import com.knuddels.jtokkit.api.Encoding;
import com.knuddels.jtokkit.api.EncodingRegistry;
import com.knuddels.jtokkit.api.EncodingType;
import com.knuddels.jtokkit.api.IntArrayList;

import java.util.List;
import java.util.function.Function;

public class TiktokenUtil {

    private static final EncodingRegistry registry;

    static {
        registry = Encodings.newDefaultEncodingRegistry();
    }

    private TiktokenUtil() {

    }

    private static Encoding getEncoding() {
        return registry.getEncoding(EncodingType.CL100K_BASE);
    }

    public static IntArrayList encode(String text) {
        return getEncoding().encode(text);
    }

    public static int size(String text) {
        return encode(text).size();
    }

    public static <T> int competeToken(List<T> dataList, Function<T, String> getDataPromptFunc) {
        int totalToken = 0;
        for (T t : dataList) {
            String prompt = getDataPromptFunc.apply(t);
            int tokenSize = TiktokenUtil.size(prompt);
            totalToken += tokenSize;
        }
        return totalToken;
    }

    public static void main(String[] args) {
        int size = TiktokenUtil.size("");
        System.out.println(size);
    }
}
