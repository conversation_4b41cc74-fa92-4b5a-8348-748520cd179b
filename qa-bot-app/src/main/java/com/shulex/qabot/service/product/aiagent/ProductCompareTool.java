package com.shulex.qabot.service.product.aiagent;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.shulex.qabot.client.dto.EsSearchResult;
import com.shulex.qabot.es.EsSearchService;
import com.shulex.qabot.client.dto.esentity.ProductEsEntity;
import com.shulex.qabot.es.dto.ProductSearchParam;
import lombok.Data;
import lombok.experimental.Accessors;
import org.elasticsearch.action.search.SearchResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class ProductCompareTool extends AiAgentTool {
    @Autowired
    EsSearchService esSearchService;

    public ProductCompareTool() {
        super("product_compare");
    }

    @Override
    public List<ProductEsEntity> findProduct(JSONObject toolInput) {
        ToolInput input = toolInput.toJavaObject(ToolInput.class);
        List<String> productNames = input.getProduct_names();
        if (CollectionUtil.isEmpty(productNames)) {
            return new ArrayList<>();
        }
        SearchResponse searchResponse = esSearchService.productSearch(new ProductSearchParam()
                .setSize(5)
                .setTenantId(input.getTenantId())
                .setProjectId(input.getProjectId())
                .setTitleList(productNames)
        ).join();
        List<EsSearchResult<ProductEsEntity>> esResults = esSearchService.searchResponseToEsResults(searchResponse, ProductEsEntity.class);
        return esResults.stream().map(EsSearchResult::getData).collect(Collectors.toList());
    }


    @Data
    @Accessors(chain = true)
    public static class ToolInput {
        long tenantId;
        long projectId;
        List<String> product_names;
    }
}
