package com.shulex.qabot.client.dto.esentity;

import com.shulex.gpt.apisdk.dto.MixedQuery;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@Data
@Accessors(chain = true)
public class IntentSampleEsEntity {
    public static final String indexName = "qa-intent-sample";

    String chatId;
    Boolean pnLabel;
    Long tenantId;
    Long projectId;
    String firstTurnTitle;
    String firstTurnContent;
    String firstTurnReply;
    Long firstTurnIntentId;
    MixedQuery firstTurnMixedQuery;
    Map<String, List<Object>> fistTurnEntities;
    Long intentId;
    @EsEmbeddingField
    private List<BigDecimal> titleContentEmbeddingCohere;
    @EsEmbeddingField
    private List<BigDecimal> summaryUserEnQueryEmbeddingCohere;

    public String generateId() {
        return String.format("%s:%s", chatId, pnLabel);
    }
}
