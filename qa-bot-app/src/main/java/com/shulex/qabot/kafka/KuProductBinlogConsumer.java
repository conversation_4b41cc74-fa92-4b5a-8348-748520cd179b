package com.shulex.qabot.kafka;

import cn.hutool.core.exceptions.ExceptionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.otter.canal.client.CanalMessageDeserializer;
import com.alibaba.otter.canal.protocol.CanalEntry;
import com.alibaba.otter.canal.protocol.Message;
import com.shulex.common.util.utils.EmptyUtil;
import com.shulex.qabot.alg.enmus.agent.enums.DmsOperateEnum;
import com.shulex.qabot.client.dto.DmsMessage;
import com.shulex.qabot.client.dto.consts.KafkaTopic;
import com.shulex.qabot.client.dto.esentity.KuEsEntity;
import com.shulex.qabot.client.dto.esentity.ProductChunkEsEntity;
import com.shulex.qabot.client.dto.esentity.ProductEsEntity;
import com.shulex.qabot.client.dto.mysqlentity.KuProduct;
import com.shulex.qabot.client.dto.mysqlentity.ProductChunk;
import com.shulex.qabot.config.NoticeConfig;
import com.shulex.qabot.es.EsService;
import com.shulex.qabot.service.product.ProductService;
import com.shulex.qabot.util.JsonUtil;
import com.shulex.qabot.util.canal.FlatMessage;
import com.shulex.qabot.util.canal.MessageUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.time.ZonedDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Component
@Slf4j
public class KuProductBinlogConsumer {
    @Autowired
    ProductService productService;
    @Autowired
    NoticeConfig noticeConfig;
    @Autowired
    EsService esService;

    @KafkaListener(topics = {KafkaTopic.DMS_KU_PRODUCT_TOPIC}, groupId = KafkaTopic.DMS_GROUP, concurrency = "2", properties = {"max.poll.records=5"})
    public void dmsKuProductListener(List<byte[]> records, Acknowledgment ack) {
        List<DmsMessage> dmsMessages = null;
        try {
            dmsMessages = records.stream()
                    .map(dat -> JSON.parseObject(new String(dat), DmsMessage.class))
                    .collect(Collectors.toList());
            log.info("dmsKuProductListener recordsSize={}, msgSize={}, msgList:\n{}", records.size(), dmsMessages.size(), dmsMessages.stream().map(JSON::toJSONString).collect(Collectors.joining("\n")));
            List<KuProduct> insertKuProductList = new ArrayList<>();
            List<KuProduct> deleteKuProductList = new ArrayList<>();
            Map<Boolean, List<KuProduct>> updateKuProductsMap = new HashMap<>();
            updateKuProductsMap.put(true, new ArrayList<>());
            updateKuProductsMap.put(false, new ArrayList<>());
            Map<String, ProductEsEntity> chunkEsEntityHashMap = new HashMap<>();
            List<String> idList = dmsMessages.stream().filter(p -> p != null && p.getData() != null && p.getData().get("id") != null).map(p -> p.getData().get("id").toString()).distinct().collect(Collectors.toList());
                if (EmptyUtil.isNotEmpty(idList)){
                    chunkEsEntityHashMap = esService.getByIds(ProductEsEntity.indexName, idList, ProductEsEntity.class, EsService.getEmbeddingFieldNames(ProductEsEntity.class));
            }
            for (DmsMessage flatMessage : dmsMessages) {
                String operation = flatMessage.getMetadata().getOperation();
                Map<String, Object> data = flatMessage.getData();
                if (data != null) {
                    Set<String> keys = data.keySet();
                    for (String key : keys) {
                        Object value = data.get(key);
                        if (value instanceof String) {
                            String stringValue = (String) value;
                            if (JsonUtil.isJsonObject(stringValue)) {
                                data.put(key, JSON.parseObject(stringValue));
                            } else if (JsonUtil.isJsonArray(stringValue)) {
                                data.put(key, JSON.parseArray(stringValue));
                            }
                        }
                    }
                    Object sku_list_obj = data.get("sku_list");
                    if (sku_list_obj instanceof JSONArray) {
                        JSONArray skuList = (JSONArray) sku_list_obj;
                        for (Object o : skuList) {
                            if (o instanceof JSONObject) {
                                JSONObject sku = (JSONObject) o;
                                String createdAt = sku.getString("createdAt");
                                if (createdAt != null) {
                                    try {
                                        ZonedDateTime parse = ZonedDateTime.parse(createdAt);
                                        sku.put("createdAt", parse.toLocalDateTime().toString());
                                    } catch (Exception e) {

                                    }
                                }
                            }
                        }
                    } else {
                        log.warn("kuProductBinlogListener: ignore invalid json array sku_list, flatMessage={}, data={}", JSON.toJSONString(flatMessage), JSON.toJSONString(data));
                        data.put("sku_list", null);
                    }
                    KuProduct mysqlKuProduct = JSON.parseObject(JSON.toJSONString(data), KuProduct.class);
                    if (DmsOperateEnum.INSERT.getValue().equals(operation)) {
                        insertKuProductList.add(mysqlKuProduct);
                    } else if (DmsOperateEnum.DELETE.getValue().equals(operation)){
                        deleteKuProductList.add(mysqlKuProduct);
                    } else if (DmsOperateEnum.UPDATE.getValue().equals(operation)) {
                        String mysqlProductIdStr = mysqlKuProduct.getId().toString();
                        if (chunkEsEntityHashMap.containsKey(mysqlProductIdStr)) {
                            ProductEsEntity productEsEntity = chunkEsEntityHashMap.get(mysqlProductIdStr);
                            if (productEsEntity != null && ( (productEsEntity.getCategory() != null && !productEsEntity.getCategory().equals(mysqlKuProduct.getCategory()))
                                    || (productEsEntity.getTitle() != null && !productEsEntity.getTitle().equals(mysqlKuProduct.getTitle()))
                                    || (productEsEntity.getDescribe() != null && !productEsEntity.getDescribe().equals(mysqlKuProduct.getDescribe()) ))) {
                                updateKuProductsMap.get(true).add(mysqlKuProduct);
                            }else {
                                updateKuProductsMap.get(false).add(mysqlKuProduct);
                            }
                        }
                    }
                }
            }
            productService.insertKuProductToEs(insertKuProductList);
            updateKuProductsMap.forEach((needEmbedding, kuProducts) -> {
                productService.updateKuProductInEs(kuProducts, needEmbedding);
            });
            productService.deleteKuProductInEs(deleteKuProductList);
            ack.acknowledge();
        } catch (Exception e) {
            log.error("kuProductBinlogListener error, flatMessages={}, ", JSON.toJSONString(dmsMessages), e);
            noticeConfig.sendError("kuProductBinlogListener\n" + ExceptionUtil.stacktraceToString(e));
            ack.nack(0, 5000L);
        }
    }

    @KafkaListener(topics = {"kafka-default-topic"}, groupId = "kafka-default-topic-group", concurrency = "2", properties = {"max.poll.records=1"})
    public void kafkaDefaultTopicListener(List<byte[]> records, Acknowledgment ack) {
        List<DmsMessage> dmsMessages = null;
        try {
            dmsMessages = records.stream()
                    .map(dat -> JSON.parseObject(new String(dat), DmsMessage.class))
                    .collect(Collectors.toList());
            log.info("kafkaDefaultTopicListener recordsSize={}, msgSize={}, msgList:\n{}", records.size(), dmsMessages.size(), dmsMessages.stream().map(JSON::toJSONString).collect(Collectors.joining("\n")));
            ack.acknowledge();
        }catch (Exception e){
            log.error("kafkaDefaultTopicListener error, flatMessages={}, ", JSON.toJSONString(dmsMessages), e);
            noticeConfig.sendError("kafkaDefaultTopicListener\n" + ExceptionUtil.stacktraceToString(e));
            ack.nack(0, 5000L);
        }
    }

    @KafkaListener(topics = {KafkaTopic.DMS_PRODUCT_CHUNK_TOPIC}, groupId = KafkaTopic.DMS_GROUP, concurrency = "2", properties = {"max.poll.records=1"})
    public void dmsProductChunkListener(List<byte[]> records, Acknowledgment ack) {
        List<DmsMessage> dmsMessages = null;
        try {
            dmsMessages = records.stream()
                    .map(dat -> JSON.parseObject(new String(dat), DmsMessage.class))
                    .collect(Collectors.toList());
            log.info("dmsProductChunkListener recordsSize={}, msgSize={}, msgList:\n{}", records.size(), dmsMessages.size(), dmsMessages.stream().map(JSON::toJSONString).collect(Collectors.joining("\n")));
            List<ProductChunk> insertProductChunkList = new ArrayList<>();
            List<ProductChunk> deleteProductChunkList = new ArrayList<>();
            Map<Boolean, List<ProductChunk>> updateProductChunksMap = new HashMap<>();
            updateProductChunksMap.put(true, new ArrayList<>());
            updateProductChunksMap.put(false, new ArrayList<>());
            Map<String, ProductChunkEsEntity> chunkEsEntityHashMap = new HashMap<>();
            List<String> idList = dmsMessages.stream().filter(p -> p != null && p.getData() != null && p.getData().get("id") != null).map(p -> p.getData().get("id").toString()).distinct().collect(Collectors.toList());
            if (EmptyUtil.isNotEmpty(idList)){
                chunkEsEntityHashMap = esService.getByIds(ProductChunkEsEntity.indexName, idList, ProductChunkEsEntity.class, EsService.getEmbeddingFieldNames(ProductChunkEsEntity.class));
            }
            for (DmsMessage flatMessage : dmsMessages) {
                String operation = flatMessage.getMetadata().getOperation();
                Map<String, Object> data = flatMessage.getData();
                if (data != null){
                    Set<String> keys = data.keySet();
                    for (String key : keys) {
                        Object value = data.get(key);
                        if (value instanceof String) {
                            String stringValue = (String) value;
                            if (JsonUtil.isJsonObject(stringValue)) {
                                data.put(key, JSON.parseObject(stringValue));
                            } else if (JsonUtil.isJsonArray(stringValue)) {
                                data.put(key, JSON.parseArray(stringValue));
                            }
                        }
                    }
                    Object sku_list_obj = data.get("sku_list");
                    if (sku_list_obj instanceof JSONArray) {
                        JSONArray skuList = (JSONArray) sku_list_obj;
                        for (Object o : skuList) {
                            if (o instanceof JSONObject) {
                                JSONObject sku = (JSONObject) o;
                                String createdAt = sku.getString("createdAt");
                                if (createdAt != null) {
                                    try {
                                        ZonedDateTime parse = ZonedDateTime.parse(createdAt);
                                        sku.put("createdAt", parse.toLocalDateTime().toString());
                                    } catch (Exception e) {

                                    }
                                }
                            }
                        }
                    }
                    ProductChunk mysqlProductChunk = JSON.parseObject(JSON.toJSONString(data), ProductChunk.class);
                    if (DmsOperateEnum.INSERT.getValue().equals(operation)) {
                        insertProductChunkList.add(mysqlProductChunk);
                    } else if (DmsOperateEnum.DELETE.getValue().equals(operation)) {
                        deleteProductChunkList.add(mysqlProductChunk);
                    } else if (DmsOperateEnum.UPDATE.getValue().equals(operation)) {
                        String mysqlProductIdStr = mysqlProductChunk.getId().toString();
                        if (chunkEsEntityHashMap.containsKey(mysqlProductIdStr)) {
                            ProductChunkEsEntity productChunkEsEntity = chunkEsEntityHashMap.get(mysqlProductIdStr);
                            if (productChunkEsEntity != null && productChunkEsEntity.getContent() != null && !productChunkEsEntity.getContent().equals(mysqlProductChunk.getContent())) {
                                updateProductChunksMap.get(true).add(mysqlProductChunk);
                            }else {
                                updateProductChunksMap.get(false).add(mysqlProductChunk);
                            }
                        }
                    }
                }

            }
            productService.insertProductChunkToEs(insertProductChunkList);
            updateProductChunksMap.forEach((needEmbedding, productChunkList) -> {
                productService.updateProductChunkInEs(productChunkList, needEmbedding);
            });
            productService.deleteProductChunkInEs(deleteProductChunkList);
            ack.acknowledge();
        } catch (Exception e) {
            log.error("dmsProductChunkListener error, flatMessages={}, ", JSON.toJSONString(dmsMessages), e);
            noticeConfig.sendError("dmsProductChunkListener\n" + ExceptionUtil.stacktraceToString(e));
            ack.nack(0, 5000L);
        }
    }



    @KafkaListener(topics = {"ku-product-sync-topic"}, groupId = "qa-bot-group", concurrency = "2", properties = {"max.poll.records=5"})
    public void kuProductBinlogListener(List<byte[]> records, Acknowledgment ack) {
        List<FlatMessage> flatMessages = null;
        try {
            flatMessages = records.stream().flatMap(record -> {
                Message message = CanalMessageDeserializer.deserializer(record);
                return MessageUtil.convert(message).stream();
            }).collect(Collectors.toList());
            log.info("kuProductBinlogListener recordsSize={}, msgSize={}, msgList:\n{}", records.size(), flatMessages.size(), flatMessages.stream().map(JSON::toJSONString).collect(Collectors.joining("\n")));
            List<KuProduct> insertKuProductList = new ArrayList<>();
            List<KuProduct> deleteKuProductList = new ArrayList<>();
            Map<Boolean, List<KuProduct>> updateKuProductsMap = new HashMap<>();
            updateKuProductsMap.put(true, new ArrayList<>());
            updateKuProductsMap.put(false, new ArrayList<>());
            for (FlatMessage flatMessage : flatMessages) {
                String type = flatMessage.getType();
                List<Map<String, Object>> dataList = flatMessage.getData();
                List<KuProduct> kuProductList;
                if (dataList != null) {
                    kuProductList = dataList.stream().map(data -> {
                        Set<String> keys = data.keySet();
                        for (String key : keys) {
                            Object value = data.get(key);
                            if (value instanceof String) {
                                String stringValue = (String) value;
                                if (JsonUtil.isJsonObject(stringValue)) {
                                    data.put(key, JSON.parseObject(stringValue));
                                } else if (JsonUtil.isJsonArray(stringValue)) {
                                    data.put(key, JSON.parseArray(stringValue));
                                }
                            }
                        }
                        Object sku_list_obj = data.get("sku_list");
                        if (sku_list_obj instanceof JSONArray) {
                            JSONArray skuList = (JSONArray) sku_list_obj;
                            for (Object o : skuList) {
                                if (o instanceof JSONObject) {
                                    JSONObject sku = (JSONObject) o;
                                    String createdAt = sku.getString("createdAt");
                                    if (createdAt != null) {
                                        try {
                                            ZonedDateTime parse = ZonedDateTime.parse(createdAt);
                                            sku.put("createdAt", parse.toLocalDateTime().toString());
                                        } catch (Exception e) {

                                        }
                                    }
                                }
                            }
                        } else {
                            log.warn("kuProductBinlogListener: ignore invalid json array sku_list, flatMessage={}, data={}", JSON.toJSONString(flatMessage), JSON.toJSONString(data));
                            data.put("sku_list", null);
                        }
                        return JSON.parseObject(JSON.toJSONString(data), KuProduct.class);
                    }).collect(Collectors.toList());
                } else {
                    kuProductList = new ArrayList<>();
                }
                if (type.equals(CanalEntry.EventType.INSERT.name())) {
                    insertKuProductList.addAll(kuProductList);
                } else if (type.equals(CanalEntry.EventType.DELETE.name())) {
                    deleteKuProductList.addAll(kuProductList);
                } else if (type.equals(CanalEntry.EventType.UPDATE.name())) {
                    List<Map<String, Object>> old = flatMessage.getOld();
                    for (int i = 0; i < kuProductList.size(); i++) {
                        KuProduct updateKuProduct = kuProductList.get(i);
                        Map<String, Object> changedFiledOldValueMap = old.get(i);
                        String categoryColumnName = "category";
                        String titleColumnName = "title";
                        String describeColumnName = "describe";
                        boolean needReEmbedding = changedFiledOldValueMap.containsKey(categoryColumnName) ||
                                changedFiledOldValueMap.containsKey(titleColumnName) ||
                                changedFiledOldValueMap.containsKey(describeColumnName);
                        updateKuProductsMap.get(needReEmbedding).add(updateKuProduct);
                    }
                }
            }
            productService.insertKuProductToEs(insertKuProductList);
            updateKuProductsMap.forEach((needEmbedding, kuProducts) -> {
                productService.updateKuProductInEs(kuProducts, needEmbedding);
            });
            productService.deleteKuProductInEs(deleteKuProductList);
            ack.acknowledge();
        } catch (Exception e) {
            log.error("kuProductBinlogListener error, flatMessages={}, ", JSON.toJSONString(flatMessages), e);
            noticeConfig.sendError("kuProductBinlogListener\n" + ExceptionUtil.stacktraceToString(e));
            ack.nack(0, 5000L);
        }
    }

    @KafkaListener(topics = {"product-chunk-sync-topic"}, groupId = "qa-bot-group", concurrency = "2", properties = {"max.poll.records=1"})
    public void productChunkBinlogListener(List<byte[]> records, Acknowledgment ack) {
        List<FlatMessage> flatMessages = null;
        try {
            flatMessages = records.stream().flatMap(record -> {
                Message message = CanalMessageDeserializer.deserializer(record);
                return MessageUtil.convert(message).stream();
            }).collect(Collectors.toList());
            log.info("productChunkBinlogListener recordsSize={}, msgSize={}, msgList:\n{}", records.size(), flatMessages.size(), flatMessages.stream().map(JSON::toJSONString).collect(Collectors.joining("\n")));
            List<ProductChunk> insertProductChunkList = new ArrayList<>();
            List<ProductChunk> deleteProductChunkList = new ArrayList<>();
            Map<Boolean, List<ProductChunk>> updateProductChunksMap = new HashMap<>();
            updateProductChunksMap.put(true, new ArrayList<>());
            updateProductChunksMap.put(false, new ArrayList<>());
            for (FlatMessage flatMessage : flatMessages) {
                String type = flatMessage.getType();
                List<Map<String, Object>> dataList = flatMessage.getData();
                List<ProductChunk> productChunkList;
                if (dataList != null) {
                    productChunkList = dataList.stream().map(data -> {
                        Set<String> keys = data.keySet();
                        for (String key : keys) {
                            Object value = data.get(key);
                            if (value instanceof String) {
                                String stringValue = (String) value;
                                if (JsonUtil.isJsonObject(stringValue)) {
                                    data.put(key, JSON.parseObject(stringValue));
                                } else if (JsonUtil.isJsonArray(stringValue)) {
                                    data.put(key, JSON.parseArray(stringValue));
                                }
                            }
                        }
                        Object sku_list_obj = data.get("sku_list");
                        if (sku_list_obj instanceof JSONArray) {
                            JSONArray skuList = (JSONArray) sku_list_obj;
                            for (Object o : skuList) {
                                if (o instanceof JSONObject) {
                                    JSONObject sku = (JSONObject) o;
                                    String createdAt = sku.getString("createdAt");
                                    if (createdAt != null) {
                                        try {
                                            ZonedDateTime parse = ZonedDateTime.parse(createdAt);
                                            sku.put("createdAt", parse.toLocalDateTime().toString());
                                        } catch (Exception e) {

                                        }
                                    }
                                }
                            }
                        }
                        return JSON.parseObject(JSON.toJSONString(data), ProductChunk.class);
                    }).collect(Collectors.toList());
                } else {
                    productChunkList = new ArrayList<>();
                }
                if (type.equals(CanalEntry.EventType.INSERT.name())) {
                    insertProductChunkList.addAll(productChunkList);
                } else if (type.equals(CanalEntry.EventType.DELETE.name())) {
                    deleteProductChunkList.addAll(productChunkList);
                } else if (type.equals(CanalEntry.EventType.UPDATE.name())) {
                    List<Map<String, Object>> old = flatMessage.getOld();
                    for (int i = 0; i < productChunkList.size(); i++) {
                        ProductChunk updateProductChunk = productChunkList.get(i);
                        Map<String, Object> changedFiledOldValueMap = old.get(i);
                        String contentColumnName = "content";
                        boolean needReEmbedding = changedFiledOldValueMap.containsKey(contentColumnName);
                        updateProductChunksMap.get(needReEmbedding).add(updateProductChunk);
                    }
                }
            }
            productService.insertProductChunkToEs(insertProductChunkList);
            updateProductChunksMap.forEach((needEmbedding, productChunkList) -> {
                productService.updateProductChunkInEs(productChunkList, needEmbedding);
            });
            productService.deleteProductChunkInEs(deleteProductChunkList);
            ack.acknowledge();
        } catch (Exception e) {
            log.error("productChunkBinlogListener error, flatMessages={}, ", JSON.toJSONString(flatMessages), e);
            noticeConfig.sendError("productChunkBinlogListener\n" + ExceptionUtil.stacktraceToString(e));
            ack.nack(0, 5000L);
        }
    }

    public static void main(String[] args) throws IOException {
        List<JSONObject> list = JSON.parseArray(new String(Files.readAllBytes(Paths.get("/Users/<USER>/Desktop/caogao/caogao2.json")))).toJavaList(JSONObject.class);
        for (JSONObject jsonObject : list) {
            KuProduct data = jsonObject.getJSONArray("data").toJavaList(KuProduct.class).get(0);
            System.out.println(data);
        }
    }
}
