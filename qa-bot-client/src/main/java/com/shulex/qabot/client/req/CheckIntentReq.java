package com.shulex.qabot.client.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
public class CheckIntentReq {
    @ApiModelProperty(value = "tenantId")
    Long tenantId;
    @ApiModelProperty(value = "botId")
    Long botId;
    @ApiModelProperty(value = "projectId")
    Long projectId;
    @ApiModelProperty(value = "other意图id")
    Long otherIntentId;
    @ApiModelProperty(value = "当前检测的意图id")
    Long checkIntentId;
    @ApiModelProperty(value = "要检测的会话数量")
    Integer checkChatCount = 100;
    @ApiModelProperty(value = "排除的会话id list")
    List<String> excludeChatIds;
}
