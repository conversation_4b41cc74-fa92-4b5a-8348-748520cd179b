package com.shulex.qabot.service;

import com.alibaba.fastjson.JSON;
import com.shulex.qabot.es.EsService;
import com.shulex.qabot.client.dto.esentity.DirectoryEsEntity;
import com.shulex.qabot.client.dto.mysqlentity.Directory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
@Slf4j
public class DirectoryService {
    @Autowired
    EsService esService;

    public void insertDirectoryToEs(List<Directory> directoryList) {
        if (ObjectUtils.isEmpty(directoryList)) {
            return;
        }
        List<DirectoryEsEntity> directoryEsEntities = directoryList.stream().map(DirectoryEsEntity::new).collect(Collectors.toList());
        Map<String, String> id2JsonData = directoryEsEntities.stream().collect(Collectors.toMap(e -> e.getId().toString(), JSON::toJSONString, (v1, v2) -> v2));
        esService.insert(DirectoryEsEntity.indexName, id2JsonData);
        log.info("insertDirectoryToEs: directoryIds={}", id2JsonData.keySet());
    }

    public void deleteDirectoryInEs(List<Directory> directoryList) {
        if (ObjectUtils.isEmpty(directoryList)) {
            return;
        }
        List<Long> directoryIds = directoryList.stream().map(Directory::getId).filter(Objects::nonNull).collect(Collectors.toList());
        if (ObjectUtils.isEmpty(directoryIds)) {
            return;
        }
        List<String> ids = directoryIds.stream().map(Object::toString).collect(Collectors.toList());
        esService.delete(DirectoryEsEntity.indexName, ids, true);
        log.info("deleteDirectoryInEs: directoryIds={}", ids);
    }

    public void updateDirectoryInEs(List<Directory> directoryList) {
        if (ObjectUtils.isEmpty(directoryList)) {
            return;
        }
        List<DirectoryEsEntity> directoryEsEntities = directoryList.stream().map(DirectoryEsEntity::new).collect(Collectors.toList());
        Map<String, String> id2UpdateJsonData = directoryEsEntities.stream().collect(Collectors.toMap(e -> e.getId().toString(), JSON::toJSONString, (v1, v2) -> v2));
        esService.update(DirectoryEsEntity.indexName, id2UpdateJsonData, true);
        log.info("updateDirectoryInEs: updated directoryIds={}", id2UpdateJsonData.keySet());
    }
}
