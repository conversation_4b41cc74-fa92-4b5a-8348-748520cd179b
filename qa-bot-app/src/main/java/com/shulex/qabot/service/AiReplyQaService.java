package com.shulex.qabot.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.lang.func.LambdaUtil;
import com.alibaba.fastjson.JSON;
import com.shulex.gpt.apisdk.IKBApi;
import com.shulex.gpt.apisdk.IToolsApi;
import com.shulex.gpt.apisdk.dto.*;
import com.shulex.gpt.apisdk.enums.*;
import com.shulex.qabot.alg.AlgClient;
import com.shulex.qabot.alg.dto.AiReplyReq;
import com.shulex.qabot.alg.dto.RankReq;
import com.shulex.qabot.alg.dto.RankRes;
import com.shulex.qabot.alg.dto.RelevantScoreReq;
import com.shulex.qabot.client.dto.EsSearchResult;
import com.shulex.qabot.client.dto.esentity.DirectoryEsEntity;
import com.shulex.qabot.client.dto.esentity.KuEsEntity;
import com.shulex.qabot.client.dto.mysqlentity.Directory;
import com.shulex.qabot.client.dto.mysqlentity.KuLabelValue;
import com.shulex.qabot.client.dto.mysqlentity.Project;
import com.shulex.qabot.client.req.KnowledgeSearchReq;
import com.shulex.qabot.client.req.QaChatReq;
import com.shulex.qabot.client.res.KnowledgeSearchRes;
import com.shulex.qabot.client.res.QaChatRes;
import com.shulex.qabot.client.res.TranslateTagKeywordRes;
import com.shulex.qabot.config.InvokeGptConfig;
import com.shulex.qabot.config.PromptConfig;
import com.shulex.qabot.config.QaChatConfig;
import com.shulex.qabot.es.EsSearchService;
import com.shulex.qabot.es.EsService;
import com.shulex.qabot.feign.gpt.GptHttpClient;
import com.shulex.qabot.mysql.repository.*;
import com.shulex.qabot.util.ParallelUtil;
import com.shulex.qabot.util.StringUtil;
import com.shulex.qabot.util.TiktokenUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class AiReplyQaService {
    @Autowired
    ProjectRepository projectRepository;
    @Autowired
    DirectoryRepository directoryRepository;
    @Autowired
    BedrockService bedrockService;
    @Autowired
    AlgClient algClient;
    @Autowired
    KuService kuService;
    @Autowired
    EsService esService;
    @Autowired
    EsSearchService esSearchService;
    @Autowired
    QaChatConfig qaChatConfig;
    @Autowired
    KuLabelValueRepository kuLabelValueRepository;
    @Autowired
    IKBApi kbApi;
    @Autowired
    GptHttpClient gptHttpClient;
    @Autowired
    DocumentChunkRepository documentChunkRepository;
    @Autowired
    KuRepository kuRepository;
    @Autowired
    IToolsApi gptToolApiFeignClient;
    @Autowired
    InvokeGptConfig invokeGptConfig;
    @Autowired
    PromptConfig promptConfig;

    public QaChatRes qaChat(QaChatReq qaChatReq) {
        if (!kuRepository.exits(qaChatReq.getTenantId(), qaChatReq.getProjectId())) {
            log.info("qaChat[{}]: no ku, tenantId={}, projectId={}", qaChatReq.getChatId(), qaChatReq.getTenantId(), qaChatReq.getProjectId());
            return new QaChatRes().setResult(new QaChatRes.Result()
                    .setStatus("DENIED")
            );
        }
        MixedQuery mixedQuery = qaChatReq.getMixedQuery();
        QaChatReq.AiReplyOption aiReplyOption = qaChatReq.getAiReplyOption();
        List<AiReplyReq.RetrievedKuData> retrieved_kus_list = new ArrayList<>();
        log.info("qaChat[{}]: start", qaChatReq.getChatId());
        if (CollectionUtil.isEmpty(qaChatReq.getSpecifiedKuList())) {
            // 搜索文件夹
            HashSet<Long> dirIdsRangeForSearchKu = new HashSet<>(); // 目标为找到知识搜索的文件见范围
            List<Directory> allDirList = directoryRepository.getDirectories(qaChatReq.getTenantId(), qaChatReq.getProjectId());
            Map<Long, Directory> allDirIdMap = allDirList.stream().collect(Collectors.toMap(Directory::getId, d -> d, (v1, v2) -> v2));
            Function<Long, List<Directory>> getDirsByParentId = parentId -> allDirList.stream().filter(d -> d.getParentId().equals(parentId)).collect(Collectors.toList());
            Function<Long, List<Directory>> getOtherDirByParentId = parentId -> allDirList.stream().filter(d -> d.getParentId().equals(parentId) && d.getName().equalsIgnoreCase("other")).collect(Collectors.toList());
            Function<Long, Set<Long>> findAllSubDirIds = dirId -> {
                HashSet<Long> subIds = new HashSet<>();
                List<Long> needFindChildrenDirIds = new ArrayList<>();
                needFindChildrenDirIds.add(dirId);
                while (!needFindChildrenDirIds.isEmpty()) {
                    Long needFindChildDirId = needFindChildrenDirIds.get(0);
                    subIds.add(needFindChildDirId);
                    List<Directory> children = getDirsByParentId.apply(needFindChildDirId);
                    if (CollectionUtil.isNotEmpty(children)) {
                        needFindChildrenDirIds.addAll(children.stream().map(Directory::getId).collect(Collectors.toList()));
                    }
                    needFindChildrenDirIds.remove(0);
                }
                subIds.remove(dirId);
                return subIds;
            };
            HashSet<Long> strategySpecifiedDirAnsSubDirIds = new HashSet<>(); // 策略配置指定的文件夹以及所有子文件夹
            if (CollectionUtil.isNotEmpty(qaChatReq.getDirectoryIds())) {
                List<Long> strategySpecifiedDirIds = qaChatReq.getDirectoryIds();
                strategySpecifiedDirAnsSubDirIds.addAll(strategySpecifiedDirIds);
                for (Long strategySpecifiedDirId : strategySpecifiedDirIds) {
                    Set<Long> subDirIds = findAllSubDirIds.apply(strategySpecifiedDirId);
                    strategySpecifiedDirAnsSubDirIds.addAll(subDirIds);
                }
            }
            Project project = projectRepository.getById(qaChatReq.getProjectId());
            if (project != null && ProjectTypeEnum.INTENT.equals(project.getType())) {
                // 用product_name模糊搜索相关的文件夹
                HashSet<Long> productNameSearchedDirIds = new HashSet<>();
                Map<String, List<?>> entities = qaChatReq.getEntities();
                List<String> productNames = StringUtil.getProductNamesFromEntities(entities);
                if (CollectionUtil.isNotEmpty(productNames)) {
                    // 进行match
                    for (String productName : productNames) {
                        List<Pair<String, Object>> termQueryFieldAndValue = new ArrayList<>();
                        termQueryFieldAndValue.add(Pair.of(LambdaUtil.getFieldName(DirectoryEsEntity::getTenantId), qaChatReq.getTenantId()));
                        termQueryFieldAndValue.add(Pair.of(LambdaUtil.getFieldName(DirectoryEsEntity::getProjectId), qaChatReq.getProjectId()));
                        termQueryFieldAndValue.add(Pair.of(LambdaUtil.getFieldName(DirectoryEsEntity::getType), DirectoryTypeEnum.GENERIC));
                        if (CollectionUtil.isNotEmpty(strategySpecifiedDirAnsSubDirIds)) {
                            termQueryFieldAndValue.add(Pair.of(LambdaUtil.getFieldName(DirectoryEsEntity::getId), strategySpecifiedDirAnsSubDirIds));
                        }
                        List<EsSearchResult<DirectoryEsEntity>> matchedDirectories = esSearchService.matchAsync(DirectoryEsEntity.indexName, productName, LambdaUtil.getFieldName(DirectoryEsEntity::getName), 5000, termQueryFieldAndValue, null, DirectoryEsEntity.class).join();
                        if (CollectionUtil.isNotEmpty(matchedDirectories)) {
                            productNameSearchedDirIds.addAll(matchedDirectories.stream().map(p -> p.getData().getId()).collect(Collectors.toList()));
                        }
                    }
                }
                if (CollectionUtil.isNotEmpty(productNameSearchedDirIds)) {
                    // find other
                    List<Directory> otherDirectories = new ArrayList<>();
                    for (Long productNameSearchedDirId : productNameSearchedDirIds) {
                        Directory directory = allDirIdMap.get(productNameSearchedDirId);
                        Long parentId = directory.getParentId();
                        while (true) {
                            List<Directory> otherDirs = getOtherDirByParentId.apply(parentId);
                            otherDirectories.addAll(otherDirs);
                            Directory parentDir = allDirIdMap.get(parentId);
                            if (parentDir == null) {
                                break;
                            }
                            parentId = parentDir.getParentId();
                        }
                    }
                    // top other_dir's all sub dirs
                    Set<Long> topOtherDirSubDirs = new HashSet<>();
                    for (Directory otherDir : otherDirectories) {
                        if (!allDirIdMap.containsKey(otherDir.getParentId())) {
                            Set<Long> subDirs = findAllSubDirIds.apply(otherDir.getId());
                            topOtherDirSubDirs.addAll(subDirs);
                        }
                    }
                    dirIdsRangeForSearchKu.addAll(productNameSearchedDirIds);
                    dirIdsRangeForSearchKu.addAll(otherDirectories.stream().map(Directory::getId).collect(Collectors.toList()));
                    dirIdsRangeForSearchKu.addAll(topOtherDirSubDirs);
                } else {
                    dirIdsRangeForSearchKu.addAll(strategySpecifiedDirAnsSubDirIds);
                }
            } else {
                dirIdsRangeForSearchKu.addAll(strategySpecifiedDirAnsSubDirIds);
            }
            log.info("qaChat[{}]: search directory, dirIdsRangeForSearchKu={}", qaChatReq.getChatId(), dirIdsRangeForSearchKu);
            // 搜索标签
            Map<String, List<?>> entities = qaChatReq.getEntities();
            List<String> productNames = StringUtil.getProductNamesFromEntities(entities);
            if (CollectionUtil.isNotEmpty(productNames)) {
                List<KuLabelValue> kuLabelValues = kuLabelValueRepository.getKuLabelValuesByName(qaChatReq.getTenantId(), qaChatReq.getProjectId(), productNames);
                log.info("qaChat[{}]: search kuLabelValue, kuLabelValues={}", qaChatReq.getChatId(), kuLabelValues);
                if (CollectionUtil.isNotEmpty(kuLabelValues)) {
                    Map<Long, Set<Long>> kuLabelDimensionId2ValueIds = qaChatReq.getKuLabelDimensionId2ValueIds();
                    if (kuLabelDimensionId2ValueIds == null) {
                        kuLabelDimensionId2ValueIds = new HashMap<>();
                        qaChatReq.setKuLabelDimensionId2ValueIds(kuLabelDimensionId2ValueIds);
                    }
                    for (KuLabelValue kuLabelValue : kuLabelValues) {
                        Long dimensionId = kuLabelValue.getKuLabelDimensionId();
                        Long valueId = kuLabelValue.getId();
                        kuLabelDimensionId2ValueIds.computeIfAbsent(dimensionId, k -> new HashSet<>()).add(valueId);
                    }
                }
            }
            // 召回知识
            List<Pair<String, Object>> termQueryFieldAndValue = new ArrayList<>();
            termQueryFieldAndValue.add(Pair.of(LambdaUtil.getFieldName(KuEsEntity::getPublished), true));
            termQueryFieldAndValue.add(Pair.of(LambdaUtil.getFieldName(KuEsEntity::getStatus), KuStatusEnum.INDEXED));
            termQueryFieldAndValue.add(Pair.of(LambdaUtil.getFieldName(KuEsEntity::getTenantId), qaChatReq.getTenantId()));
            termQueryFieldAndValue.add(Pair.of(LambdaUtil.getFieldName(KuEsEntity::getProjectId), qaChatReq.getProjectId()));
            if (CollectionUtil.isNotEmpty(dirIdsRangeForSearchKu)) {
                termQueryFieldAndValue.add(Pair.of(LambdaUtil.getFieldName(KuEsEntity::getDirectoryId), dirIdsRangeForSearchKu));
            }
            if (CollectionUtil.isNotEmpty(qaChatReq.getKuLabelDimensionId2ValueIds())) {
                Map<Long, Set<Long>> kuLabelDimensionId2ValueIds = qaChatReq.getKuLabelDimensionId2ValueIds();
                // 不同维度之间是 and 关系，同一个维度中的值是 or 关系
                kuLabelDimensionId2ValueIds.forEach((dimensionId, valueIds) -> {
                    if (CollectionUtil.isNotEmpty(valueIds)) {
                        termQueryFieldAndValue.add(Pair.of(LambdaUtil.getFieldName(KuEsEntity::getKuLabelValueIds), valueIds));
                    }
                });
            }
            log.info("qaChat[{}]: termQueryFieldAndValue={}", qaChatReq.getChatId(), termQueryFieldAndValue);
            List<String> excludeFields = EsService.getEmbeddingFieldNames(KuEsEntity.class);
            // 直接返回知识检验
            // 用户原始问题检验
            String userQuestion = IntentService.joinTitleContent(mixedQuery.getOriginTitle(), mixedQuery.getOriginContent());
            if (StringUtils.hasLength(userQuestion)) {
                List<BigDecimal> userQuestionCohereEmbedding = bedrockService.getCohereEmbedding(userQuestion);
                List<EsSearchResult<KuEsEntity>> userQuestionSearchTitleCohereEmbeddingKuHits = esSearchService.embeddingCosSearchAsync(KuEsEntity.indexName, userQuestionCohereEmbedding, LambdaUtil.getFieldName(KuEsEntity::getTitleEmbeddingCohere), aiReplyOption.getTop_k(), termQueryFieldAndValue, excludeFields, KuEsEntity.class).join();
                if (CollectionUtil.isNotEmpty(userQuestionSearchTitleCohereEmbeddingKuHits)) {
                    EsSearchResult<KuEsEntity> mostSimilarKuPair = userQuestionSearchTitleCohereEmbeddingKuHits.get(0);
                    Double score = mostSimilarKuPair.getScore();
                    KuEsEntity mostSimilarKu = mostSimilarKuPair.getData();
                    log.info("qaChat[{}]: first cohere embedding search(userQuestion search title), most similar ku score={}, kuId={}, kuTitle={}, ku={}", qaChatReq.getChatId(), score, mostSimilarKu.getId(), mostSimilarKu.getTitle(), JSON.toJSONString(mostSimilarKu));
                    if (score >= qaChatConfig.getKuDirectReturnUserQuestionCohereSimilarity()) {
                        QaChatRes qaChatRes = new QaChatRes();
                        List<RetrievedKu> retrievedKuList = kuEsEntity2RetrievedKu(userQuestionSearchTitleCohereEmbeddingKuHits);
                        retrievedKuList.get(0).setEffect(true);
                        String response = mostSimilarKu.getContent();
                        try {
                            DetectLanguageResponse responseLanguageRes = gptToolApiFeignClient.detectLanguage(new DetectLanguageRequest(response), invokeGptConfig.getXToken());
                            if (!responseLanguageRes.getLanguage().equals(BotLanguageEnum.valueOf(aiReplyOption.getLanguage().toUpperCase()))) {
                                BotLanguageEnum languageEnum = BotLanguageEnum.valueOf(aiReplyOption.getLanguage().toUpperCase());
                                List<TranslateTagKeywordRes> translateResList = TranslateService.translate(Arrays.asList(mostSimilarKu.getContent()), Arrays.asList(languageEnum));
                                if (CollectionUtil.isNotEmpty(translateResList)) {
                                    TranslateTagKeywordRes translateRes = translateResList.get(0);
                                    response = translateRes.getToLanguageKeyword();
                                }
                            }
                        } catch (Exception e) {
                            log.warn("qaChat[{}]: translate mostSimilarKu's content failed", qaChatReq.getChatId(), e);
                        }
                        qaChatRes.setResult(new QaChatRes.Result()
                                .setStatus("SUCCESS")
                                .setResponse(response)
                                .setReason("CR_RETRIEVE_SUCCESS")
                                .setExplain("The user's questions are basically consistent with the knowledge's question, and the answers in the enterprise knowledge base are directly returned.")
                        );
                        qaChatRes.setMetaData(new QaChatRes.MetaData()
                                .setRetrieveMinScore(qaChatReq.getAiReplyOption().getRetrieve_min_score())
                                .setUsedKus(Collections.singletonList(retrievedKuList.get(0)))
                                .setRerankKus(retrievedKuList)
                        );
                        log.info("qaChat[{}]: first cohere embedding search(userQuestion search title) result satisfy so direct return, similarity={}, threshold={}, kuId={}, kuTitle={}, userQuestion={}", qaChatReq.getChatId(), score, qaChatConfig.getKuDirectReturnUserQuestionCohereSimilarity(), mostSimilarKu.getId(), mostSimilarKu.getTitle(), userQuestion);
                        return qaChatRes;
                    }
                }
            }
            // 总结后的用户问题检验
            List<BigDecimal> knowledgeCohereEmbedding = bedrockService.getCohereEmbedding(mixedQuery.getKnowledgeQuery());
            List<EsSearchResult<KuEsEntity>> knowledgeSearchTitleCohereEmbeddingKuHits = esSearchService.embeddingCosSearchAsync(KuEsEntity.indexName, knowledgeCohereEmbedding, LambdaUtil.getFieldName(KuEsEntity::getTitleEmbeddingCohere), aiReplyOption.getTop_k(), termQueryFieldAndValue, excludeFields, KuEsEntity.class).join();
            if (CollectionUtil.isNotEmpty(knowledgeSearchTitleCohereEmbeddingKuHits)) {
                EsSearchResult<KuEsEntity> mostSimilarKuPair = knowledgeSearchTitleCohereEmbeddingKuHits.get(0);
                Double score = mostSimilarKuPair.getScore();
                KuEsEntity mostSimilarKu = mostSimilarKuPair.getData();
                log.info("qaChat[{}]: first cohere embedding search(knowledge search title), most similar ku score={}, kuId={}, kuTitle={}, ku={}", qaChatReq.getChatId(), score, mostSimilarKu.getId(), mostSimilarKu.getTitle(), JSON.toJSONString(mostSimilarKu));
                if (score >= qaChatConfig.getKuDirectReturnKnowledgeCohereSimilarity()) {
                    QaChatRes qaChatRes = new QaChatRes();
                    List<RetrievedKu> retrievedKuList = kuEsEntity2RetrievedKu(knowledgeSearchTitleCohereEmbeddingKuHits);
                    retrievedKuList.get(0).setEffect(true);
                    String response = mostSimilarKu.getContent();
                    try {
                        DetectLanguageResponse responseLanguageRes = gptToolApiFeignClient.detectLanguage(new DetectLanguageRequest(response), invokeGptConfig.getXToken());
                        if (!responseLanguageRes.getLanguage().equals(BotLanguageEnum.valueOf(aiReplyOption.getLanguage().toUpperCase()))) {
                            BotLanguageEnum languageEnum = BotLanguageEnum.valueOf(aiReplyOption.getLanguage().toUpperCase());
                            List<TranslateTagKeywordRes> translateResList = TranslateService.translate(Arrays.asList(mostSimilarKu.getContent()), Arrays.asList(languageEnum));
                            if (CollectionUtil.isNotEmpty(translateResList)) {
                                TranslateTagKeywordRes translateRes = translateResList.get(0);
                                response = translateRes.getToLanguageKeyword();
                            }
                        }
                    } catch (Exception e) {
                        log.warn("qaChat[{}]: translate mostSimilarKu's content failed", qaChatReq.getChatId(), e);
                    }
                    qaChatRes.setResult(new QaChatRes.Result()
                            .setStatus("SUCCESS")
                            .setResponse(response)
                            .setReason("CR_RETRIEVE_SUCCESS")
                            .setExplain("The user's questions are basically consistent with the knowledge's question, and the answers in the enterprise knowledge base are directly returned.")
                    );
                    qaChatRes.setMetaData(new QaChatRes.MetaData()
                            .setRetrieveMinScore(qaChatReq.getAiReplyOption().getRetrieve_min_score())
                            .setUsedKus(Collections.singletonList(retrievedKuList.get(0)))
                            .setRerankKus(retrievedKuList)
                    );
                    log.info("qaChat[{}]: first cohere embedding search(knowledge search title) result satisfy so direct return, similarity={}, threshold={}, kuId={}, kuTitle={}, knowledgeQuery={}", qaChatReq.getChatId(), score, qaChatConfig.getKuDirectReturnKnowledgeCohereSimilarity(), mostSimilarKu.getId(), mostSimilarKu.getTitle(), mixedQuery.getKnowledgeQuery());
                    return qaChatRes;
                }
            }
            // 无满足直接返回的条件，则进行全部的召回逻辑
            List<BigDecimal> knowledgeMpnetEmbedding = algClient.textEmbed(mixedQuery.getKnowledgeEnQuery()).join().getResult();
            // parallel search
            CompletableFuture<List<EsSearchResult<KuEsEntity>>> titleMpnetEmbeddingKuHitsFuture = esSearchService.embeddingCosSearchAsync(KuEsEntity.indexName, knowledgeMpnetEmbedding, LambdaUtil.getFieldName(KuEsEntity::getTitleEmbeddingMpnet), aiReplyOption.getTop_k(), termQueryFieldAndValue, excludeFields, KuEsEntity.class);
            CompletableFuture<List<EsSearchResult<KuEsEntity>>> titleEsMatchKuHitsFuture = esSearchService.matchPhraseAsync(KuEsEntity.indexName, mixedQuery.getKnowledgeQuery(), LambdaUtil.getFieldName(KuEsEntity::getTitle), aiReplyOption.getTop_k(), termQueryFieldAndValue, excludeFields, KuEsEntity.class);
            CompletableFuture<List<EsSearchResult<KuEsEntity>>> contentCohereEmbeddingKuHitsFuture = esSearchService.embeddingCosSearchAsync(KuEsEntity.indexName, knowledgeCohereEmbedding, LambdaUtil.getFieldName(KuEsEntity::getContentEmbeddingCohere), aiReplyOption.getTop_k(), termQueryFieldAndValue, excludeFields, KuEsEntity.class);
            CompletableFuture<List<EsSearchResult<KuEsEntity>>> contentMpnetEmbeddingKuHitsFuture = esSearchService.embeddingCosSearchAsync(KuEsEntity.indexName, knowledgeMpnetEmbedding, LambdaUtil.getFieldName(KuEsEntity::getContentEmbeddingMpnet), aiReplyOption.getTop_k(), termQueryFieldAndValue, excludeFields, KuEsEntity.class);
            CompletableFuture<List<EsSearchResult<KuEsEntity>>> contentEsMatchKuHitsFuture = esSearchService.matchPhraseAsync(KuEsEntity.indexName, mixedQuery.getKnowledgeQuery(), LambdaUtil.getFieldName(KuEsEntity::getContent), aiReplyOption.getTop_k(), termQueryFieldAndValue, excludeFields, KuEsEntity.class);

            List<EsSearchResult<KuEsEntity>> titleMpnetEmbeddingKuHits = titleMpnetEmbeddingKuHitsFuture.join();
            List<EsSearchResult<KuEsEntity>> titleEsMatchKuHits = titleEsMatchKuHitsFuture.join();
            List<EsSearchResult<KuEsEntity>> contentCohereEmbeddingKuHits = contentCohereEmbeddingKuHitsFuture.join();
            List<EsSearchResult<KuEsEntity>> contentMpnetEmbeddingKuHits = contentMpnetEmbeddingKuHitsFuture.join();
            List<EsSearchResult<KuEsEntity>> contentEsMatchKuHits = contentEsMatchKuHitsFuture.join();
            log.info("qaChat[{}]: ku search finish", qaChatReq.getChatId());
            // 共6个结果，将对应搜索方式的title和content进行合并，得到3路结果
            List<EsSearchResult<KuEsEntity>> cohereEmbeddingKuHits = mergeTwoSearchList(knowledgeSearchTitleCohereEmbeddingKuHits, contentCohereEmbeddingKuHits, aiReplyOption.getTop_k());
            List<EsSearchResult<KuEsEntity>> mpnetEmbeddingKuHits = mergeTwoSearchList(titleMpnetEmbeddingKuHits, contentMpnetEmbeddingKuHits, aiReplyOption.getTop_k());
            List<EsSearchResult<KuEsEntity>> esMatchKuHits = mergeTwoSearchList(titleEsMatchKuHits, contentEsMatchKuHits, aiReplyOption.getTop_k());
            if (CollectionUtil.isNotEmpty(cohereEmbeddingKuHits)) {
                retrieved_kus_list.add(new AiReplyReq.RetrievedKuData().setRetrieve_type("CohereEmbedding").setRetrieved_kus(kuEsEntity2RetrievedKu(cohereEmbeddingKuHits)));
            }
            if (CollectionUtil.isNotEmpty(mpnetEmbeddingKuHits)) {
                retrieved_kus_list.add(new AiReplyReq.RetrievedKuData().setRetrieve_type("MpnetEmbedding").setRetrieved_kus(kuEsEntity2RetrievedKu(mpnetEmbeddingKuHits)));
            }
            if (CollectionUtil.isNotEmpty(esMatchKuHits)) {
                retrieved_kus_list.add(new AiReplyReq.RetrievedKuData().setRetrieve_type("EsMatch").setRetrieved_kus(kuEsEntity2RetrievedKu(esMatchKuHits)));
            }
        } else {
            retrieved_kus_list.add(new AiReplyReq.RetrievedKuData().setRetrieve_type("SpecifiedKu").setRetrieved_kus(qaChatReq.getSpecifiedKuList()));
        }
        // 调用ai_reply
        AiReplyReq aiReplyReq = new AiReplyReq()
                .setClient(qaChatReq.getClientType())
                .setTenant_id(qaChatReq.getTenantId())
                .setTouch_point(qaChatReq.getTouchPoint())
                .setBot_name(qaChatReq.getBotName())
                .setChat_id(qaChatReq.getChatId())
                .setOptions(qaChatReq.getAiReplyOption())
                .setContext(new AiReplyReq.Context()
                        .setIntent(qaChatReq.getIntent())
                        .setMessages(qaChatReq.getSummarizedMessages())
                        .setRetrieve_query(mixedQuery.getKnowledgeEnQuery())
                        .setRetrieved_kus_list(retrieved_kus_list)
                        .setEntities(qaChatReq.getEntities())
                        .setPdt(qaChatReq.getPdt())
                        .setMixedQuery(mixedQuery)
                );
        QaChatRes aiReplyRes = algClient.aiReply(aiReplyReq).join();
        log.info("qaChat[{}]: aiReplyReq finish", qaChatReq.getChatId());
        return aiReplyRes;
    }

    public QaChatRes qaChatV2(QaChatReq qaChatReq) {
        if (!kuRepository.exits(qaChatReq.getTenantId(), qaChatReq.getProjectId())) {
            log.info("qaChat[{}]: no ku, tenantId={}, projectId={}", qaChatReq.getChatId(), qaChatReq.getTenantId(), qaChatReq.getProjectId());
            return new QaChatRes().setResult(new QaChatRes.Result().setStatus("DENIED"));
        }
        MixedQuery mixedQuery = qaChatReq.getMixedQuery();
        QaChatReq.AiReplyOption aiReplyOption = qaChatReq.getAiReplyOption();
        // 知识召回的目标结果为n个知识数组
        List<AiReplyReq.RetrievedKuData> nWayRetrievedKus = new ArrayList<>();
        log.info("qaChat[{}]: start", qaChatReq.getChatId());
        if (CollectionUtil.isEmpty(qaChatReq.getSpecifiedKuList())) {
            // 搜索文件夹
            HashSet<Long> dirIdsRangeForSearchKu = new HashSet<>(); // 目标为找到知识搜索的文件见范围
            List<Directory> allDirList = directoryRepository.getDirectories(qaChatReq.getTenantId(), qaChatReq.getProjectId());
            Map<Long, Directory> allDirIdMap = allDirList.stream().collect(Collectors.toMap(Directory::getId, d -> d, (v1, v2) -> v2));
            Function<Long, List<Directory>> getDirsByParentId = parentId -> allDirList.stream().filter(d -> d.getParentId().equals(parentId)).collect(Collectors.toList());
            Function<Long, List<Directory>> getOtherDirByParentId = parentId -> allDirList.stream().filter(d -> d.getParentId().equals(parentId) && d.getName().equalsIgnoreCase("other")).collect(Collectors.toList());
            Function<Long, Set<Long>> findAllSubDirIds = dirId -> {
                HashSet<Long> subIds = new HashSet<>();
                List<Long> needFindChildrenDirIds = new ArrayList<>();
                needFindChildrenDirIds.add(dirId);
                while (!needFindChildrenDirIds.isEmpty()) {
                    Long needFindChildDirId = needFindChildrenDirIds.get(0);
                    subIds.add(needFindChildDirId);
                    List<Directory> children = getDirsByParentId.apply(needFindChildDirId);
                    if (CollectionUtil.isNotEmpty(children)) {
                        needFindChildrenDirIds.addAll(children.stream().map(Directory::getId).collect(Collectors.toList()));
                    }
                    needFindChildrenDirIds.remove(0);
                }
                subIds.remove(dirId);
                return subIds;
            };
            HashSet<Long> strategySpecifiedDirAnsSubDirIds = new HashSet<>(); // 策略配置指定的文件夹以及所有子文件夹
            if (CollectionUtil.isNotEmpty(qaChatReq.getDirectoryIds())) {
                List<Long> strategySpecifiedDirIds = qaChatReq.getDirectoryIds();
                strategySpecifiedDirAnsSubDirIds.addAll(strategySpecifiedDirIds);
                for (Long strategySpecifiedDirId : strategySpecifiedDirIds) {
                    Set<Long> subDirIds = findAllSubDirIds.apply(strategySpecifiedDirId);
                    strategySpecifiedDirAnsSubDirIds.addAll(subDirIds);
                }
            }
            Project project = projectRepository.getById(qaChatReq.getProjectId());
            if (project != null && ProjectTypeEnum.INTENT.equals(project.getType())) {
                // 用product_name模糊搜索相关的文件夹
                HashSet<Long> productNameSearchedDirIds = new HashSet<>();
                Map<String, List<?>> entities = qaChatReq.getEntities();
                List<String> productNames = StringUtil.getProductNamesFromEntities(entities);
                if (CollectionUtil.isNotEmpty(productNames)) {
                    // 进行match
                    for (String productName : productNames) {
                        List<Pair<String, Object>> termQueryFieldAndValue = new ArrayList<>();
                        termQueryFieldAndValue.add(Pair.of(LambdaUtil.getFieldName(DirectoryEsEntity::getTenantId), qaChatReq.getTenantId()));
                        termQueryFieldAndValue.add(Pair.of(LambdaUtil.getFieldName(DirectoryEsEntity::getProjectId), qaChatReq.getProjectId()));
                        termQueryFieldAndValue.add(Pair.of(LambdaUtil.getFieldName(DirectoryEsEntity::getType), DirectoryTypeEnum.GENERIC));
                        if (CollectionUtil.isNotEmpty(strategySpecifiedDirAnsSubDirIds)) {
                            termQueryFieldAndValue.add(Pair.of(LambdaUtil.getFieldName(DirectoryEsEntity::getId), strategySpecifiedDirAnsSubDirIds));
                        }
                        List<EsSearchResult<DirectoryEsEntity>> matchedDirectories = esSearchService.matchAsync(DirectoryEsEntity.indexName, productName, LambdaUtil.getFieldName(DirectoryEsEntity::getName), 5000, termQueryFieldAndValue, null, DirectoryEsEntity.class).join();
                        if (CollectionUtil.isNotEmpty(matchedDirectories)) {
                            productNameSearchedDirIds.addAll(matchedDirectories.stream().map(p -> p.getData().getId()).collect(Collectors.toList()));
                        }
                    }
                }
                if (CollectionUtil.isNotEmpty(productNameSearchedDirIds)) {
                    // find other
                    List<Directory> otherDirectories = new ArrayList<>();
                    for (Long productNameSearchedDirId : productNameSearchedDirIds) {
                        Directory directory = allDirIdMap.get(productNameSearchedDirId);
                        Long parentId = directory.getParentId();
                        while (true) {
                            List<Directory> otherDirs = getOtherDirByParentId.apply(parentId);
                            otherDirectories.addAll(otherDirs);
                            Directory parentDir = allDirIdMap.get(parentId);
                            if (parentDir == null) {
                                break;
                            }
                            parentId = parentDir.getParentId();
                        }
                    }
                    // top other_dir's all sub dirs
                    Set<Long> topOtherDirSubDirs = new HashSet<>();
                    for (Directory otherDir : otherDirectories) {
                        if (!allDirIdMap.containsKey(otherDir.getParentId())) {
                            Set<Long> subDirs = findAllSubDirIds.apply(otherDir.getId());
                            topOtherDirSubDirs.addAll(subDirs);
                        }
                    }
                    dirIdsRangeForSearchKu.addAll(productNameSearchedDirIds);
                    dirIdsRangeForSearchKu.addAll(otherDirectories.stream().map(Directory::getId).collect(Collectors.toList()));
                    dirIdsRangeForSearchKu.addAll(topOtherDirSubDirs);
                } else {
                    dirIdsRangeForSearchKu.addAll(strategySpecifiedDirAnsSubDirIds);
                }
            } else {
                dirIdsRangeForSearchKu.addAll(strategySpecifiedDirAnsSubDirIds);
            }
            log.info("qaChat[{}]: search directory, dirIdsRangeForSearchKu={}", qaChatReq.getChatId(), dirIdsRangeForSearchKu);
            // 搜索标签
            Map<String, List<?>> entities = qaChatReq.getEntities();
            List<String> productNames = StringUtil.getProductNamesFromEntities(entities);
            if (CollectionUtil.isNotEmpty(productNames)) {
                List<KuLabelValue> kuLabelValues = kuLabelValueRepository.getKuLabelValuesByName(qaChatReq.getTenantId(), qaChatReq.getProjectId(), productNames);
                log.info("qaChat[{}]: search kuLabelValue, kuLabelValues={}", qaChatReq.getChatId(), kuLabelValues);
                if (CollectionUtil.isNotEmpty(kuLabelValues)) {
                    Map<Long, Set<Long>> kuLabelDimensionId2ValueIds = qaChatReq.getKuLabelDimensionId2ValueIds();
                    if (kuLabelDimensionId2ValueIds == null) {
                        kuLabelDimensionId2ValueIds = new HashMap<>();
                        qaChatReq.setKuLabelDimensionId2ValueIds(kuLabelDimensionId2ValueIds);
                    }
                    for (KuLabelValue kuLabelValue : kuLabelValues) {
                        Long dimensionId = kuLabelValue.getKuLabelDimensionId();
                        Long valueId = kuLabelValue.getId();
                        kuLabelDimensionId2ValueIds.computeIfAbsent(dimensionId, k -> new HashSet<>()).add(valueId);
                    }
                }
            }
            // 召回知识
            List<Pair<String, Object>> termQueryFieldAndValue = new ArrayList<>();
            termQueryFieldAndValue.add(Pair.of(LambdaUtil.getFieldName(KuEsEntity::getPublished), true));
            termQueryFieldAndValue.add(Pair.of(LambdaUtil.getFieldName(KuEsEntity::getStatus), KuStatusEnum.INDEXED));
            termQueryFieldAndValue.add(Pair.of(LambdaUtil.getFieldName(KuEsEntity::getTenantId), qaChatReq.getTenantId()));
            termQueryFieldAndValue.add(Pair.of(LambdaUtil.getFieldName(KuEsEntity::getProjectId), qaChatReq.getProjectId()));
            if (CollectionUtil.isNotEmpty(dirIdsRangeForSearchKu)) {
                termQueryFieldAndValue.add(Pair.of(LambdaUtil.getFieldName(KuEsEntity::getDirectoryId), dirIdsRangeForSearchKu));
            }
            if (CollectionUtil.isNotEmpty(qaChatReq.getKuLabelDimensionId2ValueIds())) {
                Map<Long, Set<Long>> kuLabelDimensionId2ValueIds = qaChatReq.getKuLabelDimensionId2ValueIds();
                // 不同维度之间是 and 关系，同一个维度中的值是 or 关系
                kuLabelDimensionId2ValueIds.forEach((dimensionId, valueIds) -> {
                    if (CollectionUtil.isNotEmpty(valueIds)) {
                        termQueryFieldAndValue.add(Pair.of(LambdaUtil.getFieldName(KuEsEntity::getKuLabelValueIds), valueIds));
                    }
                });
            }
            log.info("qaChat[{}]: termQueryFieldAndValue={}", qaChatReq.getChatId(), termQueryFieldAndValue);
            List<String> excludeFields = EsService.getEmbeddingFieldNames(KuEsEntity.class);
            // 直接返回知识检验
            // 用户原始问题检验
            String userQuestion = IntentService.joinTitleContent(mixedQuery.getOriginTitle(), mixedQuery.getOriginContent());
            if (StringUtils.hasLength(userQuestion)) {
                List<BigDecimal> userQuestionCohereEmbedding = bedrockService.getCohereEmbedding(userQuestion);
                List<EsSearchResult<KuEsEntity>> userQuestionSearchTitleCohereEmbeddingKuHits = esSearchService.embeddingCosSearchAsync(KuEsEntity.indexName, userQuestionCohereEmbedding, LambdaUtil.getFieldName(KuEsEntity::getTitleEmbeddingCohere), aiReplyOption.getTop_k(), termQueryFieldAndValue, excludeFields, KuEsEntity.class).join();
                if (CollectionUtil.isNotEmpty(userQuestionSearchTitleCohereEmbeddingKuHits)) {
                    EsSearchResult<KuEsEntity> mostSimilarKuPair = userQuestionSearchTitleCohereEmbeddingKuHits.get(0);
                    Double score = mostSimilarKuPair.getScore();
                    KuEsEntity mostSimilarKu = mostSimilarKuPair.getData();
                    log.info("qaChat[{}]: first cohere embedding search(userQuestion search title), most similar ku score={}, kuId={}, kuTitle={}, ku={}", qaChatReq.getChatId(), score, mostSimilarKu.getId(), mostSimilarKu.getTitle(), JSON.toJSONString(mostSimilarKu));
                    if (score >= qaChatConfig.getKuDirectReturnUserQuestionCohereSimilarity()) {
                        QaChatRes qaChatRes = new QaChatRes();
                        List<RetrievedKu> retrievedKuList = kuEsEntity2RetrievedKu(userQuestionSearchTitleCohereEmbeddingKuHits);
                        retrievedKuList.get(0).setEffect(true);
                        String response = mostSimilarKu.getContent();
                        try {
                            DetectLanguageResponse responseLanguageRes = gptToolApiFeignClient.detectLanguage(new DetectLanguageRequest(response), invokeGptConfig.getXToken());
                            if (!responseLanguageRes.getLanguage().equals(BotLanguageEnum.valueOf(aiReplyOption.getLanguage().toUpperCase()))) {
                                BotLanguageEnum languageEnum = BotLanguageEnum.valueOf(aiReplyOption.getLanguage().toUpperCase());
                                List<TranslateTagKeywordRes> translateResList = TranslateService.translate(Arrays.asList(mostSimilarKu.getContent()), Arrays.asList(languageEnum));
                                if (CollectionUtil.isNotEmpty(translateResList)) {
                                    TranslateTagKeywordRes translateRes = translateResList.get(0);
                                    response = translateRes.getToLanguageKeyword();
                                }
                            }
                        } catch (Exception e) {
                            log.warn("qaChat[{}]: translate mostSimilarKu's content failed", qaChatReq.getChatId(), e);
                        }
                        qaChatRes.setResult(new QaChatRes.Result()
                                .setStatus("SUCCESS")
                                .setResponse(response)
                                .setReason("CR_RETRIEVE_SUCCESS")
                                .setExplain("The user's questions are basically consistent with the knowledge's question, and the answers in the enterprise knowledge base are directly returned.")
                        );
                        qaChatRes.setMetaData(new QaChatRes.MetaData()
                                .setRetrieveMinScore(qaChatReq.getAiReplyOption().getRetrieve_min_score())
                                .setUsedKus(Collections.singletonList(retrievedKuList.get(0)))
                                .setRerankKus(retrievedKuList)
                        );
                        log.info("qaChat[{}]: first cohere embedding search(userQuestion search title) result satisfy so direct return, similarity={}, threshold={}, kuId={}, kuTitle={}, userQuestion={}", qaChatReq.getChatId(), score, qaChatConfig.getKuDirectReturnUserQuestionCohereSimilarity(), mostSimilarKu.getId(), mostSimilarKu.getTitle(), userQuestion);
                        return qaChatRes;
                    }
                }
            }
            // 总结后的用户问题检验
            List<BigDecimal> knowledgeCohereEmbedding = bedrockService.getCohereEmbedding(mixedQuery.getKnowledgeQuery());
            List<EsSearchResult<KuEsEntity>> knowledgeSearchTitleCohereEmbeddingKuHits = esSearchService.embeddingCosSearchAsync(KuEsEntity.indexName, knowledgeCohereEmbedding, LambdaUtil.getFieldName(KuEsEntity::getTitleEmbeddingCohere), aiReplyOption.getTop_k(), termQueryFieldAndValue, excludeFields, KuEsEntity.class).join();
            if (CollectionUtil.isNotEmpty(knowledgeSearchTitleCohereEmbeddingKuHits)) {
                EsSearchResult<KuEsEntity> mostSimilarKuPair = knowledgeSearchTitleCohereEmbeddingKuHits.get(0);
                Double score = mostSimilarKuPair.getScore();
                KuEsEntity mostSimilarKu = mostSimilarKuPair.getData();
                log.info("qaChat[{}]: first cohere embedding search(knowledge search title), most similar ku score={}, kuId={}, kuTitle={}, ku={}", qaChatReq.getChatId(), score, mostSimilarKu.getId(), mostSimilarKu.getTitle(), JSON.toJSONString(mostSimilarKu));
                if (score >= qaChatConfig.getKuDirectReturnKnowledgeCohereSimilarity()) {
                    QaChatRes qaChatRes = new QaChatRes();
                    List<RetrievedKu> retrievedKuList = kuEsEntity2RetrievedKu(knowledgeSearchTitleCohereEmbeddingKuHits);
                    retrievedKuList.get(0).setEffect(true);
                    String response = mostSimilarKu.getContent();
                    try {
                        DetectLanguageResponse responseLanguageRes = gptToolApiFeignClient.detectLanguage(new DetectLanguageRequest(response), invokeGptConfig.getXToken());
                        if (!responseLanguageRes.getLanguage().equals(BotLanguageEnum.valueOf(aiReplyOption.getLanguage().toUpperCase()))) {
                            BotLanguageEnum languageEnum = BotLanguageEnum.valueOf(aiReplyOption.getLanguage().toUpperCase());
                            List<TranslateTagKeywordRes> translateResList = TranslateService.translate(Arrays.asList(mostSimilarKu.getContent()), Arrays.asList(languageEnum));
                            if (CollectionUtil.isNotEmpty(translateResList)) {
                                TranslateTagKeywordRes translateRes = translateResList.get(0);
                                response = translateRes.getToLanguageKeyword();
                            }
                        }
                    } catch (Exception e) {
                        log.warn("qaChat[{}]: translate mostSimilarKu's content failed", qaChatReq.getChatId(), e);
                    }
                    qaChatRes.setResult(new QaChatRes.Result()
                            .setStatus("SUCCESS")
                            .setResponse(response)
                            .setReason("CR_RETRIEVE_SUCCESS")
                            .setExplain("The user's questions are basically consistent with the knowledge's question, and the answers in the enterprise knowledge base are directly returned.")
                    );
                    qaChatRes.setMetaData(new QaChatRes.MetaData()
                            .setRetrieveMinScore(qaChatReq.getAiReplyOption().getRetrieve_min_score())
                            .setUsedKus(Collections.singletonList(retrievedKuList.get(0)))
                            .setRerankKus(retrievedKuList)
                    );
                    log.info("qaChat[{}]: first cohere embedding search(knowledge search title) result satisfy so direct return, similarity={}, threshold={}, kuId={}, kuTitle={}, knowledgeQuery={}", qaChatReq.getChatId(), score, qaChatConfig.getKuDirectReturnKnowledgeCohereSimilarity(), mostSimilarKu.getId(), mostSimilarKu.getTitle(), mixedQuery.getKnowledgeQuery());
                    return qaChatRes;
                }
            }
            // 无满足直接返回的条件，则进行全部的召回逻辑
            List<BigDecimal> knowledgeMpnetEmbedding = algClient.textEmbed(mixedQuery.getKnowledgeEnQuery()).join().getResult();
            // parallel search
            CompletableFuture<List<EsSearchResult<KuEsEntity>>> titleMpnetEmbeddingKuHitsFuture = esSearchService.embeddingCosSearchAsync(KuEsEntity.indexName, knowledgeMpnetEmbedding, LambdaUtil.getFieldName(KuEsEntity::getTitleEmbeddingMpnet), aiReplyOption.getTop_k(), termQueryFieldAndValue, excludeFields, KuEsEntity.class);
            CompletableFuture<List<EsSearchResult<KuEsEntity>>> titleEsMatchKuHitsFuture = esSearchService.matchPhraseAsync(KuEsEntity.indexName, mixedQuery.getKnowledgeQuery(), LambdaUtil.getFieldName(KuEsEntity::getTitle), aiReplyOption.getTop_k(), termQueryFieldAndValue, excludeFields, KuEsEntity.class);
            CompletableFuture<List<EsSearchResult<KuEsEntity>>> contentCohereEmbeddingKuHitsFuture = esSearchService.embeddingCosSearchAsync(KuEsEntity.indexName, knowledgeCohereEmbedding, LambdaUtil.getFieldName(KuEsEntity::getContentEmbeddingCohere), aiReplyOption.getTop_k(), termQueryFieldAndValue, excludeFields, KuEsEntity.class);
            CompletableFuture<List<EsSearchResult<KuEsEntity>>> contentMpnetEmbeddingKuHitsFuture = esSearchService.embeddingCosSearchAsync(KuEsEntity.indexName, knowledgeMpnetEmbedding, LambdaUtil.getFieldName(KuEsEntity::getContentEmbeddingMpnet), aiReplyOption.getTop_k(), termQueryFieldAndValue, excludeFields, KuEsEntity.class);
            CompletableFuture<List<EsSearchResult<KuEsEntity>>> contentEsMatchKuHitsFuture = esSearchService.matchPhraseAsync(KuEsEntity.indexName, mixedQuery.getKnowledgeQuery(), LambdaUtil.getFieldName(KuEsEntity::getContent), aiReplyOption.getTop_k(), termQueryFieldAndValue, excludeFields, KuEsEntity.class);

            List<EsSearchResult<KuEsEntity>> titleMpnetEmbeddingKuHits = titleMpnetEmbeddingKuHitsFuture.join();
            List<EsSearchResult<KuEsEntity>> titleEsMatchKuHits = titleEsMatchKuHitsFuture.join();
            List<EsSearchResult<KuEsEntity>> contentCohereEmbeddingKuHits = contentCohereEmbeddingKuHitsFuture.join();
            List<EsSearchResult<KuEsEntity>> contentMpnetEmbeddingKuHits = contentMpnetEmbeddingKuHitsFuture.join();
            List<EsSearchResult<KuEsEntity>> contentEsMatchKuHits = contentEsMatchKuHitsFuture.join();
            log.info("qaChat[{}]: ku search finish", qaChatReq.getChatId());
            // 共6个结果，将对应搜索方式的title和content进行合并，得到3路结果
            List<EsSearchResult<KuEsEntity>> cohereEmbeddingKuHits = mergeTwoSearchList(knowledgeSearchTitleCohereEmbeddingKuHits, contentCohereEmbeddingKuHits, aiReplyOption.getTop_k());
            List<EsSearchResult<KuEsEntity>> mpnetEmbeddingKuHits = mergeTwoSearchList(titleMpnetEmbeddingKuHits, contentMpnetEmbeddingKuHits, aiReplyOption.getTop_k());
            List<EsSearchResult<KuEsEntity>> esMatchKuHits = mergeTwoSearchList(titleEsMatchKuHits, contentEsMatchKuHits, aiReplyOption.getTop_k());
            if (CollectionUtil.isNotEmpty(cohereEmbeddingKuHits)) {
                nWayRetrievedKus.add(new AiReplyReq.RetrievedKuData().setRetrieve_type("CohereEmbedding").setRetrieved_kus(kuEsEntity2RetrievedKu(cohereEmbeddingKuHits)));
            }
            if (CollectionUtil.isNotEmpty(mpnetEmbeddingKuHits)) {
                nWayRetrievedKus.add(new AiReplyReq.RetrievedKuData().setRetrieve_type("MpnetEmbedding").setRetrieved_kus(kuEsEntity2RetrievedKu(mpnetEmbeddingKuHits)));
            }
            if (CollectionUtil.isNotEmpty(esMatchKuHits)) {
                nWayRetrievedKus.add(new AiReplyReq.RetrievedKuData().setRetrieve_type("EsMatch").setRetrieved_kus(kuEsEntity2RetrievedKu(esMatchKuHits)));
            }
        } else {
            nWayRetrievedKus.add(new AiReplyReq.RetrievedKuData().setRetrieve_type("SpecifiedKu").setRetrieved_kus(qaChatReq.getSpecifiedKuList()));
        }
        // rerank ku list
        Map<String, RetrievedKu> kuId2Ku = new HashMap<>();
        nWayRetrievedKus.forEach(list -> {
            List<RetrievedKu> retrievedKus = list.getRetrieved_kus();
            retrievedKus.forEach(ku -> {
                String kuId = ku.getKuId().toString();
                RetrievedKu existKu = kuId2Ku.get(kuId);
                if (existKu == null || ku.getScore() > existKu.getScore()) {
                    kuId2Ku.put(kuId, ku);
                }
            });
        });
        if (CollectionUtil.isEmpty(kuId2Ku)) {
            // 没有召回知识则直接返回
            return new QaChatRes().setResult(new QaChatRes.Result().setStatus("DENIED").setExplain("lack of knowledge"));
        }
        AtomicInteger rankCount = new AtomicInteger();
        List<List<String>> nWayKuIds = nWayRetrievedKus.stream().map(kus -> {
            List<String> kuIds = kus.getRetrieved_kus().stream().map(ku -> ku.getKuId().toString()).collect(Collectors.toList());
            rankCount.addAndGet(kuIds.size());
            return kuIds;
        }).collect(Collectors.toList());
        RankRes rankRes = algClient.rank(new RankReq(nWayKuIds, rankCount.get())).join();
        List<String> rankSortKuIds = rankRes.getResult_sort();
        List<RetrievedKu> rankSortKus = rankSortKuIds.stream().map(kuId2Ku::get).collect(Collectors.toList());
        // 取topN ku判断相关性
        int topN = qaChatConfig.getKuRelevanceCountBy(qaChatReq.getTenantId());
        List<RetrievedKu> needJudgeRelevanceKus = new ArrayList<>();
        for (RetrievedKu ku : rankSortKus) {
            if (needJudgeRelevanceKus.size() < topN) {
                needJudgeRelevanceKus.add(ku);
            } else {
                ku.setEffect(false);
            }
        }
        LLMUsages llmUsages = new LLMUsages();
        ParallelUtil.parallelAsyncCall(3, false, needJudgeRelevanceKus, ku -> {
            RelevantScoreReq req = new RelevantScoreReq().setClient(qaChatReq.getClientType())
                    .setTenant_id(qaChatReq.getTenantId())
                    .setChat_id(qaChatReq.getChatId())
                    .setTouch_point(qaChatReq.getTouchPoint())
                    .setPdt(qaChatReq.getPdt())
                    .setIntent(qaChatReq.getIntent())
                    .setLanguage(aiReplyOption.getLanguage())
                    .setQuestion(mixedQuery.getKnowledgeEnQuery())
                    .setContext(ku.getTitle() + " " + ku.getContent());
            return algClient.relevantScore(req).thenAccept(relevantScoreRes -> {
                LLMUsages usages = relevantScoreRes.getUsages();
                llmUsages.merge(usages);
                Float relevantMinScore = relevantScoreRes.getResult().getRelevant_min_score();
                Float relevantScore = relevantScoreRes.getResult().getRelevant_score();
                ku.setRelevantScore(relevantScore);
                ku.setEffect(relevantScore >= relevantMinScore);
            });
        });
        boolean hasRelevantKu = needJudgeRelevanceKus.stream().anyMatch(RetrievedKu::isEffect);
        if (!hasRelevantKu) {
            // 没有相关的知识则直接返回
            return new QaChatRes().setResult(new QaChatRes.Result().setStatus("DENIED").setExplain("lack of relevant knowledge").setNeedImproved(true)).setUsages(llmUsages);
        }
        List<RetrievedKu> relevantSortKus = new ArrayList<>(rankSortKus);
        relevantSortKus.sort((ku1, ku2) -> {
            if (ku1.isEffect()) {
                if (ku2.isEffect()) {
                    return ku2.getRelevantScore().compareTo(ku1.getRelevantScore());
                } else {
                    return -1;
                }
            } else {
                if (ku2.isEffect()) {
                    return 1;
                } else {
                    return Float.compare(ku2.getScore(), ku1.getScore());
                }
            }
        });
        // 相关的ku进行prompt拼接
        List<String> kuContentList = new ArrayList<>();
        for (RetrievedKu ku : relevantSortKus) {
            if (ku.isEffect()) {
                String title = ku.getTitle();
                String content = ku.getContent();
                if (LLMTypeEnum.HAIKU.equals(qaChatReq.getModelType())) {
                    kuContentList.add(String.format("<question>\n%s\n</question>\n<answer>\n%s\n</answer>", title, content));
                } else {
                    kuContentList.add(String.format("Question: %s\nAnswer: %s", title, content));
                }
            }
        }
        String kuContents = String.join("\n\n", kuContentList);
        int wordsLimit = aiReplyOption.getWords_limit() != null ? aiReplyOption.getWords_limit() : qaChatConfig.getDocumentRespWordsLimit();
        String qaSystemContent = promptConfig.getSystemContent(qaChatReq.getTenantId(), qaChatReq.getAiReplyPromptInfo(), kuContents, aiReplyOption.getLanguage(), wordsLimit, qaChatReq.getModelType());
        log.info("qaChat[{}]: qaSystemContent={}", qaChatReq.getChatId(), qaSystemContent);
        // 组装result，保持兼容
        Double retrieveMinScore = null, relevantMinScore = null, relevantTotalScore = 0.0, usedKuRelevantTotalScore = 0.0;
        int relevantKusCount = 0, usedKuRelevantKusCount = 0;
        for (RetrievedKu ku : relevantSortKus) {
            if (retrieveMinScore == null || ku.getScore() < retrieveMinScore) {
                retrieveMinScore = (double) ku.getScore();
            }
            if ((relevantMinScore == null && ku.getRelevantScore() != null) || (ku.getRelevantScore() != null && ku.getRelevantScore() < relevantMinScore)) {
                relevantMinScore = Double.valueOf(ku.getRelevantScore());
            }
            if (ku.getRelevantScore() != null) {
                relevantTotalScore += ku.getRelevantScore();
                relevantKusCount++;
            }
            if (ku.isEffect()) {
                usedKuRelevantTotalScore += ku.getRelevantScore();
                usedKuRelevantKusCount++;
            }

        }
        QaChatRes qaChatRes = new QaChatRes()
                .setGptSystemContent(qaSystemContent)
                .setContextContent(kuContents)
                .setUsages(llmUsages)
                .setResult(new QaChatRes.Result()
                        .setStatus("SUCCESS")
                )
                .setMetaData(new QaChatRes.MetaData()
                        .setRetrieveMinScore(retrieveMinScore)
                        .setRelevantMinScore(relevantMinScore)
                        .setRelevantKusCount(relevantKusCount)
                        .setRelevantAvgScore(relevantKusCount == 0 ? null : relevantTotalScore / relevantKusCount)
                        .setUsedKusCount(usedKuRelevantKusCount)
                        .setUsedKusRelevantAvgScore(usedKuRelevantKusCount == 0 ? null : usedKuRelevantTotalScore / usedKuRelevantKusCount)
                        .setUsedKus(relevantSortKus.stream().filter(RetrievedKu::isEffect).collect(Collectors.toList()))
                        .setRerankKus(relevantSortKus)
                );
        log.info("qaChat[{}]: qaChatRes finish", qaChatReq.getChatId());
        return qaChatRes;
    }

    public static List<EsSearchResult<KuEsEntity>> mergeTwoSearchList(List<EsSearchResult<KuEsEntity>> l1, List<EsSearchResult<KuEsEntity>> l2, int topK) {
        Map<Long, Double> id2score = new HashMap<>();
        Map<Long, KuEsEntity> id2Entity = new HashMap<>();
        ArrayList<EsSearchResult<KuEsEntity>> all = new ArrayList<>(l1);
        all.addAll(l2);
        for (EsSearchResult<KuEsEntity> esSearchResult : all) {
            Double score = esSearchResult.getScore();
            KuEsEntity kuEsEntity = esSearchResult.getData();
            id2Entity.put(kuEsEntity.getId(), kuEsEntity);
            if (!id2score.containsKey(kuEsEntity.getId())) {
                id2score.put(kuEsEntity.getId(), score);
            } else {
                Double existScore = id2score.get(kuEsEntity.getId());
                if (score > existScore) {
                    id2score.put(kuEsEntity.getId(), score);
                }
            }
        }
        List<EsSearchResult<KuEsEntity>> result = id2Entity.entrySet().stream()
                .map(entry -> new EsSearchResult<KuEsEntity>().setScore(id2score.get(entry.getKey())).setData(entry.getValue()))
                .sorted((p1, p2) -> p2.getScore().compareTo(p1.getScore()))
                .collect(Collectors.toList());
        return result;
    }

    public static List<RetrievedKu> kuEsEntity2RetrievedKu(List<EsSearchResult<KuEsEntity>> kuHits) {
        List<RetrievedKu> retrievedKuList = new ArrayList<>();
        for (int i = 0; i < kuHits.size(); i++) {
            EsSearchResult<KuEsEntity> esSearchResult = kuHits.get(i);
            Double score = esSearchResult.getScore();
            KuEsEntity kuEsEntity = esSearchResult.getData();
            RetrievedKu retrievedKu = new RetrievedKu();
            retrievedKu.setRank(i + 1);
            retrievedKu.setKuType(kuEsEntity.getType());
            retrievedKu.setKuId(kuEsEntity.getId());
            retrievedKu.setDocumentId(kuEsEntity.getDocumentId());
            retrievedKu.setDirectoryId(kuEsEntity.getDirectoryId());
            retrievedKu.setScore(score.floatValue());
            retrievedKu.setTitle(kuEsEntity.getTitle());
            retrievedKu.setContent(kuEsEntity.getContent());
            retrievedKu.setOperatorId(kuEsEntity.getCreatedBy());
            retrievedKu.setKuSource(kuEsEntity.getSource());
            retrievedKu.setCreatedAt(DateUtil.date(kuEsEntity.getCreatedAt()).getTime());
            retrievedKu.setUpdatedAt(DateUtil.date(kuEsEntity.getUpdatedAt()).getTime());
            retrievedKu.setTokens(TiktokenUtil.size(retrievedKu.getContent()));
            retrievedKu.setTags(kuEsEntity.getTags());
            retrievedKuList.add(retrievedKu);
        }
        return retrievedKuList;
    }
}
