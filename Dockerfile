FROM openjdk:8-jdk-alpine

RUN apk update
RUN apk add gcompat
RUN apk add busybox-extras
RUN apk add net-tools
RUN apk add htop
RUN apk add --no-cache tini

ARG JAR_NAME
ENV PROFILE="prod"
ENV OPT="-Xmx512m -Xms512m -Xmn256m"
ENV OPT_EXT=""
ENV LD_PRELOAD="/lib/libgcompat.so.0"

WORKDIR /app
VOLUME /app/logs

ADD ${JAR_NAME}.jar app.jar

EXPOSE 8080
ENTRYPOINT tini java -jar -Dfile.encoding=UTF-8 $OPT $OPT_EXT -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=/app/logs/ app.jar --spring.profiles.active=${PROFILE}
