package com.shulex.qabot.alg.dto;

import com.shulex.gpt.apisdk.dto.Message;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
public class SummaryReq {
    Long tenant_id;
    String chat_id;
    String client;
    List<Message> messages;
    String title;
    String content;
    String product;
    String user_language;
    String knowledge_language;
    String model;
    String touch_point;
    String summary_type;
}
