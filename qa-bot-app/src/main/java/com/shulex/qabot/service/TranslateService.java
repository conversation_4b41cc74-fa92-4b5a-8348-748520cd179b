package com.shulex.qabot.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.shulex.gpt.apisdk.enums.BotLanguageEnum;
import com.shulex.qabot.client.res.TranslateTagKeywordRes;
import com.shulex.qabot.util.AsyncHttpUtil;
import groovy.lang.Tuple3;
import lombok.Data;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

public class TranslateService {
    public static List<TranslateTagKeywordRes> translate(List<String> textList, List<BotLanguageEnum> tolanguageList) {
        if (CollectionUtil.isEmpty(textList) || CollectionUtil.isEmpty(tolanguageList)) {
            return Collections.emptyList();
        }
        List<EdgeTranslate.TranslateResult> translateResultList = EdgeTranslate.translate(textList, tolanguageList.stream().map(BotLanguageEnum::getIso).collect(Collectors.toList())).join();
        List<TranslateTagKeywordRes> res = new ArrayList<>();
        for (int i = 0; i < translateResultList.size(); i++) {
            EdgeTranslate.TranslateResult translateResult = translateResultList.get(i);
            String originText = textList.get(i);
            for (EdgeTranslate.Translation translation : translateResult.getTranslations()) {
                String translationText = translation.getText();
                String translationTo = translation.getTo();
                res.add(new TranslateTagKeywordRes().setKeyword(originText).setToLanguageEnum(BotLanguageEnum.ofByIso(translationTo)).setToLanguageKeyword(translationText));
            }
        }
        return res;
    }

    public static class EdgeTranslate {
        private static final String getTokenUrl = "https://edge.microsoft.com/translate/auth";
        private static final String translateUrl = "https://api-edge.cognitive.microsofttranslator.com/translate?api-version=3.0&includeSentenceLength=true";
        private static volatile String token = null;
        private static volatile long tokenGenerateTime = 0;

        public static CompletableFuture<String> translate(String text, String toLanguage) {
            return translate(Arrays.asList(text), Arrays.asList(toLanguage)).
                    thenApply(translateResults -> translateResults.get(0).getTranslations().get(0).getText());
        }

        public static CompletableFuture<List<TranslateResult>> translate(List<String> textList, List<String> toLanguageList) {
            String token = getToken(System.currentTimeMillis() - tokenGenerateTime >= 1000 * 60 * 5);
            JSONArray reqBody = new JSONArray();
            for (String text : textList) {
                reqBody.add(new JSONObject(MapUtil.of("Text", text)));
            }
            String url = translateUrl + "&to=" + String.join(",", toLanguageList);
            JSONObject header = new JSONObject();
            header.put("authorization", "Bearer " + token);
            header.put("Content-Type", "application/json");
            return AsyncHttpUtil.post(url, reqBody, header, new TypeReference<JSONArray>() {
            }).thenApply(resBody -> resBody.toJavaList(TranslateResult.class));
        }

        private synchronized static String getToken(boolean forceUpdateToken) {
            if (forceUpdateToken || token == null) {
                if ((System.currentTimeMillis() - tokenGenerateTime) > 30000) {
                    token = AsyncHttpUtil.get(getTokenUrl, null, new TypeReference<String>() {
                    }).join();
                    tokenGenerateTime = System.currentTimeMillis();
                }
            }
            return token;
        }

        @Data
        public static class TranslateResult {
            List<Translation> translations;
            DetectedLanguage detectedLanguage;
        }

        @Data
        public static class DetectedLanguage {
            Double score;
            String language;
        }

        @Data
        public static class Translation {
            JSONObject sentLen;
            String text;
            String to;
        }
    }
}
