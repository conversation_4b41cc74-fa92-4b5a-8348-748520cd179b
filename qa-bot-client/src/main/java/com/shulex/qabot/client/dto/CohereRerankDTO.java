package com.shulex.qabot.client.dto;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

public abstract class CohereRerankDTO {
    @Data
    @Accessors(chain = true)
    public static class ReqBody {
        String model;
        String query;
        List<String> documents;
        Integer top_n = null;
        Boolean return_documents = true;
    }


    @Data
    @Accessors(chain = true)
    public static class ResBody {
        String id;
        List<ResItem> results;
        JSONObject meta;

        public Integer getSearchUnits() {
            if (meta != null) {
                JSONObject billedUnits = meta.getJSONObject("billed_units");
                if (billedUnits != null) {
                    return billedUnits.getInteger("search_units");
                }
            }
            return null;
        }
    }

    @Data
    @Accessors(chain = true)
    public static class ResItem {
        Integer index;
        Double relevance_score;
        DocItem document;
    }

    @Data
    @Accessors(chain = true)
    public static class DocItem {
        String text;
    }
}
