package com.shulex.qabot.controller;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.shulex.common.user.token.Authed;
import com.shulex.common.user.utils.CactusUtil;
import com.shulex.common.util.utils.EmptyUtil;
import com.shulex.gpt.apisdk.enums.BotLanguageEnum;
import com.shulex.qabot.client.dto.*;
import com.shulex.qabot.client.dto.esentity.CategorySampleEsEntity;
import com.shulex.qabot.client.req.*;
import com.shulex.qabot.client.res.IntentParseRes;
import com.shulex.qabot.client.res.QaChatRes;
import com.shulex.qabot.client.res.TranslateTagKeywordRes;
import com.shulex.qabot.client.res.*;
import com.shulex.qabot.client.req.TagQueryParam;
import com.shulex.qabot.es.EsService;
import com.shulex.qabot.feign.voc.VocHttpClient;
import com.shulex.qabot.service.*;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.io.PrintWriter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@RestController
@Slf4j
public class QaBotController {
    @Autowired
    AiReplyDocumentService aiReplyDocumentService;
    @Autowired
    IntentService intentService;
    @Autowired
    AiReplyQaService aiReplyQaService;
    @Autowired
    KnowledgeService knowledgeService;
    @Autowired
    VocHttpClient vocHttpClient;
    @Autowired
    LabelService labelService;

    @ApiOperation(value = "意图识别", hidden = true)
    @Authed(allowInner = true)
    @PostMapping("/intentParse")
    public IntentParseRes intentParse(@RequestBody IntentParseReq intentParseReq) {
        return intentService.intentParse(intentParseReq);
    }

    @ApiOperation(value = "获取标签", hidden = true)
    @Authed(allowInner = true)
    @PostMapping("/getLabelIntent")
    public LabelIntentParseRes getLabelIntent(@RequestBody LabelIntentParseReq intentParseReq) {
        return intentService.getLabelIntent(intentParseReq);
    }

    @ApiOperation(value = "qa问答", hidden = true)
    @Authed(allowInner = true)
    @PostMapping("/qaChat")
    public QaChatRes qaChat(@RequestBody QaChatReq qaChatReq) {
        return aiReplyQaService.qaChat(qaChatReq);
    }

    @ApiOperation(value = "qa问答", hidden = true)
    @Authed(allowInner = true)
    @PostMapping("/qaChatV2")
    public QaChatRes qaChatV2(@RequestBody QaChatReq qaChatReq) {
        return aiReplyQaService.qaChatV2(qaChatReq);
    }

    @ApiOperation(value = "翻译", hidden = true)
    @Authed(allowInner = true)
    @PostMapping("/translateTagKeyword")
    public List<TranslateTagKeywordRes> translateTagKeyword(@RequestBody TranslateTagKeywordReq translateTagKeywordReq) {
        if (CollectionUtil.isEmpty(translateTagKeywordReq.getToLanguageEnums())) {
            List<BotLanguageEnum> toLanguageEnums = Arrays.stream(BotLanguageEnum.values()).filter(e -> e.getIso() != null).collect(Collectors.toList());
            translateTagKeywordReq.setToLanguageEnums(toLanguageEnums);
        }
        List<TranslateTagKeywordRes> translateResList = TranslateService.translate(translateTagKeywordReq.getKeywords(), translateTagKeywordReq.getToLanguageEnums());
        return translateResList;
    }

    @ApiOperation(value = "校验样本")
    @Authed
    @PostMapping("/checkIntent")
    public List<IntentSampleDTO> checkIntent(@RequestBody CheckIntentReq checkIntentReq) {
        if (checkIntentReq.getTenantId() == null) {
            Long tenantId = CactusUtil.getTenantId();
            checkIntentReq.setTenantId(tenantId);
        }
        return intentService.checkIntent(checkIntentReq, null);
    }

    @ApiOperation(value = "校验样本stream")
    @Authed
    @PostMapping("/checkIntentStream")
    public void checkIntentStream(HttpServletResponse response, @RequestBody CheckIntentReq checkIntentReq) throws IOException {
        if (checkIntentReq.getTenantId() == null) {
            Long tenantId = CactusUtil.getTenantId();
            checkIntentReq.setTenantId(tenantId);
        }
        response.setHeader("Content-Type", MediaType.TEXT_EVENT_STREAM_VALUE);
        response.setContentType(MediaType.TEXT_EVENT_STREAM_VALUE);
        response.setCharacterEncoding("UTF-8");
        PrintWriter responseWriter = response.getWriter();
        List<IntentSampleDTO> intentSampleDTOS = intentService.checkIntent(checkIntentReq, checkResult -> {
            try {
                if (!responseWriter.checkError()) {
                    responseWriter.write(new SseResponseDTO<>().setIsEnd(false).setData(checkResult).toSseString());
                    responseWriter.flush();
                    return true;
                }
            } catch (Exception e) {
                log.error("", e);
            }
            return false;
        });
        if (!responseWriter.checkError()) {
            responseWriter.write(new SseResponseDTO<>().setIsEnd(true).setData(intentSampleDTOS).toSseString());
            responseWriter.flush();
        }
    }

    @ApiOperation(value = "保存标签样本")
    @Authed(allowInner = true)
    @PostMapping("/saveLabelSample")
    public void saveLabelSample(@RequestBody List<LabelSampleDTO> labelSampleDTOList) {
        intentService.saveLabelSample(labelSampleDTOList);
    }

    @ApiOperation(value = "删除标签样本")
    @Authed(allowInner = true)
    @PostMapping("/deleteLabelSample")
    public void deleteLabelSample(@RequestBody List<LabelSampleDTO> labelSampleDTOList) {
        intentService.deleteLabelSample(labelSampleDTOList);
    }

    @ApiOperation(value = "搜索标签样本")
    @Authed(allowInner = true)
    @PostMapping("/searchLabelSample")
    public SearchLabelSampleDTO searchLabelSample(@RequestBody List<LabelSampleDTO> labelSampleDTOList) {
        return intentService.searchLabelSample(labelSampleDTOList);
    }


    @ApiOperation(value = "保存样本")
    @Authed
    @PostMapping("/saveIntentSample")
    public void saveIntentSample(@RequestBody List<IntentSampleDTO> intentSampleDTOList) {
        Long tenantId = CactusUtil.getTenantId();
        for (IntentSampleDTO intentSampleDTO : intentSampleDTOList) {
            if (intentSampleDTO.getTenantId() == null) {
                intentSampleDTO.setTenantId(tenantId);
            }
        }
        intentService.saveIntentSample(intentSampleDTOList);
    }

    @ApiOperation(value = "删除样本")
    @Authed
    @PostMapping("/deleteIntentSample")
    public void deleteIntentSample(@RequestBody List<IntentSampleDTO> intentSampleDTOList) {
        intentService.deleteIntentSample(intentSampleDTOList);
    }

    @ApiOperation(value = "修改样本")
    @Authed
    @PostMapping("/updateIntentSample")
    public void updateIntentSample(@RequestBody List<IntentSampleDTO> intentSampleDTOList) {
        intentService.updateIntentSample(intentSampleDTOList);
    }

    @ApiOperation(value = "查询样本")
    @Authed
    @PostMapping("/getIntentSample")
    public List<IntentSampleDTO> getIntentSample(@RequestBody GetIntentSampleReq req) {
        Long tenantId = CactusUtil.getTenantId();
        return intentService.getIntentSample(tenantId, req.getProjectId(), req.getIntentId(), req.getChatIds(), req.getPnLabel());
    }

    @ApiOperation(value = "获取文档问答的上下文")
    @Authed(allowInner = true)
    @PostMapping("/getDocumentQaContext")
    public DocumentQaContext getDocumentQaContext(@RequestBody DocumentQaReq documentQaReq) {
        return aiReplyDocumentService.getDocumentQaContext(documentQaReq);
    }

    @ApiOperation(value = "searchKnowledge")
    @Authed(allowInner = true)
    @PostMapping("/searchKnowledge")
    public KnowledgeSearchRes searchKnowledge(@RequestBody KnowledgeSearchReq searchReq) {
        return knowledgeService.searchKnowledge(searchReq);
    }

    @ApiOperation(value = "getTenantVersion")
    @Authed(allowInner = true)
    @GetMapping("/getTenantVersion")
    public String getTenantVersion(@RequestParam(value = "chatId", required = false) String chatId, @RequestParam(value = "tenantId", required = false) long tenantId) {
        return vocHttpClient.queryTenantVersion(chatId, tenantId);
    }

    @ApiOperation(value = "replyCompare")
    @Authed(allowInner = true)
    @PostMapping("/replyCompare")
    public ReplyCompareRes replyCompare(@RequestBody ReplyCompareParseReq replyCompareParseReq) {
        return labelService.replyCompare(replyCompareParseReq);
    }


    @ApiOperation(value = "searchTags")
    @Authed(allowInner = true)
    @PostMapping("/searchTags")
    public List<String> searchTags(@RequestBody SearchTagsReq searchTagsReq) {
        return intentService.getKeywords(searchTagsReq.getTenantId(), searchTagsReq.getProjectId(), searchTagsReq.getKeyword());
    }


    @ApiOperation(value = "getLabelTag")
    @Authed(allowInner = true)
    @PostMapping("/label/getLabelTag")
    public List<GetLabelTagRes> getLabelTag(@RequestBody GetLabelTagReq req) {
        return labelService.getLabelTags(req.getTenantId(), req.getProjectId(), req.getChatId(), req.getMessageIds());
    }


    @ApiOperation(value = "esSearch")
    @Authed(allowInner = true)
    @PostMapping("/esSearch")
    public Object searchTags(@RequestBody EsSearchReq esSearchReq) {
        return intentService.esSearch(esSearchReq.getTenantId(), esSearchReq.getProjectId(), esSearchReq.getKnowledgeType(), esSearchReq.getTitle(), esSearchReq.getContent(), esSearchReq.getTopSize());
    }

    @ApiOperation(value = "tag报表查询")
    @Authed(allowInner = true)
    @PostMapping("/label/project/{projectId}/report/distribution")
    public List<TagQueryRes> getDistribution(@PathVariable("projectId") Long projectId, @RequestBody TagQueryParam req) {
        Long tenantId = CactusUtil.getTenantId();
        if (req.getTenantId() == null){
            req.setTenantId(tenantId);
        }
        if (req.getProjectId() == null){
            req.setProjectId(projectId);
        }
        return labelService.getDistribution(req);
    }

    @ApiOperation(value = "categorySearch")
    @Authed(allowInner = true)
    @GetMapping("/label/categorySearch")
    public  List<CategorySampleEsEntity> categorySearch(String input){
        return labelService.searchCategory(input);
    }

    @ApiOperation(value = "")
    @PostMapping("/label/getAutoDescribe")
    @Authed(allowInner = true)
    public TagDescriptionRes getAutoDescribe(@RequestBody TagDescriptionReq req){
        return labelService.getAutoDescribe(req);
    }

    @ApiOperation(value = "test")
    @PostMapping("/label/test")
    public String labelTest(@RequestParam("file") MultipartFile file) throws IOException {
        insert(file.getInputStream());
        log.info("test success");
        return "ok";
    }


    public void insert(InputStream inputStream) throws IOException {
        XSSFWorkbook workbook = new XSSFWorkbook(inputStream);
        List<Pair<Integer, List<String>>> table = tableAndIndex(workbook, 0, true);
        List<CategorySampleEsEntity> categorySampleEsEntityList = new ArrayList<>();
        for (int i = 0; i < table.size(); i++) {
            if (i == 0) {
                continue;
            }
            List<String> row = table.get(i).getValue();
            String level1 = row.get(1);
            String level2 = row.get(3);
            String level3 = row.get(5);
            String level4 = row.get(7);
            String id = row.get(8);
            List<String> path = new ArrayList<>();
            if (EmptyUtil.isNotEmpty(level1)) {
                path.add(level1);
            }
            if (EmptyUtil.isNotEmpty(level2)) {
                path.add(level2);
            }
            if (EmptyUtil.isNotEmpty(level3)) {
                path.add(level3);
            }
            if (EmptyUtil.isNotEmpty(level4)) {
                path.add(level4);
            }
            CategorySampleEsEntity categorySampleEsEntity = new CategorySampleEsEntity();
            categorySampleEsEntity.setCategoryPath(path);
            categorySampleEsEntity.setEndCategory(path.get(path.size() - 1));
            categorySampleEsEntity.setSampleId(Math.round(Double.parseDouble(id)));
            categorySampleEsEntityList.add(categorySampleEsEntity);
        }
        log.info("1231");
        Map<String, String> id2JsonData = categorySampleEsEntityList.stream().collect(Collectors.toMap(p->p.getSampleId().toString(), JSON::toJSONString, (v1, v2) -> v2));
        esService.insert(CategorySampleEsEntity.indexName, id2JsonData);
    }

    @Autowired
    EsService esService;

    public static List<Pair<Integer, List<String>>> tableAndIndex(Workbook workbook, int sheetIndex, boolean align) {
        List<Pair<Integer, List<String>>> table = new ArrayList<>();
        Sheet sheet = workbook.getSheetAt(sheetIndex);
        int maxCellNum = 0;
        for (int i = 0; i <= sheet.getLastRowNum(); i++) {
            List<String> row = new ArrayList<>();
            Row sheetRow = sheet.getRow(i);
            if (sheetRow == null) {
                continue;
            }
            for (int j = 0; j < sheetRow.getLastCellNum(); j++) {
                Cell cell = sheetRow.getCell(j);
                String value;
                if (cell == null) {
                    value = "";
                } else {
                    if (CellType.STRING.equals(cell.getCellType())) {
                        value = cell.getStringCellValue();
                    } else if (CellType.NUMERIC.equals(cell.getCellType())) {
                        value = Convert.toStr(cell.getNumericCellValue());
                    } else if (CellType.BLANK.equals(cell.getCellType())) {
                        value = "";
                    } else {
                        cell.setCellType(CellType.STRING);
                        value = cell.getStringCellValue();
                    }
                    value = ObjectUtil.defaultIfNull(value, "").trim();
                }
                if (EmptyUtil.isNotEmpty(value)) {
                    maxCellNum = Math.max(j + 1, maxCellNum);
                }
                row.add(value);
            }
            table.add(new Pair<>(i + 1, row));
        }
        if (align) {
            for (Pair<Integer, List<String>> row : table) {
                if (row.getValue().size() < maxCellNum) {
                    for (int j = row.getValue().size(); j < maxCellNum; j++) {
                        row.getValue().add("");
                    }
                }
            }
        }
        return table;
    }

}
