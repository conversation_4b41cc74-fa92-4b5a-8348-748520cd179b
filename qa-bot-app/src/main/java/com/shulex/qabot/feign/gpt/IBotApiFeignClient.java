package com.shulex.qabot.feign.gpt;

import com.shulex.gpt.apisdk.IBotApi;
import com.shulex.gpt.apisdk.IInternalApi;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Profile;

@FeignClient(name = "shulex-gpt", contextId = "IBotApiFeignClient")
@Profile({"test", "prod"})
public interface IBotApiFeignClient extends IBotApi {
}

@FeignClient(name = "shulex-gpt", contextId = "IBotApiFeignClientDev", url = "http://10.7.4.9:8008")
@Profile({"dev"})
interface IBotApiFeignClientDev extends IBotApi {
}
