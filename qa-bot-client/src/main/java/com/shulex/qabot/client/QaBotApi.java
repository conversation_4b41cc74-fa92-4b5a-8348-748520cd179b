package com.shulex.qabot.client;

import com.shulex.qabot.client.dto.EsSearchResult;
import com.shulex.qabot.client.dto.LabelSampleDTO;
import com.shulex.qabot.client.dto.SearchLabelSampleDTO;
import com.shulex.qabot.client.dto.esentity.LabelSampleEsEntity;
import com.shulex.qabot.client.req.DocumentQaReq;
import com.shulex.qabot.client.req.*;
import com.shulex.qabot.client.res.DocumentQaRes;
import com.shulex.qabot.client.res.IntentParseRes;
import com.shulex.qabot.client.res.QaChatRes;
import com.shulex.qabot.client.res.RecommendProductRes;
import com.shulex.qabot.client.res.TranslateTagKeywordRes;
import org.springframework.web.bind.annotation.*;
import com.shulex.qabot.client.req.IntentParseReq;
import com.shulex.qabot.client.req.QaChatReq;
import com.shulex.qabot.client.req.TranslateTagKeywordReq;
import com.shulex.qabot.client.res.*;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;
import java.util.Map;

import static com.shulex.gpt.apisdk.consts.GptConst.X_TOKEN;

public interface QaBotApi {
    @PostMapping("/intentParse")
    IntentParseRes intentParse(@RequestBody IntentParseReq intentParseReq);

    @PostMapping("/getLabelIntent")
    LabelIntentParseRes getLabelIntent(@RequestBody LabelIntentParseReq intentParseReq);

    @PostMapping("/qaChat")
    QaChatRes qaChat(@RequestBody QaChatReq qaChatReq);

    @PostMapping("/recommendProduct")
    RecommendProductRes recommendProduct(@RequestBody RecommendProductReq req);

    @PostMapping("/qaChatV2")
    QaChatRes qaChatV2(@RequestBody QaChatReq qaChatReq);

    @PostMapping("/translateTagKeyword")
    List<TranslateTagKeywordRes> translateTagKeyword(@RequestBody TranslateTagKeywordReq translateTagKeywordReq);

    @PostMapping("/getDocumentQaContext")
    DocumentQaContext getDocumentQaContext(@RequestBody DocumentQaReq documentQaReq);

    @PostMapping("/getRecommendProductContext")
    RecommendProductContext getRecommendProductContext(@RequestBody RecommendProductReq req);

    @PostMapping("/searchKnowledge")
    KnowledgeSearchRes searchKnowledge(@RequestBody KnowledgeSearchReq searchReq);

    @PostMapping("/saveLabelSample")
    void saveLabelSample(@RequestBody List<LabelSampleDTO> labelSampleDTOList);

    @PostMapping("/deleteLabelSample")
    void deleteLabelSample(@RequestBody List<LabelSampleDTO> labelSampleDTOList);

    @PostMapping("/searchLabelSample")
    SearchLabelSampleDTO searchLabelSample(@RequestBody List<LabelSampleDTO> labelSampleDTOList);

    @GetMapping("/getTenantVersion")
    String getTenantVersion(@RequestParam(value = "chatId", required = false) String chatId, @RequestParam(value = "tenantId", required = false) long tenantId);

    @PostMapping("/replyCompare")
    ReplyCompareRes replyCompare(@RequestBody ReplyCompareParseReq replyCompareParseReq);

    @PostMapping("/searchTags")
    List<String> searchTags(@RequestBody SearchTagsReq searchTagsReq);

    @PostMapping("/esSearch")
    Object esSearch(@RequestBody EsSearchReq esSearchReq);

    @PostMapping("/label/getAutoDescribe")
    TagDescriptionRes getAutoDescribe(@RequestBody TagDescriptionReq req);

    @PostMapping("/label/project/{projectId}/report/distribution")
    List<TagQueryRes> getDistribution(@PathVariable("projectId") Long projectId, @RequestBody TagQueryParam req);

    @PostMapping("/label/getLabelTag")
    List<GetLabelTagRes> getLabelTag(@RequestBody GetLabelTagReq req);
}
