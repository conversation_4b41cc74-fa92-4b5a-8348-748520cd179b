package com.shulex.qabot.mysql.repository;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.shulex.qabot.client.dto.mysqlentity.Ku;
import com.shulex.qabot.mysql.mapper.KuMapper;
import org.springframework.stereotype.Repository;

@Repository
public class KuRepository extends ServiceImpl<KuMapper, Ku> {
    public boolean exits(long tenantId, long projectId) {
        return lambdaQuery().eq(Ku::getTenantId, tenantId).eq(Ku::getProjectId, projectId).exists();
    }
}
