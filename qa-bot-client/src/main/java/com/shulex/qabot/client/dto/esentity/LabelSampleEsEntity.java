package com.shulex.qabot.client.dto.esentity;

import com.shulex.gpt.apisdk.dto.MixedQuery;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

@Data
@Accessors(chain = true)
public class LabelSampleEsEntity {
    public static final String indexName = "label-intent-sample";
    String chatId;
    Long createdAt;
    Boolean pnLabel;
    Long tenantId;
    Long projectId;
    Long treeId;
    Long nodeId;
    String example;
    @EsEmbeddingField
    private List<BigDecimal> exampleEmbeddingCohere;

    public String generateId() {
        return String.format("%s:%s:%s:%s:%s", tenantId, projectId, treeId, nodeId, chatId);
    }
}
