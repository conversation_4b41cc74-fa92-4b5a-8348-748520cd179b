package com.shulex.qabot.client.dto.mysqlentity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.shulex.gpt.apisdk.enums.KuSourceEnum;
import com.shulex.gpt.apisdk.enums.KuStatusEnum;
import com.shulex.gpt.apisdk.enums.KuTypeEnum;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@TableName("ku")
public class Ku {
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    private KuTypeEnum type;
    private Boolean published;
    private KuStatusEnum status;
    private String title;
    private String content;
    private KuSourceEnum source;
    private String metadata;
    private String ext;
    private Long tenantId;
    private Long projectId;
    private Long directoryId;
    private Long documentId;
    private Long parentId;
    private Long prevId;
    private Long nextId;
    private String childIds;
    private Long createdBy;
    private Long updatedBy;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    private Boolean isVisible;
}
