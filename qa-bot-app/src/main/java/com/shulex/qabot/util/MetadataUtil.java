package com.shulex.qabot.util;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

public class MetadataUtil {
    public static <T> T getFirstValue(Map<String, List<Object>> metadata, String key, Class<T> clazz) {
        if (CollectionUtil.isNotEmpty(metadata) && key != null && clazz != null) {
            List<Object> objects = metadata.get(key);
            if (CollectionUtil.isNotEmpty(objects)) {
                for (Object object : objects) {
                    if (clazz.isInstance(object)) {
                        return clazz.cast(object);
                    }
                }
            }
        }
        return null;
    }

    public static <T> T getMetadataFirstValue(Map<String, List<?>> metadata, String key, Class<T> clazz) {
        if (CollectionUtil.isNotEmpty(metadata) && key != null && clazz != null) {
            List<?> objects = metadata.get(key);
            if (CollectionUtil.isNotEmpty(objects)) {
                for (Object object : objects) {
                    if (object != null) {
                        return JSON.parseObject(JSON.toJSONString(object), clazz);
                    }
                }
            }
        }
        return null;
    }
}
