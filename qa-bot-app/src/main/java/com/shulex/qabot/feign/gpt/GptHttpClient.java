package com.shulex.qabot.feign.gpt;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.shulex.gpt.apisdk.dto.LLMUsage;
import com.shulex.gpt.apisdk.dto.Message;
import com.shulex.gpt.apisdk.enums.RoleEnum;
import com.shulex.qabot.util.AsyncHttpUtil;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class GptHttpClient {
    public final String host = "https://desk.shulex.com/api_v2/gpt";

    public AzureChatCompletionResponse gpt4ChatCompletion(List<Message> history, String systemContent, String userContent) {
        String url = host + "/openai/deployments/gpt-4/chat/completions";
        Map<String, Object> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        headers.put("service-id", "qa-bot");
        headers.put("api-key", "AK/hCFzLo4xhB3xrGF4HrnTiTBX53LhCrJY");
        List<AzureChatMessage> messages = new ArrayList<>();
        messages.add(new AzureChatMessage().setRole("system").setContent(systemContent));
        if (CollectionUtil.isNotEmpty(history)) {
            for (Message message : history) {
                if (message.getRole().equals(RoleEnum.USER)) {
                    messages.add(new AzureChatMessage().setRole("user").setContent(message.getContent()));
                } else if (message.getRole().equals(RoleEnum.ASSISTANT)) {
                    messages.add(new AzureChatMessage().setRole("assistant").setContent(message.getContent()));
                }
            }
        }
        messages.add(new AzureChatMessage().setRole("user").setContent(userContent));
        JSONObject body = new JSONObject();
        body.put("messages", messages);
        body.put("temperature", 0);
        AzureChatCompletionResponse res = AsyncHttpUtil.post(url, body, headers, new TypeReference<AzureChatCompletionResponse>() {
        }).join();
        res.setRequestBody(body);
        return res;
    }


    @Data
    @Accessors(chain = true)
    public static class AzureChatCompletionResponse {
        private String id;
        private String object;
        private Long created;
        private String model;
        private List<ChoicesItem> choices;
        private AzureCompletionUsage usage;
        private JSONObject requestBody;
    }

    @Data
    @Accessors(chain = true)
    public static class ChoicesItem {
        private String finishReason;
        private Integer index;
        private AzureChatMessage message;
        private AzureChatMessage delta;
    }

    @Data
    @Accessors(chain = true)
    public static class AzureCompletionUsage {
        private Integer promptTokens;
        private Integer completionTokens;
        private Integer totalTokens;

        public LLMUsage usage() {
            LLMUsage usage = new LLMUsage();
            usage.setPromptTokens(promptTokens);
            usage.setCompletionTokens(completionTokens);
            usage.setTotalTokens(totalTokens);
            return usage;
        }
    }

    @Data
    @Accessors(chain = true)
    public static class AzureChatMessage {
        /**
         * system、user、assistant、function
         */
        private String role;
        private String content;
    }
}
