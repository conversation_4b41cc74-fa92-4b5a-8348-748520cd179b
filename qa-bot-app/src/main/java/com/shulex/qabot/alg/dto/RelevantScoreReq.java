package com.shulex.qabot.alg.dto;

import com.alibaba.fastjson.JSON;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class RelevantScoreReq {
    String client;
    Long tenant_id;
    String chat_id;
    String touch_point;
    String pdt;
    String intent;
    String language;
    String question;
    String context;

    public RelevantScoreReq copy() {
        return JSON.parseObject(JSON.toJSONString(this), RelevantScoreReq.class);
    }
}
