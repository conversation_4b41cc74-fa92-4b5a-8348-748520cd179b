package com.shulex.qabot.client.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
public class GetIntentSampleReq {
    @ApiModelProperty(value = "projectId")
    long projectId;
    @ApiModelProperty(value = "意图id（可选）")
    Long intentId;
    @ApiModelProperty(value = "chatIds（可选）")
    List<String> chatIds;
    @ApiModelProperty(value = "正负样本(可选)")
    Boolean pnLabel;
}
