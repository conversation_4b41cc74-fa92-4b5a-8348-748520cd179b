package com.shulex.qabot.client.dto.mysqlentity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.shulex.qabot.client.dto.typehandler.ListSkuTypeHandler;
import com.shulex.qabot.client.dto.typehandler.ListStringTypeHandler;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

@Data
@Accessors(chain = true)
@TableName(value = "ku_product", autoResultMap = true)
public class KuProduct {
    private Long id;
    private Long tenantId;
    private Long projectId;
    private Boolean enable;
    private String productId;
    private String handle;
    private String title;
    @TableField(value = "`describe`")
    private String describe;
    private String vendor;
    private String category;
    private LocalDateTime publishedAt;
    private Boolean requiresShipping;
    @TableField(typeHandler = ListStringTypeHandler.class)
    private List<String> tags;
    @TableField(typeHandler = ListStringTypeHandler.class)
    private List<String> images;
    private String source;
    private Platform platform;
    private String country;
    private String currency;
    @TableField(typeHandler = ListSkuTypeHandler.class)
    private List<Sku> skuList;
    private Boolean mainPush;
    private Long documentId;
    private Boolean indexed;
    private Long createdBy;
    private Long updatedBy;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    private String ext;
    private String spuUrl;
    private Long directoryId;
    private Boolean isVisible;

    public enum Platform {
        QAM,
        SHOPIFY,
        SELF,
        AMAZON
    }

    @Data
    @Accessors(chain = true)
    public static class Sku {
        String id;
        String productId;
        String title;
        String detailJson;
        String aboutJson;
        String spuUrl;
        String describe;
        String status;
        String brand;
        String category;
        LocalDateTime publishedAt;
        Integer state;
        String country;
        BigDecimal price;
        BigDecimal compareAtPrice;
        String sku;
        Map<String, Object> opt;
        String image;
        LocalDateTime createdAt;
        Long grams;
        Long inventoryQuantity;
    }
}
