package com.shulex.qabot.es.dto;

import com.alibaba.fastjson.JSON;
import com.shulex.qabot.client.dto.esentity.ProductEsEntity;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

@Data
@Accessors(chain = true)
public class ProductEmbeddingSearchParam {
    String index = ProductEsEntity.indexName;
    int size;
    long tenantId;
    long projectId;
    List<BigDecimal> productDetailEmbedding;
    List<BigDecimal> titleEmbedding;
    List<BigDecimal> describeEmbedding;
    List<BigDecimal> chunkContentEmbedding;
    Double minSkuPrice;
    Double maxSkuPrice;
    List<String> titleList;
    List<String> describeList;

    public ProductEmbeddingSearchParam copy() {
        return JSON.parseObject(JSON.toJSONString(this), ProductEmbeddingSearchParam.class);
    }
}
