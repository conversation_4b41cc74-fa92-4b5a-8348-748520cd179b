package com.shulex.qabot.kafka;

import cn.hutool.cache.CacheUtil;
import cn.hutool.cache.impl.LFUCache;
import cn.hutool.core.exceptions.ExceptionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.otter.canal.client.CanalMessageDeserializer;
import com.alibaba.otter.canal.protocol.CanalEntry;
import com.alibaba.otter.canal.protocol.Message;
import com.shulex.gpt.apisdk.enums.KuTypeEnum;
import com.shulex.qabot.alg.enmus.agent.enums.DmsOperateEnum;
import com.shulex.qabot.client.dto.DmsMessage;
import com.shulex.qabot.client.dto.consts.KafkaTopic;
import com.shulex.qabot.config.NoticeConfig;
import com.shulex.qabot.client.dto.mysqlentity.KuLabelRelation;
import com.shulex.qabot.service.KuLabelRelationService;
import com.shulex.qabot.util.RetryUtil;
import com.shulex.qabot.util.canal.FlatMessage;
import com.shulex.qabot.util.canal.MessageUtil;
import lombok.Data;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;

@Component
@Slf4j
public class KuLabelRelationBinlogConsumer {
    @Autowired
    NoticeConfig noticeConfig;
    @Autowired
    KuLabelRelationService kuLabelRelationService;
    @Autowired
    KafkaTemplate<String, byte[]> kafkaTemplate;
    public static final String kuLabelRelationDelayProcessTopic = "ku-label-relation-delay-process-topic";
    final LFUCache<String, Integer> needDelayProcessKuCache = CacheUtil.newLFUCache(1000, 1000 * 300);

    @KafkaListener(topics = {KafkaTopic.DMS_KU_LABEL_RELATION_TOPIC}, groupId = KafkaTopic.DMS_GROUP, concurrency = "1", properties = {"max.poll.records=5"})
    public void dmsKuLabelRelationListener(List<byte[]> records, Acknowledgment ack) {
        List<DmsMessage> dmsMessages = null;
        try {
            dmsMessages = records.stream()
                    .map(dat -> JSON.parseObject(new String(dat), DmsMessage.class))
                    .collect(Collectors.toList());
            log.info("dmsKuListener recordsSize={}, msgSize={}, msgList:\n{}", records.size(), dmsMessages.size(), dmsMessages.stream().map(JSON::toJSONString).collect(Collectors.joining("\n")));
            for (DmsMessage flatMessage : dmsMessages) {
                String operation = flatMessage.getMetadata().getOperation();
                if (flatMessage.getData() != null) {
                    KuLabelRelation mysqlKuLabelRelation = JSON.parseObject(JSON.toJSONString(flatMessage.getData()), KuLabelRelation.class);
                    Map<KuTypeEnum, Set<Long>> kuType2Ids = new HashMap<>();
                    kuType2Ids.computeIfAbsent(mysqlKuLabelRelation.getKuType(), key -> new HashSet<>()).add(mysqlKuLabelRelation.getKuId());
                    CanalEntry.EventType updateType;
                    if (DmsOperateEnum.INSERT.getValue().equals(operation)) {
                        updateType = CanalEntry.EventType.INSERT;
                    } else if (DmsOperateEnum.DELETE.getValue().equals(operation)) {
                        updateType = CanalEntry.EventType.DELETE;
                    } else if (DmsOperateEnum.UPDATE.getValue().equals(operation)) {
                        updateType = CanalEntry.EventType.UPDATE;
                    } else {
                        updateType = null;
                    }
                    if (updateType == null) {
                        log.warn("dmsKuLabelRelationListener can't process the dataTypeChange[{}],flatMessage={}", operation, flatMessage);
                    } else {
                        kuType2Ids.forEach((kuType, kuIds) -> {
                            for (Long kuId : kuIds) {
                                KuLabelRelationProcessData data = new KuLabelRelationProcessData().setKuType(kuType).setKuId(kuId).setUpdateType(updateType);
                                processKuLabelRelation(data);
                            }
                        });
                    }
                }
            }
            ack.acknowledge();
        } catch (Exception e) {
            log.error("dmsKuLabelRelationListener error, flatMessages={}, ", JSON.toJSONString(dmsMessages), e);
            noticeConfig.sendError("dmsKuLabelRelationListener\n" + ExceptionUtil.stacktraceToString(e));
            ack.nack(0, 5000L);
        }
    }


    @KafkaListener(topics = {"ku-label-relation-sync-topic"}, groupId = "qa-bot-group", concurrency = "1", properties = {"max.poll.records=5"})
    public void kuLabelRelationBinlogListener(List<byte[]> records, Acknowledgment ack) {
        List<FlatMessage> flatMessages = null;
        try {
            flatMessages = records.stream().flatMap(record -> {
                Message message = CanalMessageDeserializer.deserializer(record);
                return MessageUtil.convert(message).stream();
            }).collect(Collectors.toList());
            log.info("kuLabelRelationBinlogListener recordsSize={}, msgSize={}, msgList:\n{}", records.size(), flatMessages.size(), flatMessages.stream().map(JSON::toJSONString).collect(Collectors.joining("\n")));
            for (FlatMessage flatMessage : flatMessages) {
                String type = flatMessage.getType();
                List<Map<String, Object>> dataList = flatMessage.getData();
                List<KuLabelRelation> relationList = dataList == null ? new ArrayList<>() : dataList.stream().map(data -> JSON.parseObject(JSON.toJSONString(data), KuLabelRelation.class)).collect(Collectors.toList());
                Map<KuTypeEnum, Set<Long>> kuType2Ids = new HashMap<>();
                for (KuLabelRelation relation : relationList) {
                    if (relation.getKuType() == null) {
                        continue;
                    }
                    kuType2Ids.computeIfAbsent(relation.getKuType(), key -> new HashSet<>()).add(relation.getKuId());
                }
                CanalEntry.EventType updateType;
                if (type.equals(CanalEntry.EventType.INSERT.name())) {
                    updateType = CanalEntry.EventType.INSERT;
                } else if (type.equals(CanalEntry.EventType.DELETE.name())) {
                    updateType = CanalEntry.EventType.DELETE;
                } else if (type.equals(CanalEntry.EventType.UPDATE.name())) {
                    updateType = CanalEntry.EventType.UPDATE;
                } else {
                    updateType = null;
                }
                if (updateType == null) {
                    log.warn("kuLabelRelationBinlogListener can't process the dataTypeChange[{}],flatMessage={}", type, flatMessage);
                } else {
                    kuType2Ids.forEach((kuType, kuIds) -> {
                        for (Long kuId : kuIds) {
                            KuLabelRelationProcessData data = new KuLabelRelationProcessData().setKuType(kuType).setKuId(kuId).setUpdateType(updateType);
                            processKuLabelRelation(data);
                        }
                    });
                }
            }
            ack.acknowledge();
        } catch (Exception e) {
            log.error("kuLabelRelationBinlogListener error, flatMessages={}, ", JSON.toJSONString(flatMessages), e);
            noticeConfig.sendError("kuLabelRelationBinlogListener\n" + ExceptionUtil.stacktraceToString(e));
            ack.nack(0, 5000L);
        }
    }

    @KafkaListener(topics = {kuLabelRelationDelayProcessTopic}, groupId = "qa-bot-group", concurrency = "1", properties = {"max.poll.records=1"})
    public void kuLabelRelationDelayProcessListener(List<byte[]> records, Acknowledgment ack) {
        List<KuLabelRelationProcessData> kuLabelRelationProcessDataList = null;
        try {
            kuLabelRelationProcessDataList = records.stream().map(r -> JSON.parseObject(new String(r), KuLabelRelationProcessData.class)).collect(Collectors.toList());
            for (KuLabelRelationProcessData data : kuLabelRelationProcessDataList) {
                processKuLabelRelation(data);
            }
            ack.acknowledge();
        } catch (Exception e) {
            log.error("kuLabelRelationDelayProcessListener error, flatMessages={}, ", JSON.toJSONString(kuLabelRelationProcessDataList), e);
            noticeConfig.sendError("kuLabelRelationDelayProcessListener\n" + ExceptionUtil.stacktraceToString(e));
            ack.nack(0, 5000L);
        }
    }

    private void processKuLabelRelation(KuLabelRelationProcessData data) {
        KuTypeEnum kuType = data.getKuType();
        Long kuId = data.getKuId();
        CanalEntry.EventType updateType = data.getUpdateType();
        KuLabelRelationService.KuLabelRelationToEsResult result = kuLabelRelationService.retryUpdateKuLabelRelationToEs(kuType, kuId, updateType);
        switch (result) {
            case SUCCESS_UPDATE_KU_IN_ES:
                log.info("processKuLabelRelation success update kuLabelRelation, kuType={}, kuId={}, updateType={}", kuType, kuId, updateType);
                break;
            case NOT_FOUND_KU_IN_ES_AND_MYSQL:
                log.warn("processKuLabelRelation can't find ku in es and mysql, kuType={}, kuId={}, updateType={}", kuType, kuId, updateType);
                break;
            case NOT_FOUND_KU_IN_ES_BUT_IN_MYSQL:
                String msg = String.format("processKuLabelRelation can't find ku in es but in mysql, so send this data to delayTopic, kuType=%s, kuId=%s, updateType=%s", kuType, kuId, updateType);
                log.warn(msg);
                RetryUtil.retry(3, 1000, () -> {
                    try {
                        kafkaTemplate.send(kuLabelRelationDelayProcessTopic, JSON.toJSONBytes(data)).get();
                    } catch (InterruptedException | ExecutionException e) {
                        throw new RuntimeException(e);
                    }
                });
                Integer alreadyWaitCount;
                synchronized (needDelayProcessKuCache) {
                    String needDelayProcessKuCacheKey = String.format("%s_%s_%s", kuType, kuId, updateType);
                    alreadyWaitCount = needDelayProcessKuCache.get(needDelayProcessKuCacheKey);
                    if (alreadyWaitCount == null) {
                        alreadyWaitCount = 0;
                    }
                    alreadyWaitCount += 1;
                    needDelayProcessKuCache.put(needDelayProcessKuCacheKey, alreadyWaitCount);
                }
                if (alreadyWaitCount > 10) {
                    noticeConfig.sendError("processKuLabelRelation\nalreadyWaitCount=" + alreadyWaitCount + "\n" + msg);
                }
                try {
                    Thread.sleep(3000L);
                } catch (InterruptedException ignored) {
                }
                break;
        }
    }

    @Data
    @Accessors(chain = true)
    public static class KuLabelRelationProcessData {
        KuTypeEnum kuType;
        Long kuId;
        CanalEntry.EventType updateType;
    }
}
