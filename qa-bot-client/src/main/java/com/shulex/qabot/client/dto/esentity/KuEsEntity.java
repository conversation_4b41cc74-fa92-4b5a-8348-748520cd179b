package com.shulex.qabot.client.dto.esentity;

import cn.hutool.core.bean.BeanUtil;
import com.shulex.gpt.apisdk.enums.KuSourceEnum;
import com.shulex.gpt.apisdk.enums.KuStatusEnum;
import com.shulex.gpt.apisdk.enums.KuTypeEnum;
import com.shulex.qabot.client.dto.mysqlentity.Ku;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

@Data
@Accessors(chain = true)
@NoArgsConstructor
public class KuEsEntity {
    public static final String indexName = "qa-ku";

    private Long id;
    private KuStatusEnum status;
    private Boolean published;
    private String title;
    private String content;
    private KuSourceEnum source;
    private Long tenantId;
    private Long projectId;
    private Long directoryId;
    private Long documentId;
    private Long createdBy;
    private Long updatedBy;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    private KuTypeEnum type;
    private String metadata;
    private String ext;
    private Set<Long> kuLabelValueIds;
    private List<String> tags;
    private String brief;
    private Boolean isVisible;

    @EsEmbeddingField
    private List<BigDecimal> titleEmbeddingCohere;
    @EsEmbeddingField
    private List<BigDecimal> titleEmbeddingMpnet;
    @EsEmbeddingField
    private List<BigDecimal> contentEmbeddingCohere;
    @EsEmbeddingField
    private List<BigDecimal> contentEmbeddingMpnet;

    public KuEsEntity(Ku ku) {
        BeanUtil.copyProperties(ku, this);
    }
}
