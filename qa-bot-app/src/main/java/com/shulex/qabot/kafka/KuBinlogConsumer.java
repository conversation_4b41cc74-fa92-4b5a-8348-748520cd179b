package com.shulex.qabot.kafka;

import cn.hutool.core.exceptions.ExceptionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.otter.canal.client.CanalMessageDeserializer;
import com.alibaba.otter.canal.protocol.CanalEntry;
import com.alibaba.otter.canal.protocol.Message;
import com.shulex.common.util.utils.EmptyUtil;
import com.shulex.qabot.alg.enmus.agent.enums.DmsOperateEnum;
import com.shulex.qabot.client.dto.DmsMessage;
import com.shulex.qabot.client.dto.consts.KafkaTopic;
import com.shulex.qabot.client.dto.esentity.KuEsEntity;
import com.shulex.qabot.config.NoticeConfig;
import com.shulex.qabot.client.dto.mysqlentity.Ku;
import com.shulex.qabot.es.EsService;
import com.shulex.qabot.service.KuService;
import com.shulex.qabot.util.canal.FlatMessage;
import com.shulex.qabot.util.canal.MessageUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;


@Component
@Slf4j
public class KuBinlogConsumer {
    @Autowired
    KuService kuService;
    @Autowired
    NoticeConfig noticeConfig;
    @Autowired
    EsService esService;


    @KafkaListener(topics = {KafkaTopic.DMS_KU_TOPIC}, groupId = KafkaTopic.DMS_GROUP, concurrency = "2", properties = {"max.poll.records=5"})
    public void dmsKuListener(List<byte[]> records, Acknowledgment ack) {
        List<DmsMessage> dmsMessages = null;
        try {
            dmsMessages = records.stream()
                    .map(dat -> JSON.parseObject(new String(dat), DmsMessage.class))
                    .collect(Collectors.toList());
            log.info("dmsKuListener recordsSize={}, msgSize={}, msgList:\n{}", records.size(), dmsMessages.size(), dmsMessages.stream().map(JSON::toJSONString).collect(Collectors.joining("\n")));
            List<Ku> insertKuList = new ArrayList<>();
            List<Ku> deleteKuList = new ArrayList<>();
            Map<Boolean, List<Ku>> needReEmbedding2UpdateKuList = new HashMap<>();
            needReEmbedding2UpdateKuList.put(true, new ArrayList<>());
            needReEmbedding2UpdateKuList.put(false, new ArrayList<>());
            Map<String, KuEsEntity> chunkEsEntityHashMap = new HashMap<>();
            List<String> idList = dmsMessages.stream().filter(p -> p != null && p.getData() != null && p.getData().get("id") != null).map(p -> p.getData().get("id").toString()).distinct().collect(Collectors.toList());
            if (EmptyUtil.isNotEmpty(idList)){
                chunkEsEntityHashMap = esService.getByIds(KuEsEntity.indexName, idList, KuEsEntity.class, EsService.getEmbeddingFieldNames(KuEsEntity.class));
            }
            for (DmsMessage flatMessage : dmsMessages) {
                String operation = flatMessage.getMetadata().getOperation();
                Ku mysqlKu = JSON.parseObject(JSON.toJSONString(flatMessage.getData()), Ku.class);
                if (flatMessage.getData() != null){
                    if (DmsOperateEnum.INSERT.getValue().equals(operation)) {
                        insertKuList.add(mysqlKu);
                    } else if (DmsOperateEnum.DELETE.getValue().equals(operation))  {
                        deleteKuList.add(mysqlKu);
                    } else if (DmsOperateEnum.UPDATE.getValue().equals(operation))  {
                        String kuIdStr = mysqlKu.getId().toString();
                        if (chunkEsEntityHashMap.containsKey(kuIdStr)) {
                            KuEsEntity kuEsEntity = chunkEsEntityHashMap.get(kuIdStr);
                            if (kuEsEntity != null && ((kuEsEntity.getTitle() != null && !kuEsEntity.getTitle().equals(mysqlKu.getTitle())) || (kuEsEntity.getContent() != null && !kuEsEntity.getContent().equals(mysqlKu.getContent())))) {
                                needReEmbedding2UpdateKuList.get(true).add(mysqlKu);
                            }else {
                                needReEmbedding2UpdateKuList.get(false).add(mysqlKu);
                            }
                        }
                    }
                }
            }
            if (insertKuList.size() >= 20) {
                kuService.insertKuToEs(insertKuList, 2);
            } else {
                kuService.insertKuToEs(insertKuList, null);
            }
            needReEmbedding2UpdateKuList.forEach((needReEmbedding, updateKuList) -> kuService.updateKuInEs(updateKuList, needReEmbedding));
            kuService.deleteKuInEs(deleteKuList);
            ack.acknowledge();
        } catch (Exception e) {
            log.error("dmsKuListener error, flatMessages={}, ", JSON.toJSONString(dmsMessages), e);
            noticeConfig.sendError("dmsKuListener\n" + ExceptionUtil.stacktraceToString(e));
            ack.nack(0, 5000L);
        }
    }



    @KafkaListener(topics = {"ku-sync-topic"}, groupId = "qa-bot-group", concurrency = "2", properties = {"max.poll.records=5"})
    public void kuBinlogListener(List<byte[]> records, Acknowledgment ack) {
        List<FlatMessage> flatMessages = null;
        try {
            flatMessages = records.stream().flatMap(record -> {
                Message message = CanalMessageDeserializer.deserializer(record);
                return MessageUtil.convert(message).stream();
            }).collect(Collectors.toList());
            log.info("kuBinlogListener recordsSize={}, msgSize={}, msgList:\n{}", records.size(), flatMessages.size(), flatMessages.stream().map(JSON::toJSONString).collect(Collectors.joining("\n")));
            List<Ku> insertKuList = new ArrayList<>();
            List<Ku> deleteKuList = new ArrayList<>();
            Map<Boolean, List<Ku>> needReEmbedding2UpdateKuList = new HashMap<>();
            needReEmbedding2UpdateKuList.put(true, new ArrayList<>());
            needReEmbedding2UpdateKuList.put(false, new ArrayList<>());
            for (FlatMessage flatMessage : flatMessages) {
                String type = flatMessage.getType();
                List<Map<String, Object>> dataList = flatMessage.getData();
                List<Ku> kuList = dataList == null ? new ArrayList<>() : dataList.stream().map(data -> JSON.parseObject(JSON.toJSONString(data), Ku.class)).collect(Collectors.toList());
                if (type.equals(CanalEntry.EventType.INSERT.name())) {
                    insertKuList.addAll(kuList);
                } else if (type.equals(CanalEntry.EventType.DELETE.name())) {
                    deleteKuList.addAll(kuList);
                } else if (type.equals(CanalEntry.EventType.UPDATE.name())) {
                    List<Map<String, Object>> old = flatMessage.getOld();
                    for (int i = 0; i < kuList.size(); i++) {
                        Ku newKu = kuList.get(i);
                        Map<String, Object> oldKu = old.get(i);
                        boolean needEmbedding = oldKu.containsKey("title") || oldKu.containsKey("content");
                        needReEmbedding2UpdateKuList.get(needEmbedding).add(newKu);
                    }
                }
            }
            if (insertKuList.size() >= 20) {
                kuService.insertKuToEs(insertKuList, 2);
            } else {
                kuService.insertKuToEs(insertKuList, null);
            }
            needReEmbedding2UpdateKuList.forEach((needReEmbedding, updateKuList) -> kuService.updateKuInEs(updateKuList, needReEmbedding));
            kuService.deleteKuInEs(deleteKuList);
            ack.acknowledge();
        } catch (Exception e) {
            log.error("kuBinlogListener error, flatMessages={}, ", JSON.toJSONString(flatMessages), e);
            noticeConfig.sendError("kuBinlogListener\n" + ExceptionUtil.stacktraceToString(e));
            ack.nack(0, 5000L);
        }
    }
}
