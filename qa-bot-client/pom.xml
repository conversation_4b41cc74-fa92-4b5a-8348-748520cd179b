<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.shulex</groupId>
        <artifactId>qa-bot</artifactId>
        <version>1.0-SNAPSHOT</version>
    </parent>

    <artifactId>qa-bot-client</artifactId>
    <version>${client.version}</version>

    <distributionManagement>
        <repository>
            <id>shulex-repository-releases</id>
            <url>https://nexus.shulex-tech.com/repository/maven-releases/</url>
        </repository>
        <snapshotRepository>
            <id>shulex-repository-snapshot</id>
            <url>https://nexus.shulex-tech.com/repository/maven-snapshots/</url>
        </snapshotRepository>
    </distributionManagement>

    <dependencies>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
            <version>3.0.1</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-web</artifactId>
            <version>5.3.13</version>
        </dependency>
        <dependency>
            <groupId>com.shulex</groupId>
            <artifactId>shulex-common-util</artifactId>
            <version>2.0.8-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.shulex</groupId>
            <artifactId>shulex-gpt-apisdk</artifactId>
            <version>1.3.47-RELEASE</version>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-jexl3</artifactId>
            <version>3.3</version>
        </dependency>
        <dependency>
            <groupId>com.shulex</groupId>
            <artifactId>shulex-common-mybatis</artifactId>
            <version>2.0.4-SNAPSHOT</version>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>com.cohere</groupId>-->
<!--            <artifactId>cohere-java</artifactId>-->
<!--            <version>1.0.8</version>-->
<!--        </dependency>-->
    </dependencies>

</project>