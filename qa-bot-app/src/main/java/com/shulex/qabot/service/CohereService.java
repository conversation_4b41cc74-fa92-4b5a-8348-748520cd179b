package com.shulex.qabot.service;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.TypeReference;
import com.shulex.gpt.apisdk.dto.LLMUsage;
import com.shulex.gpt.apisdk.dto.LLMUsages;
import com.shulex.gpt.apisdk.dto.RetrievedKu;
import com.shulex.qabot.client.dto.CohereRerankDTO;
import com.shulex.qabot.client.dto.EsSearchResult;
import com.shulex.qabot.client.dto.esentity.DocumentChunkEsEntity;
import com.shulex.qabot.client.dto.esentity.ProductEsEntity;
import com.shulex.qabot.config.CohereConfig;
import com.shulex.qabot.util.AsyncHttpUtil;
import groovy.lang.Tuple3;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class CohereService {
    @Autowired
    CohereConfig cohereConfig;

    public Map<String, Double> rerank(String query, List<String> texts, LLMUsages usages) {
        if (!StringUtils.hasText(query) || CollectionUtil.isEmpty(texts)) {
            return new HashMap<>();
        }
        texts = texts.stream().filter(StringUtils::hasText).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(texts)) {
            return new HashMap<>();
        }
        Map<String, Object> headers = new HashMap<>();
        headers.put("content-type", "application/json");
        headers.put("accept", "application/json");
        headers.put("Authorization", "bearer " + cohereConfig.getApiKey());
        CohereRerankDTO.ReqBody reqBody = new CohereRerankDTO.ReqBody()
                .setModel(cohereConfig.getModel())
                .setQuery(query)
                .setDocuments(texts);
        CohereRerankDTO.ResBody resBody = AsyncHttpUtil.post(cohereConfig.getUrl(), reqBody, headers, new TypeReference<CohereRerankDTO.ResBody>() {
        }).join();
        if (usages != null) {
            Integer searchUnits = resBody.getSearchUnits();
            if (searchUnits == null) {
                searchUnits = 1;
            }
            usages.merge(LLMUsages.of("cohere-rerank", new LLMUsage(0, 0, searchUnits)));
        }
        List<CohereRerankDTO.ResItem> resItems = resBody.getResults();
        Map<String, Double> rerankMap = new HashMap<>();
        for (CohereRerankDTO.ResItem resItem : resItems) {
            rerankMap.computeIfPresent(resItem.getDocument().getText(), (k, oldScore) -> resItem.getRelevance_score() > oldScore ? resItem.getRelevance_score() : oldScore);
            rerankMap.putIfAbsent(resItem.getDocument().getText(), resItem.getRelevance_score());
        }
        return rerankMap;
    }

    public <T> CompletableFuture<List<EsSearchResult<T>>> rerank(String query, List<T> data, Function<T, String> getContentFunc, LLMUsages usages) {
        if (!StringUtils.hasText(query) || CollectionUtil.isEmpty(data)) {
            return CompletableFuture.completedFuture(new ArrayList<>());
        }
        List<String> contents = data.stream().map(getContentFunc).collect(Collectors.toList());
        Map<String, Object> headers = new HashMap<>();
        headers.put("content-type", "application/json");
        headers.put("accept", "application/json");
        headers.put("Authorization", "bearer " + cohereConfig.getApiKey());
        CohereRerankDTO.ReqBody reqBody = new CohereRerankDTO.ReqBody()
                .setModel(cohereConfig.getModel())
                .setQuery(query)
                .setDocuments(contents);
        return AsyncHttpUtil.post(cohereConfig.getUrl(), reqBody, headers, new TypeReference<CohereRerankDTO.ResBody>() {
        }).thenApply(resBody -> {
            if (usages != null) {
                Integer searchUnits = resBody.getSearchUnits();
                if (searchUnits == null) {
                    searchUnits = 1;
                }
                usages.merge(LLMUsages.of("cohere-rerank", new LLMUsage(0, 0, searchUnits)));
            }
            List<CohereRerankDTO.ResItem> resItems = resBody.getResults();
            List<EsSearchResult<T>> results = resItems.stream().map(item ->
                            new EsSearchResult<T>()
                                    .setScore(item.getRelevance_score())
                                    .setData(data.get(item.getIndex())))
                    .collect(Collectors.toList());
            return results;
        });
    }

    public List<KnowledgeService.KnowledgeSortResult> rerankKnowledge(String query, List<RetrievedKu> kuList, List<List<DocumentChunkEsEntity>> chunkList, List<ProductEsEntity> productList, LLMUsages usages) {
        int retry = 3;
        Exception exception = null;
        while (retry-- > 0) {
            try {
                int kuSize = CollectionUtil.isEmpty(kuList) ? 0 : kuList.size();
                int chunkSize = CollectionUtil.isEmpty(chunkList) ? 0 : chunkList.size();
                int productSize = CollectionUtil.isEmpty(productList) ? 0 : productList.size();
                // 只有1个知识则不进行调用
                if (kuSize + chunkSize + productSize == 1) {
                    KnowledgeService.KnowledgeSortResult knowledgeSortResult = new KnowledgeService.KnowledgeSortResult().setRerankRank(1).setRerankScore(Double.MAX_VALUE);
                    if (kuSize == 1) {
                        knowledgeSortResult.setKu(kuList.get(0));
                    }
                    if (chunkSize == 1) {
                        knowledgeSortResult.setChunk(chunkList.get(0));
                    }
                    if (productSize == 1) {
                        knowledgeSortResult.setProduct(productList.get(0));
                    }
                    return Arrays.asList(knowledgeSortResult);
                }
                List<String> contents = new ArrayList<>();
                if (CollectionUtil.isNotEmpty(kuList)) {
                    for (RetrievedKu ku : kuList) {
                        contents.add(String.format("%s\n%s", ku.getTitle(), ku.getContent()));
                    }
                }
                if (CollectionUtil.isNotEmpty(chunkList)) {
                    for (List<DocumentChunkEsEntity> docChunks : chunkList) {
                        contents.add(docChunks.stream().map(DocumentChunkEsEntity::getContent).collect(Collectors.joining("\n")));
                    }
                }
                if (CollectionUtil.isNotEmpty(productList)) {
                    for (ProductEsEntity product : productList) {
                        contents.add(String.format("%s\n%s", product.getTitle(), product.getDescribe()));
                    }
                }
                List<CohereRerankDTO.ResItem> resItems = new ArrayList<>();
                if (StringUtils.hasText(query) && CollectionUtil.isNotEmpty(contents)) {
                    Map<String, Object> headers = new HashMap<>();
                    headers.put("content-type", "application/json");
                    headers.put("accept", "application/json");
                    headers.put("Authorization", "bearer " + cohereConfig.getApiKey());
                    CohereRerankDTO.ReqBody reqBody = new CohereRerankDTO.ReqBody()
                            .setModel(cohereConfig.getModel())
                            .setQuery(query)
                            .setDocuments(contents);
                    CohereRerankDTO.ResBody resBody = AsyncHttpUtil.post(cohereConfig.getUrl(), reqBody, headers, new TypeReference<CohereRerankDTO.ResBody>() {
                    }).join();
                    if (usages != null) {
                        Integer searchUnits = resBody.getSearchUnits();
                        if (searchUnits == null) {
                            searchUnits = 1;
                        }
                        usages.merge(LLMUsages.of("cohere-rerank", new LLMUsage(0, 0, searchUnits)));
                    }
                    resItems = resBody.getResults();
                }
                List<KnowledgeService.KnowledgeSortResult> knowledgeSortResults = new ArrayList<>();
                int rank = 1;
                for (CohereRerankDTO.ResItem resItem : resItems) {
                    Double relevanceScore = resItem.getRelevance_score();
                    Integer index = resItem.getIndex();
                    KnowledgeService.KnowledgeSortResult knowledgeSortResult = new KnowledgeService.KnowledgeSortResult()
                            .setRerankRank(rank++)
                            .setRerankScore(relevanceScore);
                    if (index < kuSize) {
                        knowledgeSortResult.setKu(kuList.get(index));
                    } else if (index < kuSize + chunkSize) {
                        knowledgeSortResult.setChunk(chunkList.get(index - kuSize));
                    } else if (index < kuSize + chunkSize + productSize) {
                        knowledgeSortResult.setProduct(productList.get(index - kuSize - chunkSize));
                    }
                    knowledgeSortResults.add(knowledgeSortResult);
                }
                return knowledgeSortResults;
            } catch (Exception e) {
                log.warn("rerank fail", e);
                exception = e;
            }
        }
        throw new RuntimeException(exception);
    }
}
