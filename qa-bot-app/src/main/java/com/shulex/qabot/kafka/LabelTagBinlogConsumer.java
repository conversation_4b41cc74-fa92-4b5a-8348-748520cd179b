package com.shulex.qabot.kafka;

import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.lang.func.Func1;
import cn.hutool.core.lang.func.LambdaUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.otter.canal.client.CanalMessageDeserializer;
import com.alibaba.otter.canal.protocol.CanalEntry;
import com.alibaba.otter.canal.protocol.Message;
import com.shulex.common.util.utils.EmptyUtil;
import com.shulex.qabot.alg.enmus.agent.enums.DmsOperateEnum;
import com.shulex.qabot.client.dto.DmsMessage;
import com.shulex.qabot.client.dto.DmsMetadata;
import com.shulex.qabot.client.dto.ExternalSyncChatDTO;
import com.shulex.qabot.client.dto.consts.KafkaTopic;
import com.shulex.qabot.client.dto.esentity.LabelTagEsEntity;
import com.shulex.qabot.client.dto.mysqlentity.LabelChatRelation;
import com.shulex.qabot.config.NoticeConfig;
import com.shulex.qabot.es.EsService;
import com.shulex.qabot.service.LabelService;
import com.shulex.qabot.util.canal.FlatMessage;
import com.shulex.qabot.util.canal.MessageUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


@Component
@Slf4j
public class LabelTagBinlogConsumer {
    @Autowired
    NoticeConfig noticeConfig;
    @Autowired
    LabelService labelService;
    @Autowired
    EsService esService;


    @KafkaListener(topics = {"label-tag-file-event"}, groupId = "label-tag-file-event-group", concurrency = "2", properties = {"max.poll.records=5"})
    public void tagFileEventListener(List<byte[]> records, Acknowledgment ack){
        try {
            List<LabelTagEsEntity> labelTagEsEntities = records.stream().map(record -> JSON.parseObject(new String(record), LabelTagEsEntity.class)).collect(Collectors.toList());
            log.info("tagFileEventListener recordsSize={}, msgSize={}, labelTagEsEntities:\n{}", records.size(), records.size(), labelTagEsEntities);
            labelService.insertLabelTagEsEntity(labelTagEsEntities);
            ack.acknowledge();
        } catch (Exception e) {
            log.error("tagFileEventListener error, records={}, ", JSON.toJSONString(records), e);
            noticeConfig.sendError("tagFileEventListener\n" + ExceptionUtil.stacktraceToString(e));
            ack.nack(0, 5000L);
        }
    }




    @KafkaListener(topics = {"external-chat-sync-event"}, groupId = "external-chat-sync-event-group", concurrency = "2", properties = {"max.poll.records=5"})
    public void externalChatSyncListener(List<byte[]> records, Acknowledgment ack){
        try {
            List<ExternalSyncChatDTO> externalSyncChatDTOList = records.stream().map(record -> JSON.parseObject(new String(record), ExternalSyncChatDTO.class)).collect(Collectors.toList());
            log.info("externalChatSyncListener recordsSize={}, msgSize={}, labelTagEsEntities:\n{}", records.size(), records.size(), externalSyncChatDTOList);
            for (ExternalSyncChatDTO externalSyncChatDTO : externalSyncChatDTOList) {
                if (externalSyncChatDTO.getChatId() == null){
                    log.info("externalChatSyncListener chatId is null, externalSyncChatDTO:{}", externalSyncChatDTO);
                    continue;
                }
                List<Pair<String, Object>> termQueryFieldAndValue = new ArrayList<>();
                termQueryFieldAndValue.add(Pair.of(LambdaUtil.getFieldName(LabelTagEsEntity::getTenantId), externalSyncChatDTO.getTenantId()));
                termQueryFieldAndValue.add(Pair.of(LambdaUtil.getFieldName(LabelTagEsEntity::getProjectId), externalSyncChatDTO.getProjectId()));
                termQueryFieldAndValue.add(Pair.of(getFieldKeyword(LabelTagEsEntity::getPlatform), externalSyncChatDTO.getPlatform()));
                termQueryFieldAndValue.add(Pair.of(getFieldKeyword(LabelTagEsEntity::getTicketId), externalSyncChatDTO.getTicketId()));
                esService.updateByQuery(LabelTagEsEntity.indexName, termQueryFieldAndValue, String.format("ctx._source.chatId = '%s'", externalSyncChatDTO.getChatId()));
            }
            ack.acknowledge();
        } catch (Exception e) {
            log.error("externalChatSyncListener error, records={}, ", JSON.toJSONString(records), e);
            noticeConfig.sendError("externalChatSyncListener\n" + ExceptionUtil.stacktraceToString(e));
            ack.nack(0, 5000L);
        }
    }

    private <T> String getFieldKeyword(Func1<T, ?> func){
        return LambdaUtil.getFieldName(func) + ".keyword";
    }


    @KafkaListener(topics = {"label-tag-external-event"}, groupId = "label-tag-external-event-group", concurrency = "2", properties = {"max.poll.records=5"})
    public void tagExternalEventListener(List<byte[]> records, Acknowledgment ack){
        try {
            List<LabelTagEsEntity> labelTagEsEntities = records.stream().map(record -> JSON.parseObject(new String(record), LabelTagEsEntity.class)).collect(Collectors.toList());
            log.info("tagExternalEventListener recordsSize={}, msgSize={}, labelTagEsEntities:\n{}", records.size(), records.size(), labelTagEsEntities);
            labelService.insertLabelTagEsEntity(labelTagEsEntities);
            ack.acknowledge();
        } catch (Exception e) {
            log.error("tagExternalEventListener error, records={}, ", JSON.toJSONString(records), e);
            noticeConfig.sendError("tagExternalEventListener\n" + ExceptionUtil.stacktraceToString(e));
            ack.nack(0, 5000L);
        }
    }

    @KafkaListener(topics = {KafkaTopic.DMS_LABEL_CHAT_RELATION_TOPIC}, groupId = KafkaTopic.DMS_GROUP, concurrency = "2", properties = {"max.poll.records=5"})
    public void dmsLabelChatRelationListener(List<byte[]> records, Acknowledgment ack) {
        try {
            List<DmsMessage> dmsMessages = records.stream()
                    .map(dat -> JSON.parseObject(new String(dat), DmsMessage.class))
                    .collect(Collectors.toList());
            log.info("dmsLabelChatRelationListener recordsSize={}, msgSize={}, msgList:\n{}", records.size(), dmsMessages.size(), dmsMessages.stream().map(JSON::toJSONString).collect(Collectors.joining("\n")));
            List<LabelTagEsEntity> insertLabelTagEsEntityList = new ArrayList<>();
            List<LabelTagEsEntity> updateLabelTagEsEntityList = new ArrayList<>();
            List<LabelTagEsEntity> deleteLabelTagEsEntityList = new ArrayList<>();
            dmsMessages.forEach(dmsMessage -> {
                DmsMetadata dmsMetadata = dmsMessage.getMetadata();
                String operation = dmsMetadata.getOperation();
                if (dmsMessage.getData() != null) {
                    LabelChatRelation relation = JSON.parseObject(JSON.toJSONString(dmsMessage.getData()), LabelChatRelation.class);
                    String metadataStr = relation.getMetadata();
                    LabelChatRelation.Metadata metadata = JSON.parseObject(metadataStr, LabelChatRelation.Metadata.class);
                    List<LabelChatRelation.TagInfo> tagInfos = metadata.getTagInfos();
                    for (LabelChatRelation.TagInfo tagInfo : tagInfos) {
                        LabelTagEsEntity labelTagEsEntity = new LabelTagEsEntity(relation);
                        labelTagEsEntity.setMessageId(tagInfo.getMessageId());
                        if (tagInfo.getTagId() != null) {
                            labelTagEsEntity.setTagId(tagInfo.getTagId());
                        }
                        if (EmptyUtil.isNotEmpty(tagInfo.getTagName())) {
                            labelTagEsEntity.setTagName(tagInfo.getTagName());
                        }
                        if (EmptyUtil.isNotEmpty(tagInfo.getIsMultiTag())) {
                            labelTagEsEntity.setIsMultiTag(tagInfo.getIsMultiTag());
                        }
                        if (EmptyUtil.isNotEmpty(tagInfo.getMultiTagNamePath())) {
                            labelTagEsEntity.setMultiTagNamePath(tagInfo.getMultiTagNamePath());
                        }
                        if (EmptyUtil.isNotEmpty(tagInfo.getTitle())) {
                            labelTagEsEntity.setTitle(tagInfo.getTitle());
                        }
                        if (EmptyUtil.isNotEmpty(tagInfo.getIsAiReply())) {
                            labelTagEsEntity.setIsAiReply(tagInfo.getIsAiReply());
                        }
                        if (tagInfo.getIsMultiTag()) {
                            if (EmptyUtil.isNotEmpty(tagInfo.getMultiTagIdPath())) {
                                labelTagEsEntity.setMultiTagIdPath(tagInfo.getMultiTagIdPath());
                                labelTagEsEntity.setLevel(tagInfo.getMultiTagIdPath().get(0).size());
                            }
                        } else {
                            if (EmptyUtil.isNotEmpty(tagInfo.getTagIdPath())) {
                                labelTagEsEntity.setTagIdPath(tagInfo.getTagIdPath());
                                labelTagEsEntity.setLevel(tagInfo.getTagIdPath().size());
                            }
                        }
                        if (EmptyUtil.isNotEmpty(tagInfo.getTicketCreatedAt())) {
                            labelTagEsEntity.setTicketCreatedAt(tagInfo.getTicketCreatedAt());
                        }
                        if (EmptyUtil.isNotEmpty(tagInfo.getContent())) {
                            labelTagEsEntity.setContent(tagInfo.getContent());
                        }
                        if (EmptyUtil.isNotEmpty(tagInfo.getTagNamePath())) {
                            labelTagEsEntity.setTagNamePath(tagInfo.getTagNamePath());
                            labelTagEsEntity.setParentTagName(tagInfo.getTagNamePath().size() == 1 ? null : tagInfo.getTagNamePath().get(tagInfo.getTagNamePath().size() - 2));
                            labelTagEsEntity.setParentTagId(tagInfo.getTagIdPath().size() == 1 ? null : tagInfo.getTagIdPath().get(tagInfo.getTagIdPath().size() - 2));
                        }
                        if (EmptyUtil.isNotEmpty(tagInfo.getTreeName())) {
                            labelTagEsEntity.setTreeName(tagInfo.getTreeName());
                        }


                        if (DmsOperateEnum.INSERT.getValue().equals(operation)){
                            insertLabelTagEsEntityList.add(labelTagEsEntity);
                        }else if (DmsOperateEnum.UPDATE.getValue().equals(operation)){
                            updateLabelTagEsEntityList.add(labelTagEsEntity);
                        }else if (DmsOperateEnum.DELETE.getValue().equals(operation)){
                            deleteLabelTagEsEntityList.add(labelTagEsEntity);
                        }else {
                            log.warn("dmsLabelChatRelationListener can't process the dataTypeChange[{}],flatMessage={}", operation, dmsMessage);
                        }
                    }
                }
            });
            labelService.insertLabelTagEsEntity(insertLabelTagEsEntityList);
            labelService.updateLabelTagEsEntity(updateLabelTagEsEntityList);
            labelService.deleteLabelTagEsEntity(deleteLabelTagEsEntityList);
            ack.acknowledge();
        }catch (Exception e) {
            log.error("dmsLabelChatRelationListener error, records={}, ", JSON.toJSONString(records), e);
            noticeConfig.sendError("dmsLabelChatRelationListener\n" + ExceptionUtil.stacktraceToString(e));
            ack.nack(0, 5000L);
        }
    }


    @KafkaListener(topics = {"label-tag-sync-topic"}, groupId = "label-tag-group", concurrency = "2", properties = {"max.poll.records=5"})
    public void LabelTagBinlogListener(List<byte[]> records, Acknowledgment ack) {
        List<FlatMessage> flatMessages = null;
        try {
            flatMessages = records.stream().flatMap(record -> {
                Message message = CanalMessageDeserializer.deserializer(record);
                return MessageUtil.convert(message).stream();
            }).collect(Collectors.toList());
            log.info("LabelTagBinlogListener recordsSize={}, msgSize={}, msgList:\n{}", records.size(), flatMessages.size(), flatMessages.stream().map(JSON::toJSONString).collect(Collectors.joining("\n")));
            for (FlatMessage flatMessage : flatMessages) {
                String type = flatMessage.getType();
                List<Map<String, Object>> dataList = flatMessage.getData();
                List<LabelChatRelation> relationList = dataList == null ? new ArrayList<>() : dataList.stream().map(data -> JSON.parseObject(JSON.toJSONString(data), LabelChatRelation.class)).collect(Collectors.toList());
                List<LabelTagEsEntity> labelTagEsEntities = relationList.stream().filter(p->p.getMetadata() != null).flatMap(relation->{
                    List<LabelTagEsEntity> labelTagEsEntityList = new ArrayList<>();
                    String metadataStr = relation.getMetadata();
                    LabelChatRelation.Metadata metadata = JSON.parseObject(metadataStr, LabelChatRelation.Metadata.class);
                    List<LabelChatRelation.TagInfo> tagInfos = metadata.getTagInfos();
                    for (LabelChatRelation.TagInfo tagInfo : tagInfos) {
                        LabelTagEsEntity labelTagEsEntity = new LabelTagEsEntity(relation);
                        labelTagEsEntity.setMessageId(tagInfo.getMessageId());
                        if (tagInfo.getTagId() != null) {
                            labelTagEsEntity.setTagId(tagInfo.getTagId());
                        }
                        if (EmptyUtil.isNotEmpty(tagInfo.getTagName())){
                            labelTagEsEntity.setTagName(tagInfo.getTagName());
                        }
                        if (EmptyUtil.isNotEmpty(tagInfo.getIsMultiTag())){
                            labelTagEsEntity.setIsMultiTag(tagInfo.getIsMultiTag());
                        }
                        if (EmptyUtil.isNotEmpty(tagInfo.getMultiTagNamePath())){
                            labelTagEsEntity.setMultiTagNamePath(tagInfo.getMultiTagNamePath());
                        }
                        if (EmptyUtil.isNotEmpty(tagInfo.getTitle())){
                            labelTagEsEntity.setTitle(tagInfo.getTitle());
                        }
                        if (EmptyUtil.isNotEmpty(tagInfo.getIsAiReply())){
                            labelTagEsEntity.setIsAiReply(tagInfo.getIsAiReply());
                        }
                        if  (tagInfo.getIsMultiTag()){
                            if (EmptyUtil.isNotEmpty(tagInfo.getMultiTagIdPath())){
                                labelTagEsEntity.setMultiTagIdPath(tagInfo.getMultiTagIdPath());
                                labelTagEsEntity.setLevel(tagInfo.getMultiTagIdPath().get(0).size());
                            }
                        }else {
                            if (EmptyUtil.isNotEmpty(tagInfo.getTagIdPath())){
                                labelTagEsEntity.setTagIdPath(tagInfo.getTagIdPath());
                                labelTagEsEntity.setLevel(tagInfo.getTagIdPath().size());
                            }
                        }
                        if (EmptyUtil.isNotEmpty(tagInfo.getTicketCreatedAt())){
                            labelTagEsEntity.setTicketCreatedAt(tagInfo.getTicketCreatedAt());
                        }
                        if (EmptyUtil.isNotEmpty(tagInfo.getContent())){
                            labelTagEsEntity.setContent(tagInfo.getContent());
                        }
                        if (EmptyUtil.isNotEmpty(tagInfo.getTagNamePath())){
                            labelTagEsEntity.setTagNamePath(tagInfo.getTagNamePath());
                            labelTagEsEntity.setParentTagName(tagInfo.getTagNamePath().size() == 1 ? null : tagInfo.getTagNamePath().get(tagInfo.getTagNamePath().size() - 2));
                            labelTagEsEntity.setParentTagId(tagInfo.getTagIdPath().size() == 1 ? null : tagInfo.getTagIdPath().get(tagInfo.getTagIdPath().size() - 2));
                        }
                        if (EmptyUtil.isNotEmpty(tagInfo.getTreeName())){
                            labelTagEsEntity.setTreeName(tagInfo.getTreeName());
                        }
                        labelTagEsEntityList.add(labelTagEsEntity);
                    }
                    return labelTagEsEntityList.stream();
                }).collect(Collectors.toList());

                if (type.equals(CanalEntry.EventType.INSERT.name())) {
                    labelService.insertLabelTagEsEntity(labelTagEsEntities);
                } else if (type.equals(CanalEntry.EventType.DELETE.name())) {
                    labelService.deleteLabelTagEsEntity(labelTagEsEntities);
                } else if (type.equals(CanalEntry.EventType.UPDATE.name())) {
                    labelService.updateLabelTagEsEntity(labelTagEsEntities);
                } else {
                    log.warn("LabelTagBinlogListener can't process the dataTypeChange[{}],flatMessage={}", type, flatMessage);
                }
            }
            ack.acknowledge();
        } catch (Exception e) {
            log.error("labelTagBinlogListener error, records={}, ", JSON.toJSONString(records), e);
            noticeConfig.sendError("labelTagBinlogListener\n" + ExceptionUtil.stacktraceToString(e));
            ack.nack(0, 5000L);
        }
    }




}
