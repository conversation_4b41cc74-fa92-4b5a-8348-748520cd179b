package com.shulex.qabot.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

@Data
@Component
@ConfigurationProperties(prefix = "special-need")
public class SpecialNeedConfig {
    Set<Long> singleRequestIntentIdentifyTenantIds = new HashSet<>();
    Set<Long> singleRequestIntentIdentifyBotIds = new HashSet<>();
    Set<Long> intentParseRemoveProductTenantIds;
    Integer intentCheckParallelCount = 5;
    Map<Long, Boolean> enableNewDocSearch = new HashMap<>();
    Map<String,String> enableNewDocSearchIntentChunk = new HashMap<>();
    Integer knowledgeQaCount = 5;
    Integer knowledgeDocCount = 2;
    Integer knowledgeProductCount = 3;

    public boolean getEnableNewDocSearchBy(Long tenantId) {
        return enableNewDocSearch.getOrDefault(tenantId, false);
    }
}
