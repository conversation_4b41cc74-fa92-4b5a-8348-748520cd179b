package com.shulex.qabot.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.lang.func.LambdaUtil;
import cn.hutool.crypto.digest.DigestUtil;
import com.alibaba.fastjson.JSON;
import com.shulex.common.util.utils.EmptyUtil;
import com.shulex.gpt.apisdk.IBotApi;
import com.shulex.gpt.apisdk.IIntentApi;
import com.shulex.gpt.apisdk.dto.*;
import com.shulex.gpt.apisdk.enums.GenericStatusEnum;
import com.shulex.gpt.apisdk.enums.KuStatusEnum;
import com.shulex.gpt.apisdk.enums.RoleEnum;
import com.shulex.gpt.apisdk.enums.TagSourceEnum;
import com.shulex.qabot.alg.AlgClient;
import com.shulex.qabot.alg.dto.*;
import com.shulex.qabot.client.dto.*;
import com.shulex.qabot.client.dto.esentity.*;
import com.shulex.qabot.client.req.CheckIntentReq;
import com.shulex.qabot.client.req.EsSearchReq;
import com.shulex.qabot.client.req.IntentParseReq;
import com.shulex.qabot.client.req.LabelIntentParseReq;
import com.shulex.qabot.client.res.IntentParseRes;
import com.shulex.qabot.client.res.LabelIntentParseRes;
import com.shulex.qabot.config.LabelConfig;
import com.shulex.qabot.config.QaChatConfig;
import com.shulex.qabot.config.SpecialNeedConfig;
import com.shulex.qabot.es.EsSearchService;
import com.shulex.qabot.es.EsService;
import com.shulex.qabot.feign.voc.VocHttpClient;
import com.shulex.qabot.util.*;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.jexl3.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.BiFunction;
import java.util.function.Function;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static java.lang.String.format;

@Service
@Slf4j
public class IntentService {
    @Autowired
    AlgClient algClient;
    @Autowired
    BedrockService bedrockService;
    @Autowired
    EsService esService;
    @Autowired
    QaChatConfig qaChatConfig;
    @Autowired
    IIntentApi iIntentApiFeignClient;
    @Autowired
    EsSearchService esSearchService;
    @Autowired
    SpecialNeedConfig specialNeedConfig;
    @Autowired
    LabelConfig labelConfig;
    @Autowired
    IBotApi iBotApi;
    @Autowired
    VocHttpClient vocHttpClient;
    private final JexlEngine jexlEngine = new JexlBuilder().create();
    private final Semaphore checkIntentSemaphore = new Semaphore(20);

    public IntentParseRes intentParse(IntentParseReq intentParseReq) {
        // 从metadata获取值
        String summaryType = null;
        Long botId = null;
        if (intentParseReq.getEntities() != null) {
            Map<String, List<Object>> entities = intentParseReq.getEntities();
            for (String key : entities.keySet()) {
                List<Object> values = entities.get(key);
                if (values != null) {
                    values = values.stream().filter(Objects::nonNull).collect(Collectors.toList());
                    entities.put(key, values);
                    if (CollectionUtil.isNotEmpty(values)) {
                        if (key.equalsIgnoreCase("summary_type")) {
                            summaryType = values.get(0).toString();
                        }
                        if (key.equalsIgnoreCase("__bot_id")) {
                            botId = Long.valueOf(values.get(0).toString());
                        }
                    }
                }
            }
        }
        StopWatch stopWatch = new StopWatch("intentParse");
        // 总结用户问题
        stopWatch.start("Summary");
        CompletableFuture<SummaryRes> summaryFuture;
        String productName = null;
        if (CollectionUtil.isNotEmpty(specialNeedConfig.getIntentParseRemoveProductTenantIds()) && !specialNeedConfig.getIntentParseRemoveProductTenantIds().contains(intentParseReq.getTenantId())) {
            Map<String, List<Object>> entities = intentParseReq.getEntities();
            List<String> productNames = StringUtil.getProductNamesFromEntities2(entities);
            if (CollectionUtil.isNotEmpty(productNames)) {
                productName = productNames.get(0);
            }
        }
        SummaryReq summaryReq = new SummaryReq()
                .setTenant_id(intentParseReq.getTenantId())
                .setChat_id(intentParseReq.getChatId())
                .setClient(intentParseReq.getClientType())
                .setMessages(intentParseReq.getHistory())
                .setTitle(intentParseReq.getTitle())
                .setContent(intentParseReq.getContent())
                .setProduct(productName)
                .setUser_language(intentParseReq.getChatLanguage().name())
                .setKnowledge_language(intentParseReq.getRetrieveLanguage().name())
                .setTouch_point(intentParseReq.getTouchPoint())
                .setSummary_type(summaryType);
        log.info("intentParse[{}]: start summary, summaryReq={}", intentParseReq.getChatId(), JSON.toJSONString(summaryReq));
        if (intentParseReq.getMixedQuery() == null) {
            if (StringUtils.isEmpty(intentParseReq.getTitle()) && StringUtils.isEmpty(intentParseReq.getContent())) {
                SummaryRes summaryRes = new SummaryRes()
                        .setUsages(new LLMUsages())
                        .setResult(new SummaryRes.Result().setKnowledgeQuery("").setKnowledgeEnQuery("").setUserQuery("").setUserEnQuery(""));
                summaryFuture = CompletableFuture.completedFuture(summaryRes);
            } else {
                if (Boolean.TRUE.equals(intentParseReq.getIsLivechatPath())) {
                    // Summary， LiveChat不用总结和原文翻译，全部使用原文替代
                    String userQuestion = IntentService.joinTitleContent(intentParseReq.getTitle(), intentParseReq.getContent());
                    SummaryRes summaryRes = new SummaryRes()
                            .setUsages(new LLMUsages())
                            .setResult(new SummaryRes.Result().setKnowledgeQuery(userQuestion).setKnowledgeEnQuery(userQuestion).setUserQuery(userQuestion).setUserEnQuery(userQuestion));
                    summaryFuture = CompletableFuture.completedFuture(summaryRes);
                    log.info("intentParse[{}]: livechat don't summary, intentParseReq={}", intentParseReq.getChatId(), JSON.toJSONString(intentParseReq));
                } else {
                    summaryFuture = algClient.summary(summaryReq);
                }
            }
        } else {
            MixedQuery mixedQuery = intentParseReq.getMixedQuery();
            SummaryRes summaryRes = new SummaryRes()
                    .setUsages(new LLMUsages())
                    .setResult(new SummaryRes.Result()
                            .setKnowledgeQuery(mixedQuery.getKnowledgeQuery())
                            .setKnowledgeEnQuery(mixedQuery.getKnowledgeEnQuery())
                            .setUserQuery(mixedQuery.getUserQuery())
                            .setUserEnQuery(mixedQuery.getUserEnQuery())
                    );
            summaryFuture = CompletableFuture.completedFuture(summaryRes);
        }
        SummaryRes summaryRes = summaryFuture.join();
        stopWatch.stop();
        log.info("intentParse[{}]: summaryRes={}", intentParseReq.getChatId(), summaryRes);
        LLMUsages usages = summaryRes.getUsages();
        if (usages == null) {
            usages = new LLMUsages();
        }
        MixedQuery mixedQuery = new MixedQuery();
        mixedQuery.setUserQuery(summaryRes.getResult().getUserQuery());
        mixedQuery.setKnowledgeQuery(summaryRes.getResult().getKnowledgeQuery());
        mixedQuery.setKnowledgeEnQuery(summaryRes.getResult().getKnowledgeEnQuery());
        mixedQuery.setUserEnQuery(summaryRes.getResult().getUserEnQuery());
        mixedQuery.setTitle(intentParseReq.getTitle());
        mixedQuery.setContent(intentParseReq.getContent());
        mixedQuery.setOriginTitle(intentParseReq.getOriginTitle());
        mixedQuery.setOriginContent(intentParseReq.getOriginContent());
        if (Boolean.FALSE.equals(intentParseReq.getNeedIntentIdentify())) {
            return new IntentParseRes().setMixedQuery(mixedQuery).setUsages(usages);
        }
        // 意图识别
        // 优先级
        String otherIntentName = "other";
        String chatId = intentParseReq.getChatId();
        String touchPoint = intentParseReq.getTouchPoint();
        Boolean isLivechatPath = intentParseReq.getIsLivechatPath();
        boolean userIntentPrecedence = intentParseReq.getUserIntentPrecedence();
        Long tenantId = intentParseReq.getTenantId();
        Long projectId = intentParseReq.getProjectId();
        String clientType = intentParseReq.getClientType();
        String originTitle = intentParseReq.getOriginTitle();
        String originContent = intentParseReq.getOriginContent();
        String userQuestion = joinTitleContent(originTitle, originContent);
        List<TagDTO> tagList = intentParseReq.getTagList();
        List<TagDTO> systemPrecedenceTagList = tagList.stream().filter(tag -> {
            if (tag.getSource().equals(TagSourceEnum.SYSTEM)) {
                for (String systemPrecedenceIntent : qaChatConfig.getSystemPrecedenceIntents()) {
                    if (systemPrecedenceIntent.equalsIgnoreCase(tag.getName())) {
                        return true;
                    }
                }
            }
            return false;
        }).collect(Collectors.toList());
        List<TagDTO> userTagList = tagList.stream().filter(tag -> tag.getSource().equals(TagSourceEnum.MANUAL)).collect(Collectors.toList());
        List<TagDTO> systemTagList = tagList.stream().filter(tag -> tag.getSource().equals(TagSourceEnum.SYSTEM)).collect(Collectors.toList());
        TagDTO verifiedFirstLevelTag = null;
        IntentIdentifyRes intentIdentifyRes = null;
        List<TagDTO> firstPrecedenceTags = systemPrecedenceTagList;
        List<TagDTO> secondPrecedenceTags = userIntentPrecedence ? userTagList : systemTagList;
        List<TagDTO> thirdPrecedenceTags = userIntentPrecedence ? systemTagList : userTagList;
        // 识别1.关键词匹配
        stopWatch.start("KeywordMatch");
        try {
            List<TagDTO> tagDTOs = new ArrayList<>();
            tagDTOs.addAll(firstPrecedenceTags);
            tagDTOs.addAll(secondPrecedenceTags);
            tagDTOs.addAll(thirdPrecedenceTags);
            for (TagDTO tag : tagDTOs) {
                String keywordJexl = tag.getKeywordJexl();
                if (keywordJexl != null) {
                    JexlExpression jexlExpression = jexlEngine.createExpression(keywordJexl);
                    JexlContext mapContext = new MapContext();
                    mapContext.set("userQuestion", userQuestion);
                    Boolean result = (Boolean) jexlExpression.evaluate(mapContext);
                    if (Boolean.TRUE.equals(result)) {
                        log.info("intentParse[{}]: 符合意图[id={},name={}]的关键词规则, userQuestion={}", chatId, tag.getTagId(), tag.getName(), userQuestion);
                        verifiedFirstLevelTag = tag;
                        break;
                    }
                }
            }
        } catch (Exception e) {
            log.error("intentParse: keywordJexl error:", e);
        }
        stopWatch.stop();
        // 识别2.样本库
        stopWatch.start("SampleMatch");
        List<TagDTO> filteredTagList = new ArrayList<>();
        List<BigDecimal> userQuestionEmbedding = null;
        List<BigDecimal> userEnQueryEmbedding = null;
        if (verifiedFirstLevelTag == null || verifiedFirstLevelTag.getName().equalsIgnoreCase(otherIntentName)) {
            String userEnQuery = summaryRes.getResult().getUserEnQuery();
            CompletableFuture<List<BigDecimal>> userQuestionEmbeddingFuture = bedrockService.getCohereEmbeddingAsync(userQuestion);
            CompletableFuture<List<BigDecimal>> userEnQueryEmbeddingFuture = bedrockService.getCohereEmbeddingAsync(userEnQuery);
            userQuestionEmbedding = userQuestionEmbeddingFuture.join();
            userEnQueryEmbedding = userEnQueryEmbeddingFuture.join();
            // query negative samples
            Set<Long> negativeIntentIds = new HashSet<>();
            List<Pair<String, Object>> negativeSampleTermQuery = new ArrayList<>();
            negativeSampleTermQuery.add(Pair.of(LambdaUtil.getFieldName(IntentSampleEsEntity::getTenantId), tenantId));
            negativeSampleTermQuery.add(Pair.of(LambdaUtil.getFieldName(IntentSampleEsEntity::getProjectId), projectId));
            negativeSampleTermQuery.add(Pair.of(LambdaUtil.getFieldName(IntentSampleEsEntity::getIntentId), tagList.stream().map(TagDTO::getTagId).collect(Collectors.toList())));
            negativeSampleTermQuery.add(Pair.of(LambdaUtil.getFieldName(IntentSampleEsEntity::getPnLabel), false));
            CompletableFuture<List<EsSearchResult<IntentSampleEsEntity>>> future1 = esSearchService.embeddingCosSearchAsync(IntentSampleEsEntity.indexName, userQuestionEmbedding, LambdaUtil.getFieldName(IntentSampleEsEntity::getTitleContentEmbeddingCohere), tagList.size() * 3, negativeSampleTermQuery, EsService.getEmbeddingFieldNames(IntentSampleEsEntity.class), IntentSampleEsEntity.class);
            CompletableFuture<List<EsSearchResult<IntentSampleEsEntity>>> future2 = esSearchService.embeddingCosSearchAsync(IntentSampleEsEntity.indexName, userEnQueryEmbedding, LambdaUtil.getFieldName(IntentSampleEsEntity::getSummaryUserEnQueryEmbeddingCohere), tagList.size() * 3, negativeSampleTermQuery, EsService.getEmbeddingFieldNames(IntentSampleEsEntity.class), IntentSampleEsEntity.class);
            Stream.concat(future1.join().stream(), future2.join().stream()).forEach(esResult -> {
                Double score = esResult.getScore();
                if (score >= qaChatConfig.getIntentSampleThreshold(tenantId)) {
                    IntentSampleEsEntity sample = esResult.getData();
                    negativeIntentIds.add(sample.getIntentId());
                }
            });
            filteredTagList = tagList.stream().filter(e -> !negativeIntentIds.contains(e.getTagId())).collect(Collectors.toList());
            if (CollectionUtil.isEmpty(filteredTagList)) {
                verifiedFirstLevelTag = null;
            } else {
                // query positive samples
                List<Pair<String, Object>> positiveSampleTermQuery = new ArrayList<>();
                positiveSampleTermQuery.add(Pair.of(LambdaUtil.getFieldName(IntentSampleEsEntity::getTenantId), tenantId));
                positiveSampleTermQuery.add(Pair.of(LambdaUtil.getFieldName(IntentSampleEsEntity::getProjectId), projectId));
                positiveSampleTermQuery.add(Pair.of(LambdaUtil.getFieldName(IntentSampleEsEntity::getIntentId), filteredTagList.stream().map(TagDTO::getTagId).collect(Collectors.toList())));
                positiveSampleTermQuery.add(Pair.of(LambdaUtil.getFieldName(IntentSampleEsEntity::getPnLabel), true));
                CompletableFuture<List<EsSearchResult<IntentSampleEsEntity>>> userQuestionSearchedSamplesFuture;
                CompletableFuture<List<EsSearchResult<IntentSampleEsEntity>>> userEnQuerySearchedSamplesFuture;
                int sampleSearchSize = filteredTagList.size() * 3;
                // 匹配用户原始问题
                Map<Long, Boolean> userQuestionIntentIdentifyTenantMap = qaChatConfig.getUserQuestionIntentIdentifyTenantMap();
                if (!userQuestionIntentIdentifyTenantMap.containsKey(tenantId) || userQuestionIntentIdentifyTenantMap.get(tenantId)) {
                    userQuestionSearchedSamplesFuture = esSearchService.embeddingCosSearchAsync(IntentSampleEsEntity.indexName, userQuestionEmbedding, LambdaUtil.getFieldName(IntentSampleEsEntity::getTitleContentEmbeddingCohere), sampleSearchSize, positiveSampleTermQuery, EsService.getEmbeddingFieldNames(IntentSampleEsEntity.class), IntentSampleEsEntity.class);
                } else {
                    userQuestionSearchedSamplesFuture = CompletableFuture.completedFuture(new ArrayList<>());
                }
                // 匹配总结后的用户问题
                userEnQuerySearchedSamplesFuture = esSearchService.embeddingCosSearchAsync(IntentSampleEsEntity.indexName, userEnQueryEmbedding, LambdaUtil.getFieldName(IntentSampleEsEntity::getSummaryUserEnQueryEmbeddingCohere), sampleSearchSize, positiveSampleTermQuery, EsService.getEmbeddingFieldNames(IntentSampleEsEntity.class), IntentSampleEsEntity.class);
                // 处理搜出来的样本
                List<EsSearchResult<IntentSampleEsEntity>> userQuestionSearchedSamples = userQuestionSearchedSamplesFuture.join();
                List<EsSearchResult<IntentSampleEsEntity>> userEnQuerySearchedSamples = userEnQuerySearchedSamplesFuture.join();
                List<EsSearchResult<IntentSampleEsEntity>> searchedSamples = new ArrayList<>();
                searchedSamples.addAll(userQuestionSearchedSamples);
                searchedSamples.addAll(userEnQuerySearchedSamples);
                List<EsSearchResult<IntentSampleEsEntity>> hitSamples = searchedSamples.stream().filter(e -> e.getScore() >= qaChatConfig.getIntentSampleThreshold(tenantId)).collect(Collectors.toList());
                // 考虑意图本身的优先级
                if (CollectionUtil.isNotEmpty(hitSamples)) {
                    Map<Long, EsSearchResult<IntentSampleEsEntity>> intentId2HitSample = hitSamples.stream().collect(Collectors.toMap(e -> e.getData().getIntentId(), e -> e, (v1, v2) -> v1));
                    Function<List<TagDTO>, Pair<TagDTO, EsSearchResult<IntentSampleEsEntity>>> findHitSampleFunc = tagDTOList -> {
                        TagDTO sampleMatchedTag = null;
                        EsSearchResult<IntentSampleEsEntity> matchedSample = null;
                        for (TagDTO tag : tagDTOList) {
                            Long intentId = tag.getTagId();
                            EsSearchResult<IntentSampleEsEntity> hitSample = intentId2HitSample.get(intentId);
                            if (hitSample != null) {
                                if (matchedSample == null || matchedSample.getScore() < hitSample.getScore()) {
                                    matchedSample = hitSample;
                                    sampleMatchedTag = tag;
                                }
                            }
                        }
                        if (matchedSample != null && sampleMatchedTag != null) {
                            return Pair.of(sampleMatchedTag, matchedSample);
                        }
                        return null;
                    };
                    Pair<TagDTO, EsSearchResult<IntentSampleEsEntity>> findHitTagPair = findHitSampleFunc.apply(firstPrecedenceTags);
                    if (findHitTagPair == null) {
                        findHitTagPair = findHitSampleFunc.apply(secondPrecedenceTags);
                    }
                    if (findHitTagPair == null) {
                        findHitTagPair = findHitSampleFunc.apply(thirdPrecedenceTags);
                    }
                    if (findHitTagPair != null) {
                        TagDTO sampleMatchedTag = findHitTagPair.getKey();
                        EsSearchResult<IntentSampleEsEntity> matchedSample = findHitTagPair.getValue();
                        log.info("intentParse[{}]: 命中样本库, matchedSample={}, sampleMatchedTag={}", chatId, JSON.toJSONString(matchedSample), JSON.toJSONString(sampleMatchedTag));
                        verifiedFirstLevelTag = sampleMatchedTag;
                    }
                }
            }
        }
        stopWatch.stop();
        // 识别3.大模型
        stopWatch.start("GptIdentify");
        String tenantVersion = StringUtils.hasText(intentParseReq.getTenantVersion()) ? intentParseReq.getTenantVersion() : vocHttpClient.queryTenantVersion(chatId, tenantId);
        boolean simpleIntentIdentify = Boolean.TRUE.equals(isLivechatPath) &&
                (specialNeedConfig.getSingleRequestIntentIdentifyTenantIds().contains(tenantId) || specialNeedConfig.getSingleRequestIntentIdentifyBotIds().contains(botId));
        if ((verifiedFirstLevelTag == null || verifiedFirstLevelTag.getName().equalsIgnoreCase(otherIntentName)) && CollectionUtil.isNotEmpty(filteredTagList)) {
            BiFunction<List<TagDTO>, String, TagDTO> findTagByName = (tagDTOList, name) -> {
                for (TagDTO tagDTO : tagDTOList) {
                    if (tagDTO.getName().equalsIgnoreCase(name)) {
                        return tagDTO;
                    }
                }
                return null;
            };
            // 查询意图的正向样本
            Map<Long, List<EsSearchResult<IntentSampleEsEntity>>> tagId2Samples = new ConcurrentHashMap<>();
            List<BigDecimal> finalUserEnQueryEmbedding = userEnQueryEmbedding;
            ParallelUtil.parallelAsyncCall(3, true, filteredTagList, filteredTag -> {
                List<Pair<String, Object>> positiveSampleTermQuery = new ArrayList<>();
                positiveSampleTermQuery.add(Pair.of(LambdaUtil.getFieldName(IntentSampleEsEntity::getTenantId), tenantId));
                positiveSampleTermQuery.add(Pair.of(LambdaUtil.getFieldName(IntentSampleEsEntity::getProjectId), projectId));
                positiveSampleTermQuery.add(Pair.of(LambdaUtil.getFieldName(IntentSampleEsEntity::getIntentId), filteredTag.getTagId()));
                positiveSampleTermQuery.add(Pair.of(LambdaUtil.getFieldName(IntentSampleEsEntity::getPnLabel), true));
                int size = qaChatConfig.getAlgIntentSampleCount();
                return esSearchService.embeddingCosSearchAsync(IntentSampleEsEntity.indexName, finalUserEnQueryEmbedding, LambdaUtil.getFieldName(IntentSampleEsEntity::getSummaryUserEnQueryEmbeddingCohere), size, positiveSampleTermQuery, EsService.getEmbeddingFieldNames(IntentSampleEsEntity.class), IntentSampleEsEntity.class)
                        .thenAccept(esSearchResults -> {
                            tagId2Samples.put(filteredTag.getTagId(), esSearchResults);
                        });
            });
            // 构造请求大模型的参数
            Set<Long> filteredTagIds = filteredTagList.stream().map(TagDTO::getTagId).collect(Collectors.toSet());
            Long finalBotId = botId;
            BiFunction<Boolean, List<TagDTO>, IntentIdentifyReq> getIdentifyReqFunc = (isUserTag, tagDTOList) -> {
                List<TagDTO> filteredTags = tagDTOList.stream().filter(e -> filteredTagIds.contains(e.getTagId())).collect(Collectors.toList());
                if (CollectionUtil.isEmpty(filteredTags)) {
                    return null;
                }
                List<List<String>> tagNameWithDescList = tagDTOList.stream().filter(e -> StringUtils.hasLength(e.getDescription())).map(e -> Arrays.asList(e.getName(), e.getDescription())).collect(Collectors.toList());
                List<IntentIdentifyReq.Intent> tagObjList = filteredTags.stream().map(tagDTO ->
                                new IntentIdentifyReq.Intent()
                                        .setIntent_name(tagDTO.getName())
                                        .setIntent_description(tagDTO.getDescription())
                                        .setIntent_ai_name(tagDTO.getIntentAiName() != null ? tagDTO.getIntentAiName() : tagDTO.getName()))
                        .collect(Collectors.toList());
                List<IntentIdentifyReq.Example> examples = new ArrayList<>();
                tagId2Samples.forEach((tagId, esSamples) -> {
                    Optional<TagDTO> optionalTagDTO = filteredTags.stream().filter(e -> e.getTagId().equals(tagId)).findFirst();
                    if (optionalTagDTO.isPresent()) {
                        TagDTO tagDTO = optionalTagDTO.get();
                        List<IntentIdentifyReq.Sample> samples = esSamples.stream().map(sample -> new IntentIdentifyReq.Sample()
                                .setScore(sample.getScore())
                                .setText(joinTitleContent(sample.getData().getFirstTurnTitle(), sample.getData().getFirstTurnContent()))
                        ).collect(Collectors.toList());
                        IntentIdentifyReq.Example example = new IntentIdentifyReq.Example().setCategory(tagDTO.getName()).setSamples(samples);
                        examples.add(example);
                    }
                });
                boolean isSystemTag = !isUserTag;
                return new IntentIdentifyReq()
                        .setTenant_id(tenantId)
                        .setBot_id(finalBotId)
                        .setChat_id(chatId)
                        .setClient(clientType)
                        .setCustom_intents(isUserTag ? tagNameWithDescList : null)
                        .setCustom_intents_obj(isUserTag ? tagObjList : null)
                        .setDefault_intents(isSystemTag ? tagNameWithDescList : null)
                        .setUser_en_query(summaryRes.getResult().getUserEnQuery())
                        .setTouch_point(touchPoint)
                        .setMessages(summaryReq.getMessages())
                        .setTitle(summaryReq.getTitle())
                        .setContent(summaryReq.getContent())
                        .setProduct(summaryReq.getProduct())
                        .setExamples(examples)
                        .setTenant_version(tenantVersion)
                        .setMetadata(intentParseReq.getEntities());
            };
            // 请求大模型
            IntentIdentifyReq firstReq = getIdentifyReqFunc.apply(false, systemPrecedenceTagList);
            IntentIdentifyReq secondReq = getIdentifyReqFunc.apply(userIntentPrecedence, userIntentPrecedence ? userTagList : systemTagList);
            IntentIdentifyReq thirdReq = getIdentifyReqFunc.apply(!userIntentPrecedence, userIntentPrecedence ? systemTagList : userTagList);
            TagDTO verifiedTag = null;
            IntentIdentifyRes res = null;
            if (Boolean.TRUE.equals(isLivechatPath)) {
                // 并发调用
                if (simpleIntentIdentify) {
                    IntentIdentifyReq mergeSecondAndThirdReq = new IntentIdentifyReq()
                            .setTenant_id(secondReq.getTenant_id())
                            .setBot_id(secondReq.getBot_id())
                            .setChat_id(secondReq.getChat_id())
                            .setClient(secondReq.getClient())
                            .setUser_en_query(secondReq.getUser_en_query())
                            .setTouch_point(secondReq.getTouch_point())
                            .setMessages(secondReq.getMessages())
                            .setTitle(secondReq.getTitle())
                            .setContent(secondReq.getContent())
                            .setProduct(secondReq.getProduct())
                            .setTenant_version(secondReq.getTenant_version())
                            .setCustom_intents(ListUtils.join(secondReq.getCustom_intents(), thirdReq.getCustom_intents()))
                            .setCustom_intents_obj(ListUtils.join(secondReq.getCustom_intents_obj(), thirdReq.getCustom_intents_obj()))
                            .setDefault_intents(ListUtils.join(secondReq.getDefault_intents(), thirdReq.getDefault_intents()))
                            .setExamples(ListUtils.join(secondReq.getExamples(), thirdReq.getExamples()))
                            .setMetadata(intentParseReq.getEntities());
                    CompletableFuture<IntentIdentifyRes> future = mergeSecondAndThirdReq == null ? CompletableFuture.completedFuture(new IntentIdentifyRes().setResult(new IntentIdentifyRes.Result())) : algClient.intentIdentify(mergeSecondAndThirdReq);
                    res = future.join();
                    usages.merge(res.getUsages());
                    verifiedTag = findTagByName.apply(filteredTagList, res.getResult().getIntent());
                } else {
                    CompletableFuture<IntentIdentifyRes> future1 = firstReq == null ? CompletableFuture.completedFuture(new IntentIdentifyRes().setResult(new IntentIdentifyRes.Result())) : algClient.intentIdentify(firstReq);
                    CompletableFuture<IntentIdentifyRes> future2 = secondReq == null ? CompletableFuture.completedFuture(new IntentIdentifyRes().setResult(new IntentIdentifyRes.Result())) : algClient.intentIdentify(secondReq);
                    CompletableFuture<IntentIdentifyRes> future3 = thirdReq == null ? CompletableFuture.completedFuture(new IntentIdentifyRes().setResult(new IntentIdentifyRes.Result())) : algClient.intentIdentify(thirdReq);
                    res = future1.join();
                    usages.merge(res.getUsages());
                    verifiedTag = findTagByName.apply(filteredTagList, res.getResult().getIntent());
                    if (secondReq != null && (verifiedTag == null || verifiedTag.getName().equalsIgnoreCase(otherIntentName))) {
                        res = future2.join();
                        usages.merge(res.getUsages());
                        verifiedTag = findTagByName.apply(filteredTagList, res.getResult().getIntent());
                    }
                    if (thirdReq != null && (verifiedTag == null || verifiedTag.getName().equalsIgnoreCase(otherIntentName))) {
                        res = future3.join();
                        usages.merge(res.getUsages());
                        verifiedTag = findTagByName.apply(filteredTagList, res.getResult().getIntent());
                    }
                }
            } else {
                if (firstReq != null) {
                    res = algClient.intentIdentify(firstReq).join();
                    usages.merge(res.getUsages());
                    verifiedTag = findTagByName.apply(filteredTagList, res.getResult().getIntent());
                }
                if (secondReq != null && (verifiedTag == null || verifiedTag.getName().equalsIgnoreCase(otherIntentName))) {
                    res = algClient.intentIdentify(secondReq).join();
                    usages.merge(res.getUsages());
                    verifiedTag = findTagByName.apply(filteredTagList, res.getResult().getIntent());
                }
                if (thirdReq != null && (verifiedTag == null || verifiedTag.getName().equalsIgnoreCase(otherIntentName))) {
                    res = algClient.intentIdentify(thirdReq).join();
                    usages.merge(res.getUsages());
                    verifiedTag = findTagByName.apply(filteredTagList, res.getResult().getIntent());
                }
            }
            log.info("intentParse[{}]: alg调用检测出意图, verifiedTag={}, intentIdentifyRes={}", chatId, verifiedTag, res);
            verifiedFirstLevelTag = verifiedTag;
            intentIdentifyRes = res;
        }
        stopWatch.stop();
        // 未识别到, 默认为other
        if (verifiedFirstLevelTag == null) {
            for (TagDTO tagDTO : tagList) {
                if (tagDTO.getName().equalsIgnoreCase("other")) {
                    verifiedFirstLevelTag = tagDTO;
                    break;
                }
            }
        }
        // 子意图识别
        stopWatch.start("SubIntentIdentify");
        TagDTO verifiedSubLevelTag = null;
        if (!Boolean.TRUE.equals(intentParseReq.getExcludeSub()) && verifiedFirstLevelTag != null && !simpleIntentIdentify) {
            List<TagDTO> subTags = verifiedFirstLevelTag.getSubTags();
            if (CollectionUtil.isNotEmpty(subTags)) {
                IntentIdentifyReq intentIdentifyReq = new IntentIdentifyReq()
                        .setTenant_id(tenantId)
                        .setBot_id(botId)
                        .setChat_id(intentParseReq.getChatId())
                        .setClient(clientType)
                        .setCustom_intents(subTags.stream().map(e -> Arrays.asList(e.getName(), e.getDescription())).collect(Collectors.toList()))
                        .setUser_en_query(summaryRes.getResult().getUserEnQuery())
                        .setTouch_point(intentParseReq.getTouchPoint())
                        .setMessages(summaryReq.getMessages())
                        .setTitle(summaryReq.getTitle())
                        .setContent(summaryReq.getContent())
                        .setProduct(summaryReq.getProduct())
                        .setTenant_version(tenantVersion)
                        .setMetadata(intentParseReq.getEntities());
                IntentIdentifyRes subIntentIdentifyRes = algClient.intentIdentify(intentIdentifyReq).join();
                usages.merge(subIntentIdentifyRes.getUsages());
                String subLevelIntentName = subIntentIdentifyRes.getResult().getIntent();
                log.info("intentParse[{}]: 子意图判断, req={}, res={}", intentParseReq.getChatId(), intentIdentifyReq, subIntentIdentifyRes);
                for (TagDTO subTag : subTags) {
                    if (subTag.getName().equalsIgnoreCase(subLevelIntentName) && !subTag.getName().equalsIgnoreCase("other")) {
                        verifiedSubLevelTag = subTag;
                        break;
                    }
                }
            }
        }
        stopWatch.stop();
        log.info("intentParse[{}]: stopWatch={}", intentParseReq.getChatId(), StopWatchUtil.summary(stopWatch));
        String firstLevelOriginIntentName = null;
        if (intentIdentifyRes != null) {
            firstLevelOriginIntentName = intentIdentifyRes.getResult().getOriginIntent();
        }
        if (firstLevelOriginIntentName == null && verifiedFirstLevelTag != null) {
            firstLevelOriginIntentName = verifiedFirstLevelTag.getName();
        }
        return new IntentParseRes()
                .setMixedQuery(mixedQuery)
                .setUsages(usages)
                .setFirstLevelIntentId(verifiedFirstLevelTag == null ? null : verifiedFirstLevelTag.getTagId())
                .setSubLevelIntentId(verifiedSubLevelTag == null ? null : verifiedSubLevelTag.getTagId())
                .setFirstLevelOriginIntentName(firstLevelOriginIntentName);
    }


    public List<IntentSampleDTO> checkIntent(CheckIntentReq checkIntentReq, Function<IntentSampleDTO, Boolean> checkResultProcessFunc) {
        StopWatch sw = new StopWatch("checkIntent");
        sw.start("getBot");
        GetBotResponse bot = iBotApi.iGetBot(checkIntentReq.getBotId());
        sw.stop();
        // 获取租户的所有意图
        sw.start("getIntents");
        List<GetIntentResponse> intents = iIntentApiFeignClient.getIntents(checkIntentReq.getTenantId(), checkIntentReq.getProjectId(), null);
        if (checkIntentReq.getOtherIntentId() == null) {
            Optional<GetIntentResponse> otherIntentOption = intents.stream().filter(i -> i.getName().equalsIgnoreCase("other")).findFirst();
            if (!otherIntentOption.isPresent()) {
                throw new RuntimeException("otherIntentId does not appear in the parameter and does not appear in the tenant's intent configuration");
            }
            GetIntentResponse otherIntent = otherIntentOption.get();
            checkIntentReq.setOtherIntentId(otherIntent.getId());
        }
        intents = intents.stream().filter(i -> i.getStatus().equals(GenericStatusEnum.ENABLED)).peek(intent -> {
            if (CollectionUtil.isNotEmpty(intent.getChildren())) {
                List<GetIntentResponse> children = intent.getChildren().stream().filter(i -> i.getStatus().equals(GenericStatusEnum.ENABLED)).collect(Collectors.toList());
                intent.setChildren(children);
            }
        }).collect(Collectors.toList());
        List<TagDTO> tagDTOList = intents.stream().map(intent -> {
            List<TagDTO> subTags = null;
            if (CollectionUtil.isNotEmpty(intent.getChildren())) {
                subTags = intent.getChildren().stream().map(child -> new TagDTO()
                        .setTagId(child.getId())
                        .setName(child.getName())
                        .setDescription(child.getExample())
                        .setSource(child.getSource())
                        .setIsFirstLevelTag(false)
                        .setKeywordJexl(child.getKeywordJexl())).collect(Collectors.toList());
            }
            return new TagDTO()
                    .setTagId(intent.getId())
                    .setName(intent.getName())
                    .setDescription(intent.getExample())
                    .setSource(intent.getSource())
                    .setIsFirstLevelTag(true)
                    .setKeywordJexl(intent.getKeywordJexl())
                    .setSubTags(subTags);
        }).collect(Collectors.toList());
        sw.stop();
        // 获取租户最近识别为other意图的首轮会话记录
        sw.start("getIntentSamples");
        long startTime = DateUtil.date(LocalDateTime.now().minusMonths(1)).getTime();
        long endTime = DateUtil.date(LocalDateTime.now()).getTime();
        GetIntentSamplesRequest getIntentSamplesRequest = new GetIntentSamplesRequest().setTenantId(checkIntentReq.getTenantId()).setProjectId(checkIntentReq.getProjectId())
//                .setIntentId(checkIntentReq.getOtherIntentId()).setLabelled(false)
                .setCreatedAtGte(startTime)
                .setCreatedAtLte(endTime)
                .setLimit(checkIntentReq.getCheckChatCount())
                .setExcludeChatIds(checkIntentReq.getExcludeChatIds());
        List<GetIntentSampleResponse> intentSamples = iIntentApiFeignClient.getIntentSamples(getIntentSamplesRequest);
        List<IntentSampleDTO> historyChatMessages = intentSamples.stream().map(sample -> {
            List<Message> messages = sample.getMessages();
            List<Message> userQueryMsg = messages.stream().filter(m -> m.getRole().equals(RoleEnum.USER)).collect(Collectors.toList());
            List<Message> replyMsg = messages.stream().filter(m -> m.getRole().equals(RoleEnum.ASSISTANT)).collect(Collectors.toList());
            Intent firstTurnIntent = null;
            if (CollectionUtil.isNotEmpty(sample.getIntents())) {
                firstTurnIntent = sample.getIntents().get(0);
            }
            return new IntentSampleDTO()
                    .setChatId(sample.getChatId())
                    .setChatCreatedAt(sample.getChatCreatedAt())
                    .setTenantId(sample.getTenantId())
                    .setProjectId(sample.getProjectId())
                    .setFirstTurnTitle(sample.getSubject())
                    .setFirstTurnContent(userQueryMsg.stream().map(Message::getContent).collect(Collectors.joining("\n")))
                    .setFirstTurnReply(replyMsg.stream().map(Message::getContent).collect(Collectors.joining("\n")))
                    .setFirstTurnIntentId(firstTurnIntent == null ? null : firstTurnIntent.getId())
                    .setFirstTurnIntentName(firstTurnIntent == null ? null : firstTurnIntent.getName())
                    .setFirstTurnMixedQuery(sample.getMixedQuery())
                    .setFistTurnEntities(sample.getEntities());
        }).filter(Objects::nonNull).collect(Collectors.toList());
        sw.stop();
        if (CollectionUtil.isEmpty(historyChatMessages)) {
            log.info("{}", StopWatchUtil.summary(sw));
            return historyChatMessages;
        }
        // 对每一个首轮会话记录进行意图识别
        sw.start("intentParse");
        Integer parallelCount = new Integer(specialNeedConfig.getIntentCheckParallelCount());
        AtomicBoolean isInterrupt = new AtomicBoolean(false);
        ParallelUtil.parallelCall(parallelCount, true, historyChatMessages, historyChatMessage -> {
            if (isInterrupt.get()) {
                return;
            }
            boolean tryAcquire;
            try {
                tryAcquire = checkIntentSemaphore.tryAcquire(1L, TimeUnit.MINUTES);
            } catch (InterruptedException e) {
                log.warn("", e);
                tryAcquire = false;
            }
            if (!tryAcquire) {
                log.warn("checkIntentSemaphore too busy, checkIntentReq={}", checkIntentReq);
                return;
            }
            try {
                IntentParseReq intentParseReq = new IntentParseReq()
                        .setTenantId(checkIntentReq.getTenantId())
                        .setProjectId(checkIntentReq.getProjectId())
                        .setTitle(historyChatMessage.getFirstTurnTitle())
                        .setContent(historyChatMessage.getFirstTurnContent())
                        .setClientType(TenantUtil.getClientType(checkIntentReq.getTenantId()))
                        .setHistory(Collections.emptyList())
                        .setEntities(historyChatMessage.getFistTurnEntities())
                        .setExcludeSub(true)
                        .setTagList(tagDTOList)
                        .setMixedQuery(historyChatMessage.getFirstTurnMixedQuery())
                        .setOriginTitle(historyChatMessage.getFirstTurnMixedQuery().getOriginTitle())
                        .setOriginContent(historyChatMessage.getFirstTurnMixedQuery().getOriginContent());
                if (bot.getUserIntentPrecedence() != null) {
                    intentParseReq.setUserIntentPrecedence(bot.getUserIntentPrecedence());
                }
                IntentParseRes intentParseRes = intentParse(intentParseReq);
                Long firstLevelIntentId = intentParseRes.getFirstLevelIntentId();
                Optional<TagDTO> firstLevelTagOption = tagDTOList.stream().filter(t -> t.getTagId().equals(firstLevelIntentId)).findFirst();
                if (firstLevelTagOption.isPresent()) {
                    TagDTO firstLevelTag = firstLevelTagOption.get();
                    historyChatMessage.setIntentId(firstLevelTag.getTagId());
                    historyChatMessage.setIntentName(firstLevelTag.getName());
                }
                if (checkResultProcessFunc != null) {
                    synchronized (parallelCount) {
                        Boolean processSuccess = checkResultProcessFunc.apply(historyChatMessage);
                        if (!processSuccess) {
                            isInterrupt.set(true);
                            log.warn("checkIntent interrupt: checkIntentReq={}", checkIntentReq);
                        }
                    }
                }
            } catch (Exception e) {
                log.warn("checkIntent error ", e);
                if (checkResultProcessFunc != null) {
                    synchronized (parallelCount) {
                        Boolean processSuccess = checkResultProcessFunc.apply(historyChatMessage);
                        if (!processSuccess) {
                            isInterrupt.set(true);
                            log.warn("checkIntent interrupt: checkIntentReq={}", checkIntentReq);
                        }
                    }
                }
            } finally {
                if (tryAcquire) {
                    checkIntentSemaphore.release();
                }
            }
        });
        sw.stop();
        log.info("{}", StopWatchUtil.summary(sw));
        return historyChatMessages;
    }

    public void saveLabelSample(List<LabelSampleDTO> labelSampleDTOList) {
        List<LabelSampleEsEntity> labelSampleEsEntities = labelSampleDTOList.stream().map(labelSampleDTO -> {
            LabelSampleEsEntity labelSampleEsEntity = new LabelSampleEsEntity();
            BeanUtil.copyProperties(labelSampleDTO, labelSampleEsEntity);
            return labelSampleEsEntity;
        }).collect(Collectors.toList());

        // embedding
        ParallelUtil.parallelAsyncCall(5, false, labelSampleEsEntities, labelSampleEsEntity -> {
            String example = labelSampleEsEntity.getExample();
            return bedrockService.getCohereEmbeddingAsync(example)
                    .thenAccept(labelSampleEsEntity::setExampleEmbeddingCohere);
        });

        // 插入es
        Map<String, String> id2JsonData = labelSampleEsEntities.stream().collect(Collectors.toMap(LabelSampleEsEntity::generateId, JSON::toJSONString, (v1, v2) -> v2));
        esService.insert(LabelSampleEsEntity.indexName, id2JsonData);
    }


    public void deleteLabelSample(List<LabelSampleDTO> labelSampleDTOList) {
        if (CollectionUtil.isEmpty(labelSampleDTOList)) {
            return;
        }
        List<Pair<String, Object>> paramList = new ArrayList<>();
        List<Long> treeIdList = labelSampleDTOList.stream().map(LabelSampleDTO::getTreeId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        treeIdList.forEach(treeId -> paramList.add(Pair.of("treeId", treeId)));
        Set<Long> nodeIdSet = labelSampleDTOList.stream().map(LabelSampleDTO::getNodeId).filter(Objects::nonNull).collect(Collectors.toSet());
        nodeIdSet.forEach(nodeId -> paramList.add(Pair.of("nodeId", nodeId)));
        esService.deleteByQuery(LabelSampleEsEntity.indexName, paramList);
    }

    public SearchLabelSampleDTO searchLabelSample(List<LabelSampleDTO> labelSampleDTOList) {
        SearchLabelSampleDTO searchLabelSampleDTO = new SearchLabelSampleDTO();
        LLMUsages usages = new LLMUsages();
        Map<String, List<EsSearchResult<LabelSampleEsEntity>>> content2Samples = new ConcurrentHashMap<>();
        Map<String, String> content2UserEnQuery = new ConcurrentHashMap<>();

        ParallelUtil.parallelAsyncCall(3, true, labelSampleDTOList, filteredTag -> {
            String userEnQuery = filteredTag.getUserEnQuery();
            if (EmptyUtil.isEmpty(userEnQuery)) {
                CompletableFuture<SummaryRes> summaryFuture;
                SummaryReq summaryReq = new SummaryReq()
                        .setTenant_id(filteredTag.getTenantId())
                        .setChat_id(filteredTag.getChatId())
                        .setClient(filteredTag.getClientType())
                        .setMessages(filteredTag.getMessages())
                        .setProduct(filteredTag.getProduct())
                        .setTitle(filteredTag.getTitle())
                        .setModel(filteredTag.getModel())
                        .setContent(filteredTag.getContent())
                        .setMessages(new ArrayList<>())
                        .setUser_language(filteredTag.getUserLanguage().name())
                        .setKnowledge_language(filteredTag.getKnowledgeLanguage().name())
                        .setTouch_point(filteredTag.getTouchPoint());

                summaryFuture = algClient.summary(summaryReq);
                SummaryRes summaryRes = summaryFuture.join();
                usages.merge(summaryRes.getUsages());
                userEnQuery = summaryRes.getResult().getUserEnQuery();
            }
            List<BigDecimal> userEnQueryCohereEmbedding = bedrockService.getCohereEmbedding(userEnQuery);
            content2UserEnQuery.put(filteredTag.getContent(), userEnQuery);

            List<Pair<String, Object>> positiveSampleTermQuery = new ArrayList<>();
            positiveSampleTermQuery.add(Pair.of(LambdaUtil.getFieldName(LabelSampleEsEntity::getTenantId), filteredTag.getTenantId()));
            positiveSampleTermQuery.add(Pair.of(LambdaUtil.getFieldName(LabelSampleEsEntity::getProjectId), filteredTag.getProjectId()));
            positiveSampleTermQuery.add(Pair.of(LambdaUtil.getFieldName(LabelSampleEsEntity::getTreeId), filteredTag.getTreeId()));
            positiveSampleTermQuery.add(Pair.of(LambdaUtil.getFieldName(LabelSampleEsEntity::getNodeId), filteredTag.getNodeId()));
            int size = qaChatConfig.getAlgIntentSampleCount();
            return esSearchService.embeddingCosSearchAsync(LabelSampleEsEntity.indexName,
                            userEnQueryCohereEmbedding,
                            LambdaUtil.getFieldName(LabelSampleEsEntity::getExampleEmbeddingCohere),
                            size,
                            positiveSampleTermQuery,
                            EsService.getEmbeddingFieldNames(LabelSampleEsEntity.class), LabelSampleEsEntity.class)
                    .thenAccept(esSearchResults -> {
                        content2Samples.put(filteredTag.getContent(), esSearchResults);
                    });
        });
        searchLabelSampleDTO.setUsages(usages);
        searchLabelSampleDTO.setSearchResults(content2Samples);
        searchLabelSampleDTO.setUserEnQueryMap(content2UserEnQuery);
        return searchLabelSampleDTO;
    }


    public void saveIntentSample(List<IntentSampleDTO> intentSampleDTOList) {
        for (IntentSampleDTO intentSampleDTO : intentSampleDTOList) {
            if (!StringUtils.hasText(intentSampleDTO.getChatId())) {
                String userEnQuery = intentSampleDTO.getFirstTurnMixedQuery().getUserEnQuery();
                String firstTurnTitle = intentSampleDTO.getFirstTurnTitle();
                String firstTurnContent = intentSampleDTO.getFirstTurnContent();
                String titleContent = joinTitleContent(firstTurnTitle, firstTurnContent);
                intentSampleDTO.setChatId(format("%s:%s:%s:%s", intentSampleDTO.getTenantId(), intentSampleDTO.getProjectId(), intentSampleDTO.getIntentId(), DigestUtil.md5Hex(userEnQuery + titleContent)));
            }
            // 兼容以前的逻辑
            if (intentSampleDTO.getPnLabel() == null) {
                intentSampleDTO.setPnLabel(true);
            }
        }
        List<IntentSampleEsEntity> intentSampleEsEntities = intentSampleDTOList.stream().map(intentSampleDTO -> {
            IntentSampleEsEntity intentSampleEsEntity = new IntentSampleEsEntity();
            BeanUtil.copyProperties(intentSampleDTO, intentSampleEsEntity);
            return intentSampleEsEntity;
        }).collect(Collectors.toList());
        // embedding
        ParallelUtil.parallelAsyncCall(5, false, intentSampleEsEntities, intentSampleEsEntity -> {
            String userEnQuery = intentSampleEsEntity.getFirstTurnMixedQuery() == null ? null : intentSampleEsEntity.getFirstTurnMixedQuery().getUserEnQuery();
            String firstTurnTitle = intentSampleEsEntity.getFirstTurnTitle();
            String firstTurnContent = intentSampleEsEntity.getFirstTurnContent();
            String titleContent = joinTitleContent(firstTurnTitle, firstTurnContent);
            return bedrockService.getCohereEmbeddingAsync(userEnQuery)
                    .thenCompose(userEnQueryEmbedding -> {
                        intentSampleEsEntity.setSummaryUserEnQueryEmbeddingCohere(userEnQueryEmbedding);
                        return bedrockService.getCohereEmbeddingAsync(titleContent)
                                .thenAccept(intentSampleEsEntity::setTitleContentEmbeddingCohere);
                    });
        });
        // 插入es
        Map<String, String> id2JsonData = intentSampleEsEntities.stream().collect(Collectors.toMap(IntentSampleEsEntity::generateId, JSON::toJSONString, (v1, v2) -> v2));
        esService.insert(IntentSampleEsEntity.indexName, id2JsonData);
//        // 标记chat
//        Map<Long, Map<Long, List<IntentSampleDTO>>> tenantIdProjectId2Samples = new HashMap<>();
//        for (IntentSampleDTO intentSampleDTO : intentSampleDTOList) {
//            Long tenantId = intentSampleDTO.getTenantId();
//            Long projectId = intentSampleDTO.getProjectId();
//            Map<Long, List<IntentSampleDTO>> projectId2Samples = tenantIdProjectId2Samples.computeIfAbsent(tenantId, k -> new HashMap<>());
//            List<IntentSampleDTO> samples = projectId2Samples.computeIfAbsent(projectId, k -> new ArrayList<>());
//            samples.add(intentSampleDTO);
//        }
//        tenantIdProjectId2Samples.forEach((tenantId, projectId2Samples) -> {
//            projectId2Samples.forEach((projectId, samples) -> {
//                List<UpdateIntentSamplesRequest.Item> items = samples.stream().map(sample -> {
//                    UpdateIntentSamplesRequest.Item item = new UpdateIntentSamplesRequest.Item();
//                    item.setChatId(sample.getChatId());
//                    item.setLabels(Collections.singletonList(sample.getIntentId()));
//                    return item;
//                }).collect(Collectors.toList());
//                UpdateIntentSamplesRequest updateIntentSamplesRequest = new UpdateIntentSamplesRequest();
//                updateIntentSamplesRequest.setList(items);
//                try {
//                    List<GetIntentSampleResponse> getIntentSampleResponses = iIntentApiFeignClient.updateIntentSamples(tenantId, projectId, updateIntentSamplesRequest);
//                    log.info("label success, itemList={}", items);
//                } catch (Exception e) {
//                    log.error("saveIntentSample label error:", e);
//                }
//            });
//        });
    }

    public void deleteIntentSample(List<IntentSampleDTO> intentSampleDTOList) {
        if (CollectionUtil.isEmpty(intentSampleDTOList)) {
            return;
        }
        List<String> ids = intentSampleDTOList.stream().map(e -> {
            IntentSampleEsEntity intentSampleEsEntity = new IntentSampleEsEntity();
            BeanUtil.copyProperties(e, intentSampleEsEntity);
            return intentSampleEsEntity.generateId();
        }).collect(Collectors.toList());
        esService.delete(IntentSampleEsEntity.indexName, ids, true);
    }

    public void updateIntentSample(List<IntentSampleDTO> intentSampleDTOList) {
        if (CollectionUtil.isEmpty(intentSampleDTOList)) {
            return;
        }
        List<IntentSampleEsEntity> intentSampleEsEntities = intentSampleDTOList.stream().map(intentSampleDTO -> {
            IntentSampleEsEntity intentSampleEsEntity = new IntentSampleEsEntity();
            // 只允许修改样本所属的意图
            intentSampleEsEntity.setChatId(intentSampleDTO.getChatId());
            intentSampleEsEntity.setPnLabel(intentSampleDTO.getPnLabel());
            intentSampleEsEntity.setIntentId(intentSampleDTO.getIntentId());
            return intentSampleEsEntity;
        }).collect(Collectors.toList());
        Map<String, String> id2JsonData = intentSampleEsEntities.stream().collect(Collectors.toMap(IntentSampleEsEntity::generateId, JSON::toJSONString, (v1, v2) -> v2));
        esService.update(IntentSampleEsEntity.indexName, id2JsonData, true);
    }

    public List<IntentSampleDTO> getIntentSample(long tenantId, long projectId, Long intentId, List<String> chatIds, Boolean pnLabel) {
        List<Pair<String, Object>> termQueryFieldAndValue = new ArrayList<>();
        termQueryFieldAndValue.add(Pair.of(LambdaUtil.getFieldName(IntentSampleEsEntity::getTenantId), tenantId));
        termQueryFieldAndValue.add(Pair.of(LambdaUtil.getFieldName(IntentSampleEsEntity::getProjectId), projectId));
        if (intentId != null) {
            termQueryFieldAndValue.add(Pair.of(LambdaUtil.getFieldName(IntentSampleEsEntity::getIntentId), intentId));
        }
        if (pnLabel != null) {
            termQueryFieldAndValue.add(Pair.of(LambdaUtil.getFieldName(IntentSampleEsEntity::getPnLabel), pnLabel));
        }
        if (CollectionUtil.isNotEmpty(chatIds)) {
            termQueryFieldAndValue.add(Pair.of(LambdaUtil.getFieldName(IntentSampleEsEntity::getChatId), chatIds));
        }
        List<IntentSampleEsEntity> intentSampleEsEntities = esSearchService.termQueryAsync(IntentSampleEsEntity.indexName, 2000, termQueryFieldAndValue, EsService.getEmbeddingFieldNames(IntentSampleEsEntity.class), IntentSampleEsEntity.class).join()
                .stream().map(EsSearchResult::getData).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(intentSampleEsEntities)) {
            return Collections.emptyList();
        }
        List<GetIntentResponse> intents = iIntentApiFeignClient.getIntents(tenantId, projectId, null);
        Map<Long, GetIntentResponse> intentId2Intent = new HashMap<>();
        for (GetIntentResponse intent : intents) {
            intentId2Intent.put(intent.getId(), intent);
            if (CollectionUtil.isNotEmpty(intent.getChildren())) {
                for (GetIntentResponse child : intent.getChildren()) {
                    intentId2Intent.put(child.getId(), child);
                }
            }
        }
        List<IntentSampleDTO> intentSampleDTOS = intentSampleEsEntities.stream().map(intentSampleEsEntity -> {
            IntentSampleDTO intentSampleDTO = new IntentSampleDTO();
            BeanUtil.copyProperties(intentSampleEsEntity, intentSampleDTO);
            GetIntentResponse firstTurnIntent = intentSampleDTO.getFirstTurnIntentId() != null ? intentId2Intent.get(intentSampleDTO.getFirstTurnIntentId()) : null;
            GetIntentResponse intent = intentId2Intent.get(intentSampleDTO.getIntentId());
            if (firstTurnIntent != null) {
                intentSampleDTO.setFirstTurnIntentName(firstTurnIntent.getName());
            }
            if (intent != null) {
                intentSampleDTO.setIntentName(intent.getName());
            }
            return intentSampleDTO;
        }).collect(Collectors.toList());
        return intentSampleDTOS;
    }

    public static String joinTitleContent(String title, String content) {
        if (StringUtils.hasLength(title) && StringUtils.hasLength(content)) {
            return title + "\n" + content;
        } else {
            if (title == null) {
                title = "";
            }
            if (content == null) {
                content = "";
            }
            return title + content;
        }
    }

    public LabelIntentParseRes getLabelIntent(LabelIntentParseReq intentParseReq) {
        String userEnQuery = intentParseReq.getUserEnQuery();
        if (EmptyUtil.isEmpty(intentParseReq.getUserEnQuery())) {
            userEnQuery = (intentParseReq.getTitle() == null ? "" : intentParseReq.getTitle()) +
                    (intentParseReq.getContent() == null ? "" : intentParseReq.getContent());
            if (intentParseReq.getTreeId() != null && labelConfig.getSkipSummaryTreeId().contains(intentParseReq.getTreeId())) {
                log.info("skip summary tree id: " + intentParseReq.getTreeId());
            }else {
                // 总结用户问题
                CompletableFuture<SummaryRes> summaryFuture;
                SummaryReq summaryReq = new SummaryReq()
                        .setTenant_id(intentParseReq.getTenantId())
                        .setChat_id(intentParseReq.getChatId())
                        .setClient(intentParseReq.getClientType())
                        .setMessages(intentParseReq.getMessages())
                        .setProduct(intentParseReq.getProduct())
                        .setTitle(intentParseReq.getTitle())
//                    .setModel(intentParseReq.getModel())
                        .setContent(intentParseReq.getContent())
                        .setMessages(new ArrayList<>())
                        .setUser_language(intentParseReq.getUserLanguage().name())
                        .setKnowledge_language(intentParseReq.getKnowledgeLanguage().name())
                        .setTouch_point(intentParseReq.getTouchPoint());
                if (intentParseReq.getSummaryModel() != null) {
                    summaryReq.setModel(intentParseReq.getSummaryModel());
                }
                if(intentParseReq.getTreeId() != null && EmptyUtil.isNotEmpty(labelConfig.getSummaryConciseTreeId()) && labelConfig.getSummaryConciseTreeId().contains(intentParseReq.getTreeId())) {
                    summaryReq.setSummary_type("CONCISE");
                }
                summaryFuture = algClient.summary(summaryReq);
                SummaryRes summaryRes = summaryFuture.join();
                log.info("getLabelIntent summary req:{}, res:{}", JSON.toJSONString(summaryReq), JSON.toJSONString(summaryRes));
                userEnQuery = summaryRes.getResult().getUserEnQuery();
            }
        }

        List<List<String>> customIntents = intentParseReq.getCustomIntents();
        List<LabelExampleDTO> exampleDTOS = intentParseReq.getExampleDTOS();
        Map<String, List<LabelExampleDTO>> collect = exampleDTOS.stream().collect(Collectors.groupingBy(LabelExampleDTO::getCategory));
        List<IntentIdentifyReq.Example> examples = new ArrayList<>();
        for (String category : collect.keySet()) {
            List<LabelExampleDTO> labelExampleDTOS = collect.get(category);
            List<IntentIdentifyReq.Sample> sampleList = labelExampleDTOS.stream().map(p -> new IntentIdentifyReq.Sample().setScore(p.getScore()).setText(p.getText())).collect(Collectors.toList());
            IntentIdentifyReq.Example example = new IntentIdentifyReq.Example();
            example.setCategory(category);
            example.setSamples(sampleList);
            examples.add(example);
        }
        String tenantVersion = StringUtils.hasText(intentParseReq.getTenantVersion()) ? intentParseReq.getTenantVersion() : vocHttpClient.queryTenantVersion(intentParseReq.getChatId(), intentParseReq.getTenantId());
        IntentIdentifyReq intentIdentifyReq = new IntentIdentifyReq()
                .setTenant_id(intentParseReq.getTenantId())
                .setChat_id(intentParseReq.getChatId())
                .setClient(intentParseReq.getClientType())
                .setCustom_intents(customIntents)
                .setModel(intentParseReq.getModel())
                .setUser_en_query(userEnQuery)
                .setTouch_point(intentParseReq.getTouchPoint())
                .setMessages(intentParseReq.getMessages())
                .setExamples(examples)
                .setIs_tagging(intentParseReq.getIsTagging())
                .setIs_multi_intent(intentParseReq.getIsMultiIntent())
                .setTenant_version(tenantVersion);
        if(intentParseReq.getAddOtherCategory() != null) {
            intentIdentifyReq.setAdd_other_category(intentParseReq.getAddOtherCategory());
        }
        if (intentParseReq.getTagsDescription() != null) {
            intentIdentifyReq.setTags_description(intentParseReq.getTagsDescription());
        }
        IntentIdentifyRes subIntentIdentifyRes;
        if (labelConfig.getIsOld()) {
            subIntentIdentifyRes = algClient.tagIdentify(intentIdentifyReq, labelConfig.getOldTagPath()).join();
        } else {
            subIntentIdentifyRes = algClient.tagIdentify(intentIdentifyReq, labelConfig.getNewTagPath()).join();
        }
        log.info("getLabelIntent tag req:{}, res:{}", JSON.toJSONString(intentIdentifyReq), JSON.toJSONString(subIntentIdentifyRes));
        IntentIdentifyRes.Result result = subIntentIdentifyRes.getResult();
        List<String> intents = new ArrayList<>();
        if (result != null && EmptyUtil.isNotEmpty(result.getIntentList())) {
            intents.addAll(result.getIntentList());
        }
        return new LabelIntentParseRes()
                .setIntent(result.getIntent())
                .setIntents(intents)
                .setUsages(subIntentIdentifyRes.getUsages())
                .setUserEnQuery(userEnQuery);
    }

    //实时搜索ES中的意图
    @SneakyThrows
    public List<String> getKeywords(Long tenantId, Long projectId, String keyword) {
        //document + qa + product
        List<Pair<String, Object>> termQueryField = new ArrayList<>();
        Set<String> tagSet = Collections.synchronizedSet(new HashSet<>());
        termQueryField.add(Pair.of("tenantId", tenantId));
        termQueryField.add(Pair.of("projectId", projectId));
        String regex = "^" + keyword + ".*";
        Pattern pattern = Pattern.compile(regex);

        long start1 = System.currentTimeMillis();
        CompletableFuture<Void> documentFuture = esSearchService.wildQueryPrefixAsync(DocumentChunkEsEntity.indexName, 10, termQueryField, "tags", keyword, DocumentChunkEsEntity.class)
                .thenAccept(res -> {
                    res.forEach(p -> {
                        List<String> tags = p.getData().getTags();
                        for (String tag : tags) {
                            if (pattern.matcher(tag).matches()) {
                                tagSet.add(tag);
                            }
                        }
                    });
                    log.info("keyword:{}, document cost:{}", keyword, System.currentTimeMillis() - start1);
                });

        long start2 = System.currentTimeMillis();
        CompletableFuture<Void> qaFuture = esSearchService.wildQueryPrefixAsync(KuEsEntity.indexName, 10, termQueryField, "tags", keyword, KuEsEntity.class)
                .thenAccept(res -> {
                    res.forEach(p -> {
                        List<String> tags = p.getData().getTags();
                        for (String tag : tags) {
                            if (pattern.matcher(tag).matches()) {
                                tagSet.add(tag);
                            }
                        }
                    });
                    log.info("keyword:{}, qa cost:{}", keyword, System.currentTimeMillis() - start2);
                });

        long start3 = System.currentTimeMillis();
        CompletableFuture<Void> productFuture = esSearchService.wildQueryPrefixAsync(ProductEsEntity.indexName, 10, termQueryField, "selfTags", keyword, ProductEsEntity.class)
                .thenAccept(res -> {
                    res.forEach(p -> {
                        List<String> tags = p.getData().getSelfTags();
                        for (String tag : tags) {
                            if (pattern.matcher(tag).matches()) {
                                tagSet.add(tag);
                            }
                        }
                    });
                    log.info("keyword:{}, product cost:{}", keyword, System.currentTimeMillis() - start3);
                });

        List<CompletableFuture<Void>> futures = new ArrayList<>(Arrays.asList(documentFuture, qaFuture, productFuture));
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
        List<String> tagList = new ArrayList<>(tagSet);
        tagList.sort(String::compareTo);
        return tagList;
    }


    public Object esSearch(Long tenantId, Long projectId, EsSearchReq.KnowledgeTypeEnum knowledgeType, String title, String content, Integer topSize) {
        if (knowledgeType == EsSearchReq.KnowledgeTypeEnum.DOCUMENT) {
            List<Pair<String, Object>> termQueryFieldAndValue = new ArrayList<>();
            termQueryFieldAndValue.add(Pair.of(LambdaUtil.getFieldName(DocumentChunkEsEntity::getTenantId), tenantId));
            termQueryFieldAndValue.add(Pair.of(LambdaUtil.getFieldName(DocumentChunkEsEntity::getProjectId), projectId));
            termQueryFieldAndValue.add(Pair.of(LambdaUtil.getFieldName(DocumentChunkEsEntity::getEnable), true));
            List<BigDecimal> userQuestionCohereEmbedding = bedrockService.getCohereEmbedding(content);
            return esSearchService.embeddingCosSearchAsync(DocumentChunkEsEntity.indexName, userQuestionCohereEmbedding, LambdaUtil.getFieldName(DocumentChunkEsEntity::getContentEmbedding), topSize, termQueryFieldAndValue, EsService.getEmbeddingFieldNames(DocumentChunkEsEntity.class), DocumentChunkEsEntity.class).join();
        } else if (knowledgeType == EsSearchReq.KnowledgeTypeEnum.QA) {
            List<Pair<String, Object>> termQueryFieldAndValue = new ArrayList<>();
            termQueryFieldAndValue.add(Pair.of(LambdaUtil.getFieldName(KuEsEntity::getPublished), true));
            termQueryFieldAndValue.add(Pair.of(LambdaUtil.getFieldName(KuEsEntity::getStatus), KuStatusEnum.INDEXED));
            termQueryFieldAndValue.add(Pair.of(LambdaUtil.getFieldName(KuEsEntity::getTenantId), tenantId));
            termQueryFieldAndValue.add(Pair.of(LambdaUtil.getFieldName(KuEsEntity::getProjectId), projectId));
            List<String> excludeFields = EsService.getEmbeddingFieldNames(KuEsEntity.class);
            List<BigDecimal> userQuestionCohereEmbedding = bedrockService.getCohereEmbedding(title);
            return esSearchService.embeddingCosSearchAsync(KuEsEntity.indexName, userQuestionCohereEmbedding, LambdaUtil.getFieldName(KuEsEntity::getTitleEmbeddingCohere), topSize, termQueryFieldAndValue, excludeFields, KuEsEntity.class).join();
        }
        return new ArrayList<>();
    }
}
