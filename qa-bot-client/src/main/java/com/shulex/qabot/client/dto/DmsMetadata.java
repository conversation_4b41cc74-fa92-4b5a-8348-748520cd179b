package com.shulex.qabot.client.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

@Data
public class DmsMetadata {
    private String timestamp;

    @JSONField(name = "record-type")
    private String recordType;

    //操作类型：insert update delete
    private String operation;

    @JSONField(name = "partition-key-type")
    private String partitionKeyType;

    @JSONField(name = "schema-name")
    private String database;

    @JSONField(name = "table-name")
    private String table;
}
