package com.shulex.qabot.service.product;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.text.CharSequenceUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.shulex.gpt.apisdk.consts.TouchPointConst;
import com.shulex.gpt.apisdk.dto.LLMUsages;
import com.shulex.gpt.apisdk.dto.MixedQuery;
import com.shulex.qabot.alg.AlgClient;
import com.shulex.qabot.alg.dto.AiAgentReq;
import com.shulex.qabot.alg.dto.CustomizedPromptReq;
import com.shulex.qabot.alg.dto.RelevantScoreReq;
import com.shulex.qabot.alg.enmus.agent.AgentName;
import com.shulex.qabot.client.dto.AiAgentMessage;
import com.shulex.qabot.client.dto.AiAgentRes;
import com.shulex.qabot.client.dto.EsSearchResult;
import com.shulex.qabot.client.dto.esentity.ProductChunkEsEntity;
import com.shulex.qabot.client.dto.esentity.ProductEsEntity;
import com.shulex.qabot.client.dto.mysqlentity.KuProduct;
import com.shulex.qabot.client.req.GetCategoryReq;
import com.shulex.qabot.client.res.CustomizedPromptRes;
import com.shulex.qabot.client.res.GetCategoryRes;
import com.shulex.qabot.client.res.RecommendProductContext;
import com.shulex.qabot.config.GeneralConfig;
import com.shulex.qabot.config.PromptConfig;
import com.shulex.qabot.client.req.RecommendProductReq;
import com.shulex.qabot.client.res.RecommendProductRes;
import com.shulex.qabot.config.QaChatConfig;
import com.shulex.qabot.es.EsSearchService;
import com.shulex.qabot.es.EsService;
import com.shulex.qabot.es.dto.ProductEmbeddingSearchParam;
import com.shulex.qabot.es.dto.ProductSearchParam;
import com.shulex.qabot.mysql.repository.KuProductRepository;
import com.shulex.qabot.service.BedrockService;
import com.shulex.qabot.service.IntentService;
import com.shulex.qabot.service.product.aiagent.AiAgentTool;
import com.shulex.qabot.service.product.aiagent.ProductSearchTool;
import com.shulex.qabot.util.JsonUtil;
import com.shulex.qabot.util.ParallelUtil;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.action.search.SearchResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.BiFunction;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;

import static java.lang.String.format;

@Service
@Slf4j
public class ProductRecommendService {
    @Autowired
    AlgClient algClient;
    @Autowired
    EsSearchService esSearchService;
    @Autowired
    EsService esService;
    @Autowired
    BedrockService bedrockService;
    @Autowired
    KuProductRepository kuProductRepository;
    @Autowired
    ProductSearchTool productSearchTool;
    @Autowired
    PromptConfig promptConfig;
    @Autowired
    QaChatConfig qaChatConfig;
    @Autowired
    GeneralConfig generalConfig;

    public RecommendProductContext getRecommendProductContext(RecommendProductReq req) {
        if (!kuProductRepository.exist(req.getTenantId())) {
            log.info("getRecommendProductContext[{}]: tenantId[{}] has no product", req.getChatId(), req.getTenantId());
            return null;
        }
        MixedQuery mixedQuery = req.getMixedQuery();
        ProductSearchTool.ToolInput toolInput = new ProductSearchTool.ToolInput()
                .setTenantId(req.getTenantId())
                .setProjectId(req.getProjectId())
                .setSummary(req.getProductSearchParam() != null ? req.getProductSearchParam().getSummary() : null)
                .setSubqueries(req.getProductSearchParam() != null ? req.getProductSearchParam().getSubqueries() : null)
                .setProduct(req.getProductSearchParam() != null ? req.getProductSearchParam().getProduct() : null)
                .setRequirement(req.getProductSearchParam() != null ? req.getProductSearchParam().getRequirement() : null)
                .setPrice_range(req.getProductSearchParam() != null ? req.getProductSearchParam().getPrice_range() : null);
        Double minSkuPrice, maxSkuPrice;
        if (toolInput.getPrice_range() != null && toolInput.getPrice_range().size() >= 2) {
            minSkuPrice = toolInput.getPrice_range().get(0);
            maxSkuPrice = toolInput.getPrice_range().get(1);
        } else {
            maxSkuPrice = null;
            minSkuPrice = null;
        }
        Integer topN = qaChatConfig.getProductRecommendRelevanceCount();
        ProductEmbeddingSearchParam baseParam = new ProductEmbeddingSearchParam()
                .setSize(topN)
                .setTenantId(toolInput.getTenantId())
                .setProjectId(toolInput.getProjectId())
                .setMinSkuPrice(minSkuPrice)
                .setMaxSkuPrice(maxSkuPrice);
        RecommendProductContext resultContext = new RecommendProductContext();
        resultContext.setProductSearchParam(req.getProductSearchParam());
        LLMUsages llmUsages = new LLMUsages();
        resultContext.setLlmUsages(llmUsages);
        // 搜索es的方法，返回值的pair的key是embedding结果，value是bm25结果
        Function<String, Pair<List<EsSearchResult<ProductEsEntity>>, List<EsSearchResult<ProductEsEntity>>>> searchEsFunc = text -> {
            if (!StringUtils.hasText(text)) {
                return Pair.of(Collections.emptyList(), Collections.emptyList());
            }
            List<EsSearchResult<ProductEsEntity>> embeddingResults = embeddingSearchProduct(Arrays.asList(text), baseParam);
            List<EsSearchResult<ProductEsEntity>> bm25EsSearchProductList = searchProduct(new ProductSearchParam()
                    .setSize(topN)
                    .setTenantId(toolInput.getTenantId())
                    .setProjectId(toolInput.getProjectId())
                    .setMinSkuPrice(minSkuPrice)
                    .setMaxSkuPrice(maxSkuPrice)
                    .setTitleList(Arrays.asList(text))
            );
            return Pair.of(embeddingResults.subList(0, Math.min(embeddingResults.size(), topN)), bm25EsSearchProductList.subList(0, Math.min(bm25EsSearchProductList.size(), topN)));
        };
        // 检测相关性的方法
        BiFunction<String, Pair<List<EsSearchResult<ProductEsEntity>>, List<EsSearchResult<ProductEsEntity>>>, List<EsSearchResult<ProductEsEntity>>> relevanceJudgeFunc = (judgeText, esSearchProducts) -> {
            List<EsSearchResult<ProductEsEntity>> embeddingProducts = esSearchProducts.getKey();
            List<EsSearchResult<ProductEsEntity>> bm25Products = esSearchProducts.getValue();
            List<EsSearchResult<ProductEsEntity>> thresholdProducts = embeddingProducts.stream().filter(e -> e.getScore() >= qaChatConfig.getProductRecommendThresholdBy(req.getTenantId())).collect(Collectors.toList());
            List<EsSearchResult<ProductEsEntity>> needCheckRelevanceProducts = new ArrayList<>(bm25Products);
            needCheckRelevanceProducts.addAll(thresholdProducts);
            Boolean needRelevance = qaChatConfig.getProductRecommendNeedRelevanceBy(req.getTenantId());
            List<EsSearchResult<ProductEsEntity>> result = new ArrayList<>();
            if (Boolean.FALSE.equals(needRelevance)) {
                for (EsSearchResult<ProductEsEntity> product : needCheckRelevanceProducts) {
                    result.add(new EsSearchResult<ProductEsEntity>().setData(product.getData()).setScore(Double.MAX_VALUE));
                }
                return result;
            }
            ParallelUtil.parallelAsyncCall(5, false, needCheckRelevanceProducts, product -> {
                RelevantScoreReq relevantScoreReq = new RelevantScoreReq().setClient(req.getClientType())
                        .setTenant_id(req.getTenantId())
                        .setChat_id(req.getChatId())
                        .setTouch_point(req.getTouchPoint())
                        .setIntent(req.getIntent())
                        .setLanguage(req.getLanguage() != null ? req.getLanguage().name() : null)
                        .setQuestion(judgeText.toLowerCase())
                        .setContext((product.getData().getTitle() + "\n" + product.getData().getDescribe()).toLowerCase());
                return algClient.relevantScore(relevantScoreReq).thenAccept(relevantScoreRes -> {
                    LLMUsages usages = relevantScoreRes.getUsages();
                    llmUsages.merge(usages);
                    Float relevantScore = relevantScoreRes.getResult().getRelevant_score();
                    if (relevantScore == null) {
                        relevantScore = Float.MIN_VALUE;
                    }
                    result.add(new EsSearchResult<ProductEsEntity>().setData(product.getData()).setScore(Double.valueOf(relevantScore)));
                    log.info("getRecommendProductContext[{}]: relevanceJudgeFunc, relevantScore={}, judgeText={}, productId={}, productTitle={}", req.getChatId(), relevantScore, judgeText, product.getData().getId(), product.getData().getTitle());
                });
            });
            result.sort((e1, e2) -> e2.getScore().compareTo(e1.getScore()));
            return result;
        };
        // search es, embedding & bm25
        Map<String, Pair<List<EsSearchResult<ProductEsEntity>>, List<EsSearchResult<ProductEsEntity>>>> subQueryEsSearchProducts = new HashMap<>();
        if (CollectionUtil.isNotEmpty(toolInput.getSubqueries())) {
            for (String subquery : toolInput.getSubqueries()) {
                Pair<List<EsSearchResult<ProductEsEntity>>, List<EsSearchResult<ProductEsEntity>>> products = searchEsFunc.apply(subquery);
                if (CollectionUtil.isNotEmpty(products.getKey()) || CollectionUtil.isNotEmpty(products.getValue())) {
                    subQueryEsSearchProducts.put(subquery, products);
                }
            }
        }
        Map<String, Pair<List<EsSearchResult<ProductEsEntity>>, List<EsSearchResult<ProductEsEntity>>>> knowledgeEnQueryEsSearchProducts = new HashMap<>();
        if (req.getProductSearchParam() == null) {
            Pair<List<EsSearchResult<ProductEsEntity>>, List<EsSearchResult<ProductEsEntity>>> products = searchEsFunc.apply(mixedQuery.getKnowledgeEnQuery());
            if (CollectionUtil.isNotEmpty(products.getKey()) || CollectionUtil.isNotEmpty(products.getValue())) {
                knowledgeEnQueryEsSearchProducts.put(mixedQuery.getKnowledgeEnQuery(), products);
            }
        }
        resultContext.addEsSearchProducts(subQueryEsSearchProducts).addEsSearchProducts(knowledgeEnQueryEsSearchProducts);
        if (CollectionUtil.isEmpty(subQueryEsSearchProducts) && CollectionUtil.isEmpty(knowledgeEnQueryEsSearchProducts)) {
            // no es result
            log.info("getRecommendProductContext[{}]: tenantId[{}] search no products", req.getChatId(), req.getTenantId());
            return resultContext;
        }
        // 相关性判断
        Map<String, List<EsSearchResult<ProductEsEntity>>> relevanceJudgeProductsMap = new HashMap<>();
        subQueryEsSearchProducts.forEach((subQuery, products) -> relevanceJudgeProductsMap.put(subQuery, relevanceJudgeFunc.apply(subQuery, products)));
        knowledgeEnQueryEsSearchProducts.forEach((knowledgeEnQuery, products) -> relevanceJudgeProductsMap.put(knowledgeEnQuery, relevanceJudgeFunc.apply(knowledgeEnQuery, products)));
        resultContext.setRelevanceJudgeProducts(relevanceJudgeProductsMap);
        List<EsSearchResult<ProductEsEntity>> relevanceJudgeProducts = new ArrayList<>();
        relevanceJudgeProductsMap.forEach((query, products) -> relevanceJudgeProducts.addAll(products));
        relevanceJudgeProducts.sort((e1, e2) -> e2.getScore().compareTo(e1.getScore()));
        // 拼接prompt
        List<ProductEsEntity> usedProducts = relevanceJudgeProducts.stream()
                .filter(e -> e.getScore() >= qaChatConfig.getProductRecommendRelevanceMinValue())
                .collect(Collectors.toMap(e -> e.getData().getId(), e -> e, (v1, v2) -> v1.getScore() > v2.getScore() ? v1 : v2)).values().stream()
                .sorted((e1, e2) -> e2.getScore().compareTo(e1.getScore()))
                .map(EsSearchResult::getData)
                .collect(Collectors.toList());
        usedProducts = usedProducts.subList(0, Math.min(usedProducts.size(), 10));
        resultContext.setUsedProducts(usedProducts);
        if (CollectionUtil.isEmpty(usedProducts)) {
            log.info("getRecommendProductContext[{}]: tenantId[{}] no products exceeds relevance", req.getChatId(), req.getTenantId());
            return resultContext;
        }
        String contextContent = usedProducts.stream().map(product -> {
            StringBuilder format = new StringBuilder();
            format.append("<spu>\n## ").append(product.getTitle()).append("\n\n");
            if (CollectionUtil.isNotEmpty(product.getImages())) {
                format.append("### Product Image\n").append(product.getImages().get(0)).append("\n\n");
            }
            Double price = null;
            if (CollectionUtil.isNotEmpty(product.getSkuList())) {
                for (ProductEsEntity.Sku sku : product.getSkuList()) {
                    if (sku.getPrice() != null && sku.getPrice().doubleValue() > 0) {
                        if (price == null || price > sku.getPrice().doubleValue()) {
                            price = sku.getPrice().doubleValue();
                        }
                    }
                }
            }
            if (price != null) {
                format.append("### SKU Price($)\n").append(price).append("\n\n");
            }
            format.append("### SPU Description\n").append(product.getDescribe()).append("\n</spu>");
            return format.toString();
        }).collect(Collectors.joining("\n\n"));
        String systemContent = promptConfig.getSystemContent(req.getTenantId(), req.getAiReplyPromptInfo(), contextContent, req.getLanguage().name(), req.getWordsLimit(), req.getModelType());
        resultContext.setContextContents(contextContent).setSystemContent(systemContent);
        return resultContext;
    }

    private List<EsSearchResult<ProductEsEntity>> embeddingSearchProduct(List<String> embeddingTexts, ProductEmbeddingSearchParam baseParam) {
        List<EsSearchResult<ProductEsEntity>> productEsEntities = new ArrayList<>();
        if (embeddingTexts != null) {
            for (String embeddingText : embeddingTexts) {
                if (StringUtils.hasText(embeddingText)) {
                    List<BigDecimal> embedding = bedrockService.getCohereEmbeddingAsync(embeddingText).join();
                    ProductEmbeddingSearchParam titleEmbeddingParam = JSON.parseObject(JSON.toJSONString(baseParam), ProductEmbeddingSearchParam.class).setTitleEmbedding(embedding);
                    CompletableFuture<List<EsSearchResult<ProductEsEntity>>> titleFuture = esSearchService.productEmbeddingSearch(titleEmbeddingParam, ProductEsEntity.class);
                    ProductEmbeddingSearchParam describeEmbeddingParam = JSON.parseObject(JSON.toJSONString(baseParam), ProductEmbeddingSearchParam.class).setSize(baseParam.getSize() * 2).setIndex(ProductChunkEsEntity.indexName).setChunkContentEmbedding(embedding);
                    CompletableFuture<List<EsSearchResult<ProductChunkEsEntity>>> describeChunkFuture = esSearchService.productEmbeddingSearch(describeEmbeddingParam, ProductChunkEsEntity.class);
                    // title results
                    List<EsSearchResult<ProductEsEntity>> titlesResults = titleFuture.join();
                    productEsEntities.addAll(titlesResults);
                    // describe results
                    List<EsSearchResult<ProductChunkEsEntity>> describeChunkResults = describeChunkFuture.join();
                    Map<String, List<EsSearchResult<ProductChunkEsEntity>>> kuProductId2Chunks = describeChunkResults.stream().collect(Collectors.groupingBy(e -> e.getData().getKuProductId().toString()));
                    Map<String, ProductEsEntity> kuProductIdMap = esService.getByIds(ProductEsEntity.indexName, new ArrayList<>(kuProductId2Chunks.keySet()), ProductEsEntity.class, EsService.getEmbeddingFieldNames(ProductEsEntity.class));
                    List<EsSearchResult<ProductEsEntity>> describeResults = new ArrayList<>();
                    for (String kuProductId : kuProductId2Chunks.keySet()) {
                        ProductEsEntity productEsEntity = kuProductIdMap.get(kuProductId);
                        List<EsSearchResult<ProductChunkEsEntity>> chunks = kuProductId2Chunks.get(kuProductId);
                        // find max score and replace describe field
                        chunks.sort(Comparator.comparing(e -> e.getData().getChunkSeq()));
                        Double maxScore = Double.MIN_VALUE;
                        StringBuilder newDescription = new StringBuilder();
                        for (EsSearchResult<ProductChunkEsEntity> chunk : chunks) {
                            if (chunk.getScore() > maxScore) {
                                maxScore = chunk.getScore();
                            }
                            newDescription.append(chunk.getData().getContent()).append("\n");
                        }
                        productEsEntity.setDescribe(newDescription.toString());
                        describeResults.add(new EsSearchResult<ProductEsEntity>().setScore(maxScore).setData(productEsEntity));
                    }
                    productEsEntities.addAll(describeResults);
                }
            }
        }
        Map<String, Double> id2Score = new HashMap<>();
        Map<String, ProductEsEntity> id2Entity = new HashMap<>();
        for (EsSearchResult<ProductEsEntity> result : productEsEntities) {
            String id = result.getData().getId();
            if (id2Score.containsKey(id)) {
                Double score = id2Score.get(id);
                if (result.getScore() > score) {
                    id2Score.put(id, result.getScore());
                    id2Entity.put(id, result.getData());
                }
            } else {
                id2Score.put(id, result.getScore());
                id2Entity.put(id, result.getData());
            }
        }
        List<EsSearchResult<ProductEsEntity>> results = new ArrayList<>();
        id2Score.forEach((id, score) -> {
            results.add(new EsSearchResult<ProductEsEntity>().setData(id2Entity.get(id)).setScore(score));
        });
        results.sort((e1, e2) -> e2.getScore().compareTo(e1.getScore()));
        return results;
    }

    private List<EsSearchResult<ProductEsEntity>> searchProduct(ProductSearchParam param) {
        SearchResponse searchResponse = esSearchService.productSearch(param).join();
        return esSearchService.searchResponseToEsResults(searchResponse, ProductEsEntity.class);
    }

    public RecommendProductRes recommendProduct(RecommendProductReq req) {
        // agent message
        List<AiAgentMessage> currentChatAllMessages = new ArrayList<>();
        List<AiAgentMessage> currentTurnMessages = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(req.getHistoryAiAgentMessages())) {
            currentChatAllMessages.addAll(req.getHistoryAiAgentMessages());
        }
        String userOriginQuestion = IntentService.joinTitleContent(req.getMixedQuery().getOriginTitle(), req.getMixedQuery().getOriginContent());
        AiAgentMessage userMessage = new AiAgentMessage()
                .setRole(AiAgentMessage.Role.USER)
                .setContent(userOriginQuestion)
                .setTurn(req.getTurn());
        currentChatAllMessages.add(userMessage);
        currentTurnMessages.add(userMessage);
        // maybe multi-call agent
        LLMUsages totalUsages = new LLMUsages();
        int maxLoopCount = 10;
        int currentLoopCount = 0;
        while (true) {
            currentLoopCount++;
            if (currentLoopCount > maxLoopCount) {
                AiAgentRes aiAgentRes = new AiAgentRes()
                        .setUsages(totalUsages)
                        .setResult(new AiAgentRes.Result()
                                .setStatus(AiAgentRes.Result.Status.DENIED)
                                .setReason(AiAgentRes.Result.Reason.AGENT_INVOKE_EXCEED_MAX_LOOP_COUNT)
                                .setAgentFinish(true));
                return new RecommendProductRes()
                        .setResult(aiAgentRes)
                        .setResponse(aiAgentRes.getResult().getContent())
                        .setAiAgentMessages(currentTurnMessages);
            }
            AiAgentReq aiAgentReq = new AiAgentReq()
                    .setClient(req.getClientType())
                    .setTenant_id(req.getTenantId())
                    .setChat_id(req.getChatId())
                    .setTouch_point(req.getTouchPoint())
                    .setContext(new AiAgentReq.Context()
                            .setAgent_name(AgentName.PRESALE_AGENT)
                            .setLanguage(req.getLanguage())
                            .setMessages(currentChatAllMessages));
            AiAgentRes aiAgentRes = algClient.aiAgent(aiAgentReq).join();
            totalUsages.merge(aiAgentRes.getUsages());
            aiAgentRes.setUsages(totalUsages);
            AiAgentMessage.Action action = aiAgentRes.getResult().getAction();
            AiAgentMessage agentMsg = new AiAgentMessage()
                    .setRole(AiAgentMessage.Role.AGENT)
                    .setContent(aiAgentRes.getResult().getContent())
                    .setAction(action)
                    .setTurn(req.getTurn());
            currentTurnMessages.add(agentMsg);
            currentChatAllMessages.add(agentMsg);
            if (!aiAgentRes.getResult().getStatus().equals(AiAgentRes.Result.Status.SUCCESS)) {
                return new RecommendProductRes()
                        .setResult(aiAgentRes)
                        .setResponse(aiAgentRes.getResult().getContent())
                        .setAiAgentMessages(currentTurnMessages);
            }
            String observationProductListKey = "productList";
            switch (action.getAction_type()) {
                case ASK_USER:
                    AiAgentMessage assistantMsg = new AiAgentMessage()
                            .setRole(AiAgentMessage.Role.ASSISTANT)
                            .setContent(aiAgentRes.getResult().getContent())
                            .setTurn(req.getTurn());
                    currentTurnMessages.add(assistantMsg);
                    currentChatAllMessages.add(assistantMsg);
                    String res = aiAgentRes.getResult().getContent();
                    // 根据用户的问题搜索5个商品
                    List<BigDecimal> userQueryEmbedding = bedrockService.getCohereEmbedding(req.getMixedQuery().getUserQuery());
                    ProductEmbeddingSearchParam param = new ProductEmbeddingSearchParam().setSize(5).setTenantId(req.getTenantId()).setProjectId(req.getProjectId()).setProductDetailEmbedding(userQueryEmbedding);
                    List<EsSearchResult<ProductEsEntity>> esResults = esSearchService.productEmbeddingSearch(param, ProductEsEntity.class).join();
                    if (CollectionUtil.isNotEmpty(esResults)) {
                        List<ProductEsEntity> productEsEntities = esResults.stream().map(EsSearchResult::getData).collect(Collectors.toList());
                        productEsEntities = rankProductAfterAnswer(productEsEntities, aiAgentRes.getResult().getContent());
                        String productListString = format("[PRODUCT_LIST:%s]", productEsEntities.stream().map(ProductEsEntity::getProductId).collect(Collectors.joining(",")));
                        res += productListString;
                    }
                    return new RecommendProductRes()
                            .setResult(aiAgentRes)
                            .setResponse(res)
                            .setAiAgentMessages(currentTurnMessages);
                case FINAL_ANSWER:
                    AiAgentMessage msg = new AiAgentMessage()
                            .setRole(AiAgentMessage.Role.ASSISTANT)
                            .setContent(aiAgentRes.getResult().getContent())
                            .setTurn(req.getTurn());
                    currentTurnMessages.add(msg);
                    currentChatAllMessages.add(msg);
                    // 按照前端需要的格式组装 response
                    // 获取最后一次tool调用的商品列表
                    List<ProductEsEntity> products = null;
                    for (AiAgentMessage message : currentTurnMessages) {
                        if (message.getAction() != null && AiAgentMessage.ActionType.CALL_TOOL.equals(message.getAction().getAction_type())) {
                            JSONObject observation = message.getObservation();
                            if (observation != null) {
                                Object productJsonArray = observation.get(observationProductListKey);
                                if (JsonUtil.isJsonArray(JSON.toJSONString(productJsonArray))) {
                                    products = JSON.parseArray(JSON.toJSONString(productJsonArray)).toJavaList(ProductEsEntity.class);
                                }
                            }
                        }
                    }
                    String response = aiAgentRes.getResult().getContent();
                    if (CollectionUtil.isNotEmpty(products)) {
                        products = rankProductAfterAnswer(products, aiAgentRes.getResult().getContent());
                        String productListString = format("[PRODUCT_LIST:%s]", products.stream().map(ProductEsEntity::getProductId).collect(Collectors.joining(",")));
                        response += productListString;
                    }
                    return new RecommendProductRes()
                            .setResult(aiAgentRes)
                            .setResponse(response)
                            .setAiAgentMessages(currentTurnMessages);
                case UNKNOWN:
                    return new RecommendProductRes()
                            .setResult(aiAgentRes)
                            .setResponse(aiAgentRes.getResult().getContent())
                            .setAiAgentMessages(currentTurnMessages);
                case CALL_TOOL:
                    List<ProductEsEntity> productList = null;
                    String actionTool = action.getTool();
                    AiAgentTool tool = AiAgentTool.getToolByName(actionTool);
                    if (tool != null) {
                        JSONObject toolInput = action.getTool_input();
                        JSONObject input = new JSONObject(toolInput);
                        input.put("tenantId", req.getTenantId());
                        input.put("projectId", req.getProjectId());
                        productList = tool.findProduct(input);
                    }
                    JSONObject callToolRes = null;
                    if (CollectionUtil.isNotEmpty(productList)) {
                        callToolRes = new JSONObject();
                        List<ProductEsEntity> cleanProducts = productList.stream().map(p -> {
                            ProductEsEntity cleanProduct = JSON.parseObject(JSON.toJSONString(p), ProductEsEntity.class);
                            cleanProduct.setImages(null);
                            return cleanProduct;
                        }).collect(Collectors.toList());
                        callToolRes.put(observationProductListKey, cleanProducts);
                    }
                    agentMsg.setObservation(callToolRes);
                    break;
            }
        }
    }

    public List<ProductEsEntity> rankProductAfterAnswer(List<ProductEsEntity> products, String aiAnswer) {
        if (CollectionUtil.isEmpty(products) || StringUtils.isEmpty(aiAnswer)) {
            return products;
        }
        List<ProductEsEntity> result = new ArrayList<>();
        List<ProductEsEntity> needEmbeddingProducts = new ArrayList<>();
        for (ProductEsEntity product : products) {
            boolean resultAdd = false;
            List<ProductEsEntity.Sku> skuList = product.getSkuList();
            if (CollectionUtil.isNotEmpty(skuList)) {
                for (ProductEsEntity.Sku sku : skuList) {
                    if (sku.getId() != null && aiAnswer.toLowerCase().contains(sku.getId().toLowerCase())) {
                        result.add(product);
                        resultAdd = true;
                    }
                }
            }
            if (!resultAdd) {
                needEmbeddingProducts.add(product);
            }
        }
        if (CollectionUtil.isEmpty(needEmbeddingProducts)) {
            return result;
        }
        List<String> titleList = needEmbeddingProducts.stream().map(p -> p.getTitle() == null ? "" : p.getTitle()).collect(Collectors.toList());
        titleList.add(aiAnswer);
        List<BedrockService.CohereEmbeddingResult> embeddingResults = bedrockService.getCohereEmbeddingsAsync(titleList).join();
        List<BigDecimal> aiAnswerEmbedding = embeddingResults.get(embeddingResults.size() - 1).getEmbedding();
        List<Pair<Double, ProductEsEntity>> scoreWithProducts = new ArrayList<>();
        for (int i = 0; i < embeddingResults.size() - 1; i++) {
            List<BigDecimal> titleEmbedding = embeddingResults.get(i).getEmbedding();
            Double score = BedrockService.cos(aiAnswerEmbedding, titleEmbedding);
            scoreWithProducts.add(Pair.of(score, needEmbeddingProducts.get(i)));
        }
        scoreWithProducts.sort((e1, e2) -> e2.getKey().compareTo(e1.getKey()));
        for (Pair<Double, ProductEsEntity> scoreWithProduct : scoreWithProducts) {
            result.add(scoreWithProduct.getValue());
        }
        return result;
    }

    @SneakyThrows
    public GetCategoryRes customizedPrompt(Long tenantId, GetCategoryReq req){
        Long docId = req.getDocId();
        GetCategoryRes res = new GetCategoryRes();
        Long productNum = kuProductRepository.lambdaQuery()
                .eq(KuProduct::getTenantId, tenantId)
                .eq(KuProduct::getDocumentId, docId)
                .count();
        res.setProductNum(productNum);

        List<KuProduct> productList = kuProductRepository.lambdaQuery()
                .eq(KuProduct::getTenantId, tenantId)
                .eq(KuProduct::getDocumentId, docId)
                .isNotNull(KuProduct::getTitle)
                .last("limit 2")
                .list();

        if (CollectionUtil.isNotEmpty(productList)){
            List<String> titleList = productList.stream().map(KuProduct::getTitle).collect(Collectors.toList());
            String userContent = CharSequenceUtil.format(generalConfig.getProductPrompt(), req.getLang(), String.join(",", titleList));
            CustomizedPromptReq customizedPromptReq = new CustomizedPromptReq();
            customizedPromptReq.setUser_prompt(userContent);
            CustomizedPromptRes customizedPromptRes = algClient.customizedPrompt(customizedPromptReq).get();
            String result = customizedPromptRes.getResult();
            res.setPrompt(result);
        }
        return res;
    }
}
