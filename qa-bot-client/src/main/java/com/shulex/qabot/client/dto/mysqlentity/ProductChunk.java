package com.shulex.qabot.client.dto.mysqlentity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.shulex.qabot.client.dto.typehandler.ListIntegerTypeHandler;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;

@Data
@Accessors(chain = true)
@TableName(value = "product_chunk", autoResultMap = true)
public class ProductChunk {
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    private Boolean enable;
    private Boolean indexed;
    private Long tenantId;
    private Long projectId;
    private Long productDocId;
    private Long kuProductId;
    private Integer chunkSeq;
    private String content;
    @TableField(typeHandler = ListIntegerTypeHandler.class)
    private List<Integer> offset;
    private String chunkField;
    private Boolean isVisible;
}
