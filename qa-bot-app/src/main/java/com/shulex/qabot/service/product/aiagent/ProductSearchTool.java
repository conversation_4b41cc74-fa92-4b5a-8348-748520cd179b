package com.shulex.qabot.service.product.aiagent;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Pair;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson2.JSON;

import com.shulex.qabot.client.dto.EsSearchResult;
import com.shulex.qabot.es.EsService;
import com.shulex.qabot.client.dto.esentity.ProductChunkEsEntity;
import com.shulex.qabot.client.dto.esentity.ProductEsEntity;

import com.shulex.qabot.es.EsSearchService;
import com.shulex.qabot.es.dto.ProductEmbeddingSearchParam;
import com.shulex.qabot.es.dto.ProductSearchParam;
import com.shulex.qabot.config.QaChatConfig;
import com.shulex.qabot.service.BedrockService;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@Service
public class ProductSearchTool extends AiAgentTool {
    @Autowired
    EsSearchService esSearchService;
    @Autowired
    EsService esService;
    @Autowired
    BedrockService bedrockService;
    @Autowired
    QaChatConfig qaChatConfig;

    public ProductSearchTool() {
        super("product_search");
    }

    @Override
    public List<ProductEsEntity> findProduct(JSONObject toolInput) {
        ToolInput input = toolInput.toJavaObject(ToolInput.class);
        Double minSkuPrice = null, maxSkuPrice = null;
        if (input.getPrice_range() != null && input.getPrice_range().size() >= 2) {
            minSkuPrice = input.getPrice_range().get(0);
            maxSkuPrice = input.getPrice_range().get(1);
        }
        String productString = "";
        if (CollectionUtil.isNotEmpty(input.getProduct())) {
            productString = String.join(" ", input.getProduct());
        }
        String requirementString = "";
        if (CollectionUtil.isNotEmpty(input.getRequirement())) {
            requirementString = String.join(" ", input.getRequirement());
        }
        String summary = input.getSummary() == null ? "" : input.getSummary();
        ProductEmbeddingSearchParam baseParam = new ProductEmbeddingSearchParam()
                .setSize(5)
                .setTenantId(input.tenantId)
                .setProjectId(input.projectId)
                .setMinSkuPrice(minSkuPrice)
                .setMaxSkuPrice(maxSkuPrice);
        // 向量搜索，候选匹配文本
        LinkedHashSet<String> needEmbeddingTexts = new LinkedHashSet<>();
        for (String text : Arrays.asList(
                summary + " " + productString + " " + requirementString,
                summary + " " + productString,
                summary + " " + requirementString,
                productString + " " + requirementString,
                summary,
                productString,
                requirementString
        )) {
            if (StringUtils.hasText(text)) {
                needEmbeddingTexts.add(text);
            }
        }
        for (String needEmbeddingText : needEmbeddingTexts) {
            if (StringUtils.hasLength(needEmbeddingText)) {
                List<ProductEsEntity> productEsEntities = embeddingSearch(Arrays.asList(needEmbeddingText), baseParam);
                if (CollectionUtil.isNotEmpty(productEsEntities)) {
                    return productEsEntities;
                }
            }
        }
        // 如果向量匹配全部被过滤掉，则去除向量匹配，只进行价格搜索
        if (minSkuPrice != null || maxSkuPrice != null) {
            List<ProductEsEntity> productEsEntities = esSearchService.productSearch(new ProductSearchParam()
                            .setSize(5)
                            .setTenantId(input.tenantId)
                            .setProjectId(input.projectId)
                            .setMinSkuPrice(minSkuPrice)
                            .setMaxSkuPrice(maxSkuPrice)
                    ).thenApply(r -> esSearchService.searchResponseToEsResults(r, ProductEsEntity.class)).join()
                    .stream().map(EsSearchResult::getData).collect(Collectors.toList());
            return productEsEntities;
        }
        return new ArrayList<>();
    }

    public List<ProductEsEntity> findProduct(ToolInput input) {
        Double minSkuPrice = null, maxSkuPrice = null;
        if (input.getPrice_range() != null && input.getPrice_range().size() >= 2) {
            minSkuPrice = input.getPrice_range().get(0);
            maxSkuPrice = input.getPrice_range().get(1);
        }
        String productString = "";
        if (CollectionUtil.isNotEmpty(input.getProduct())) {
            productString = String.join(" ", input.getProduct());
        }
        String requirementString = "";
        if (CollectionUtil.isNotEmpty(input.getRequirement())) {
            requirementString = String.join(" ", input.getRequirement());
        }
        String summary = input.getSummary() == null ? "" : input.getSummary();
        ProductEmbeddingSearchParam baseParam = new ProductEmbeddingSearchParam()
                .setSize(5)
                .setTenantId(input.tenantId)
                .setProjectId(input.projectId)
                .setMinSkuPrice(minSkuPrice)
                .setMaxSkuPrice(maxSkuPrice);
        // 向量搜索，候选匹配文本
        for (List<String> textList : Arrays.asList(
                input.getSubqueries(),
                Arrays.asList(summary, productString, requirementString)
        )) {
            List<ProductEsEntity> productEsEntities = embeddingSearch(textList, baseParam);
            if (CollectionUtil.isNotEmpty(productEsEntities)) {
                return productEsEntities;
            }
        }
        // 如果向量匹配全部被过滤掉，则去除向量匹配，只进行价格搜索
        if (minSkuPrice != null || maxSkuPrice != null) {
            List<ProductEsEntity> productEsEntities = esSearchService.productSearch(new ProductSearchParam()
                            .setSize(5)
                            .setTenantId(input.tenantId)
                            .setProjectId(input.projectId)
                            .setMinSkuPrice(minSkuPrice)
                            .setMaxSkuPrice(maxSkuPrice)
                    ).thenApply(r -> esSearchService.searchResponseToEsResults(r, ProductEsEntity.class)).join()
                    .stream().map(EsSearchResult::getData).collect(Collectors.toList());
            return productEsEntities;
        }
        return new ArrayList<>();
    }

    private List<ProductEsEntity> embeddingSearch(List<String> embeddingTexts, ProductEmbeddingSearchParam baseParam) {
        List<EsSearchResult<ProductEsEntity>> productEsEntities = new ArrayList<>();
        if (embeddingTexts != null) {
            for (String embeddingText : embeddingTexts) {
                if (StringUtils.hasText(embeddingText)) {
                    List<BigDecimal> embedding = bedrockService.getCohereEmbeddingAsync(embeddingText).join();
                    ProductEmbeddingSearchParam titleEmbeddingParam = JSON.parseObject(JSON.toJSONString(baseParam), ProductEmbeddingSearchParam.class).setTitleEmbedding(embedding);
                    CompletableFuture<List<EsSearchResult<ProductEsEntity>>> titleFuture = esSearchService.productEmbeddingSearch(titleEmbeddingParam, ProductEsEntity.class);
                    ProductEmbeddingSearchParam describeEmbeddingParam = JSON.parseObject(JSON.toJSONString(baseParam), ProductEmbeddingSearchParam.class).setSize(10).setIndex(ProductChunkEsEntity.indexName).setChunkContentEmbedding(embedding);
                    CompletableFuture<List<EsSearchResult<ProductChunkEsEntity>>> describeChunkFuture = esSearchService.productEmbeddingSearch(describeEmbeddingParam, ProductChunkEsEntity.class);
                    // title results
                    List<EsSearchResult<ProductEsEntity>> titlesResults = titleFuture.join();
                    productEsEntities.addAll(titlesResults);
                    // describe results
                    List<EsSearchResult<ProductChunkEsEntity>> describeChunkResults = describeChunkFuture.join();
                    Map<String, List<EsSearchResult<ProductChunkEsEntity>>> kuProductId2Chunks = describeChunkResults.stream().collect(Collectors.groupingBy(e -> e.getData().getKuProductId().toString()));
                    Map<String, ProductEsEntity> kuProductIdMap = esService.getByIds(ProductEsEntity.indexName, new ArrayList<>(kuProductId2Chunks.keySet()), ProductEsEntity.class, EsService.getEmbeddingFieldNames(ProductEsEntity.class));
                    List<EsSearchResult<ProductEsEntity>> describeResults = new ArrayList<>();
                    for (String kuProductId : kuProductId2Chunks.keySet()) {
                        ProductEsEntity productEsEntity = kuProductIdMap.get(kuProductId);
                        List<EsSearchResult<ProductChunkEsEntity>> chunks = kuProductId2Chunks.get(kuProductId);
                        // find max score and replace describe field
                        chunks.sort(Comparator.comparing(e -> e.getData().getChunkSeq()));
                        Double maxScore = Double.MIN_VALUE;
                        StringBuilder newDescription = new StringBuilder();
                        for (EsSearchResult<ProductChunkEsEntity> chunk : chunks) {
                            if (chunk.getScore() > maxScore) {
                                maxScore = chunk.getScore();
                            }
                            newDescription.append(chunk.getData().getContent()).append("\n");
                        }
                        productEsEntity.setDescribe(newDescription.toString());
                        describeResults.add(new EsSearchResult<ProductEsEntity>().setScore(maxScore).setData(productEsEntity));
                    }
                    productEsEntities.addAll(describeResults);
                }
            }
        }
        Map<String, Double> id2Score = new HashMap<>();
        Map<String, ProductEsEntity> id2Entity = new HashMap<>();
        for (EsSearchResult<ProductEsEntity> result : productEsEntities) {
            String id = result.getData().getId();
            if (id2Score.containsKey(id)) {
                Double score = id2Score.get(id);
                if (result.getScore() > score) {
                    id2Score.put(id, result.getScore());
                    id2Entity.put(id, result.getData());
                }
            } else {
                id2Score.put(id, result.getScore());
                id2Entity.put(id, result.getData());
            }
        }
        List<Pair<Double, ProductEsEntity>> entities = new ArrayList<>();
        id2Score.forEach((id, score) -> {
            if (score >= qaChatConfig.getProductRecommendThresholdBy(baseParam.getTenantId())) {
                entities.add(Pair.of(score, id2Entity.get(id)));
            }
        });
        entities.sort((e1, e2) -> e2.getKey().compareTo(e1.getKey()));
        return entities.stream().map(Pair::getValue).collect(Collectors.toList());
    }

    @Data
    @Accessors(chain = true)
    public static class ToolInput {
        long tenantId;
        long projectId;
        String summary;
        List<String> subqueries;
        List<String> product;
        List<String> requirement;
        List<Double> price_range;
    }
}
