package com.shulex.qabot.client.res;

import cn.hutool.core.lang.Pair;
import cn.hutool.core.util.NumberUtil;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Data
@Accessors(chain = true)
public class TagQueryRes {
    private String tag;
    private Long tagId;
    private Long treeId;
    private String treeName;
    private Integer level;
    private Long count = 0L;
    private Double rate;
    private Double aiReplyRate;
    private Set<String> chatIds = new HashSet<>();
    private List<Long> tagIds = new ArrayList<>();
    private List<String> tagNames = new ArrayList<>();
    private List<Long> multiTagIds = new ArrayList<>();
    private List<String> multiTagNames = new ArrayList<>();
    private List<TagQueryRes> children = new ArrayList<>();


    // 用于递归构建树时添加子节点
    public void addChild(TagQueryRes child) {
        this.children.add(child);
    }

    // 递归计算 count 总和
//    public Long computeTotalCount(Long sum) {
//        Long totalCount = this.count;
//        for (TagQueryRes child : children) {
//            totalCount += child.computeTotalCount(sum);
//        }
//        this.count = totalCount;
//        this.rate = NumberUtil.div(Double.valueOf(totalCount), Double.valueOf(sum), 2);
//        return totalCount;
//    }

    public Pair<Long, Set<String>>  computeTotalCount(Long sum) {
        Long totalCount = this.count;
        Set<String> chatIdSet = new HashSet<>(this.chatIds);
        for (TagQueryRes child : children) {
            Pair<Long, Set<String>> longSetPair = child.computeTotalCount(sum);
            totalCount += longSetPair.getKey();
            chatIdSet.addAll(longSetPair.getValue());
        }
        this.count = totalCount;
        this.chatIds = chatIdSet;
        this.rate = sum == 0 ? 0 : NumberUtil.div(Double.valueOf(totalCount), Double.valueOf(sum), 2);
        return Pair.of(totalCount, chatIdSet);
    }
}
