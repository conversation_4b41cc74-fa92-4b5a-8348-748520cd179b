package com.shulex.qabot.client.res;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Pair;
import com.shulex.gpt.apisdk.dto.LLMUsages;
import com.shulex.qabot.client.dto.EsSearchResult;
import com.shulex.qabot.client.dto.esentity.ProductEsEntity;
import com.shulex.qabot.client.req.RecommendProductReq;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
@Accessors(chain = true)
public class RecommendProductContext {
    RecommendProductReq.ProductSearchParam productSearchParam;
    LLMUsages llmUsages;
    Map<String, Pair<List<EsSearchResult<ProductEsEntity>>, List<EsSearchResult<ProductEsEntity>>>> esSearchProducts;
    Map<String, List<EsSearchResult<ProductEsEntity>>> relevanceJudgeProducts;
    List<ProductEsEntity> usedProducts;
    String contextContents;
    String systemContent;
    @Deprecated
    List<EsSearchResult<ProductEsEntity>> productEsResults;
    @Deprecated
    List<EsSearchResult<ProductEsEntity>> productRelevanceResults;

    public RecommendProductContext addEsSearchProducts(Map<String, Pair<List<EsSearchResult<ProductEsEntity>>, List<EsSearchResult<ProductEsEntity>>>> esSearchProducts) {
        if (CollectionUtil.isNotEmpty(esSearchProducts)) {
            if (this.esSearchProducts == null) {
                this.esSearchProducts = new HashMap<>();
            }
            this.esSearchProducts.putAll(esSearchProducts);
        }
        return this;
    }
}
