package com.shulex.qabot.client.dto;

import com.shulex.gpt.apisdk.dto.LLMUsages;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class AiAgentRes {
    Result result;
    LLMUsages usages;

    @Data
    @Accessors(chain = true)
    public static class Result {
        Status status;
        Reason reason;
        String content;
        AiAgentMessage.Action action;
        Boolean agentFinish;

        public enum Status {
            SUCCESS,
            DENIED
        }

        public enum Reason {
            AGENT_INVOKE_EXCEED_MAX_LOOP_COUNT,
            AG_SUCCESS,
            AG_TRANSFER_TO_AGENT,
            AG_LLM_CALL_ERROR,
            AG_LLM_RESULT_PARSE_ERROR,
            AG_UNKNOWN_REPLY_ERROR,
        }
    }
}
