package com.shulex.qabot.client.res;

import com.alibaba.fastjson.annotation.JSONField;
import com.shulex.gpt.apisdk.dto.LLMUsages;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
public class TagDescriptionRes {
    List<Tag> tags;
    LLMUsages usages;


    @Data
    public static class Tag{
        private String id;
        private String name;
        @JSONField(name = "auto_describe")
        private String autoDescribe;
    }
}
