package com.shulex.qabot.util;

import cn.hutool.core.collection.ListUtil;

import java.util.Arrays;
import java.util.List;
import java.util.Properties;
import java.util.stream.Collectors;

public class NLPUtil {
    private static final List<String> marks;

    static {
        marks = ListUtil.toList("!", "'", ",", "-", ".", ":", ";", "?", "\"", "`", "«", "´", "»", "–", "—", "‘", "’", "‛", "“", "”", "„", "‟", "‹", "›", "[", "]", "{", "}", "(", ")", "（", "）");
    }

    public static List<String> words(String text) {
        return Arrays.stream(text.split("[ .,，。!！?？;；：:\\-/|\\[\\]<>]+")).collect(Collectors.toList());
    }

    public static boolean isMark(String word) {
        return marks.contains(word);
    }
}
