package com.shulex.qabot.client.res;

import com.shulex.gpt.apisdk.dto.LLMUsages;
import com.shulex.gpt.apisdk.dto.RetrievedKu;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;
import java.util.Map;

@Data
@Accessors(chain = true)
public class QaChatRes {
    private Result result;
    private MetaData metaData;
    private LLMUsages usages;
    private String contextContent;
    private String gptSystemContent;

    @Data
    @Accessors(chain = true)
    public static class Result {
        private String status;
        private String explain;
        private String reason;
        private String response;
        private String responseType;
        private Boolean needImproved;
    }

    @Data
    @Accessors(chain = true)
    public static class MetaData {
        private Double retrieveMinScore;
        private Double relevantMinScore;
        private Integer relevantKusCount;
        private Double relevantAvgScore;
        private Integer usedKusCount;
        private Double usedKusRelevantAvgScore;

        private List<RetrievedKu> usedKus;
        private List<RetrievedKu> rerankKus;
        private Map<String, Object> optionsData;
        private Map<String, Object> scoreFilterData;
        private Object topkFilterData;
        private Object llmData;
        private Object usagesDetails;
        private Object rankData;
    }
}
