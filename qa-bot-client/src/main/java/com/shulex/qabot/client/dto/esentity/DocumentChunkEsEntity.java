package com.shulex.qabot.client.dto.esentity;

import cn.hutool.core.bean.BeanUtil;
import com.shulex.qabot.client.dto.mysqlentity.DocumentChunk;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

@Data
@Accessors(chain = true)
@NoArgsConstructor
public class DocumentChunkEsEntity {
    public static final String indexName = "document-chunk";

    Long id;
    LocalDateTime createdAt;
    LocalDateTime updatedAt;
    Boolean enable;
    Boolean indexed;
    Long tenantId;
    Long projectId;
    Long directoryId;
    Long documentId;
    Long resourceId;
    Integer chunkSeq;
    String content;
    List<Integer> offset;
    Set<Long> kuLabelValueIds;
    // document层面的检索信息
    String brief;
    List<String> tags;
    @EsEmbeddingField
    List<BigDecimal> contentEmbedding;
    Boolean isVisible;

    public DocumentChunkEsEntity(DocumentChunk documentChunk) {
        BeanUtil.copyProperties(documentChunk, this);
    }
}
