package com.shulex.qabot.mysql.repository;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.shulex.qabot.client.dto.mysqlentity.DocumentChunk;
import com.shulex.qabot.mysql.mapper.DocumentChunkMapper;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;

@Repository
public class DocumentChunkRepository extends ServiceImpl<DocumentChunkMapper, DocumentChunk> {
    public void indexSucceed(Collection<Long> ids) {
        if (CollectionUtil.isNotEmpty(ids)) {
            lambdaUpdate()
                    .in(DocumentChunk::getId, ids)
                    .set(DocumentChunk::getIndexed, true)
                    .update();
        }
    }

    public boolean existChunk(long tenantId) {
        return lambdaQuery().eq(DocumentChunk::getTenantId, tenantId).exists();
    }

    public List<DocumentChunk> getChunks(long documentId, SFunction<DocumentChunk, ?>... columns) {
        if (columns == null || columns.length == 0) {
            return lambdaQuery().eq(DocumentChunk::getDocumentId, documentId).list();
        } else {
            return lambdaQuery().select(columns).eq(DocumentChunk::getDocumentId, documentId).list();
        }
    }
}
