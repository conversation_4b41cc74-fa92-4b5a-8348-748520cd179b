package com.shulex.qabot.alg.dto;

import com.shulex.gpt.apisdk.dto.Message;
import com.shulex.gpt.apisdk.dto.MixedQuery;
import com.shulex.gpt.apisdk.dto.RetrievedKu;
import com.shulex.qabot.client.req.QaChatReq;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;
import java.util.Map;

@Data
@Accessors(chain = true)
public class AiReplyReq {
    String client;
    Long tenant_id;
    String touch_point;
    String bot_name;
    String chat_id;
    Context context;
    QaChatReq.AiReplyOption options;


    @Data
    @Accessors(chain = true)
    public static class Context {
        String retrieve_query;
        String intent;
        List<Message> messages;
        List<RetrievedKuData> retrieved_kus_list;
        Map<String, List<?>> entities;
        String pdt;
        MixedQuery mixedQuery;
    }

    @Data
    @Accessors(chain = true)
    public static class RetrievedKuData {
        String retrieve_type;
        List<RetrievedKu> retrieved_kus;
    }
}
