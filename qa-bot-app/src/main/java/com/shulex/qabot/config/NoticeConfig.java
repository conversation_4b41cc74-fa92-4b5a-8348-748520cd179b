package com.shulex.qabot.config;

import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.google.common.collect.Maps;
import lombok.Data;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import static java.lang.String.format;

@Data
@Component
@ConfigurationProperties(prefix = "notice")
@Slf4j
public class NoticeConfig {
    String dingGroupUrl;
    String errorDingGroupUrl;
    private final ExecutorService sendMsgThreadPool = Executors.newFixedThreadPool(3);

    public CompletableFuture<Void> sendError(String message) {
        return CompletableFuture.runAsync(() -> {
            String activeProfile = SpringUtil.getActiveProfile();
            Map<String, Object> params = Maps.newHashMap();
            params.put("msgtype", "text");
            Map<String, String> content = Maps.newHashMap();
            try {
                content.put("content", format("[qa-bot:%s:%s]Error\n%s", activeProfile, InetAddress.getLocalHost().getHostName(), message));
            } catch (UnknownHostException e) {
                content.put("content", format("[qa-bot:%s:%s]Error\n%s", activeProfile, "", message));
            }
            params.put("text", content);
            String res = HttpUtil.post(errorDingGroupUrl, JSON.toJSONString(params));
            JSONObject resJson = JSON.parseObject(res);
            Integer errcode = resJson.getInteger("errcode");
            if (errcode == null || errcode != 0) {
                log.error("DingTalkNoticeUtil.sendError: {}", res);
            }
        }, sendMsgThreadPool);
    }

    public CompletableFuture<Void> sendNotice(String message) {
        return CompletableFuture.runAsync(() -> {
            String activeProfile = SpringUtil.getActiveProfile();
            Map<String, Object> params = Maps.newHashMap();
            params.put("msgtype", "text");
            Map<String, String> content = Maps.newHashMap();
            try {
                content.put("content", format("[qa-bot:%s:%s]提醒\n%s", activeProfile, InetAddress.getLocalHost().getHostName(), message));
            } catch (UnknownHostException e) {
                content.put("content", format("[qa-bot:%s:%s]提醒\n%s", activeProfile, "", message));
            }
            params.put("text", content);
            String res = HttpUtil.post(dingGroupUrl, JSON.toJSONString(params));
            JSONObject resJson = JSON.parseObject(res);
            Integer errcode = resJson.getInteger("errcode");
            if (errcode == null || errcode != 0) {
                log.error("DingTalkNoticeUtil.sendError: {}", res);
            }
        }, sendMsgThreadPool);
    }
}
