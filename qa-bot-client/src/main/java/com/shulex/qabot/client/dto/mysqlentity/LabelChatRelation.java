package com.shulex.qabot.client.dto.mysqlentity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

@Data
@Accessors(chain = true)
@TableName("label_chat_relation")
public class LabelChatRelation {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField("tree_id")
    private Long treeId;

    @TableField("task_id")
    private Long taskId;

    @TableField("chat_id")
    private String chatId;

    @TableField("transcript_id")
    private Long transcriptId;

    @TableField("status")
    private StatusEnum status;

    @TableField("metadata")
    private String metadata;

    @TableField("tenant_id")
    private Long tenantId;

    @TableField("project_id")
    private Long projectId;

    @TableField("user_id")
    private Long userId;

    @TableField("created_at")
    private LocalDateTime createdAt;

    @TableField("updated_at")
    private LocalDateTime updatedAt;

    public enum StatusEnum{
        WAIT_TAG,
        SUCCESS
    }

    @Data
    public static class Metadata {
        private List<TagInfo> tagInfos;
    }

    @Data
    @Accessors(chain = true)
    public static class TagInfo{
        private String messageId;
        private String tagName;
        private Long tagId;
        private String treeName;
        private Boolean isAiReply = false;
        private Long treeId;
        private List<String> tagNamePath;
        private List<Long> tagIdPath;
        private List<List<String>> multiTagNamePath;
        private List<List<Long>> multiTagIdPath;
        private Boolean isMultiTag = false;
        private String title;
        private String content;
        private LocalDateTime ticketCreatedAt;
        private Map<String, Object> chatMeta;
    }
}
