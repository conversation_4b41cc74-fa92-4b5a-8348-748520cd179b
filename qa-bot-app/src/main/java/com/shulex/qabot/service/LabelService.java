package com.shulex.qabot.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.lang.func.LambdaUtil;
import cn.hutool.core.util.NumberUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.shulex.common.util.utils.EmptyUtil;
import com.shulex.qabot.alg.AlgClient;
import com.shulex.qabot.alg.dto.ReplyCompareReq;
import com.shulex.qabot.client.dto.EsSearchResult;
import com.shulex.qabot.client.dto.esentity.CategorySampleEsEntity;
import com.shulex.qabot.client.dto.esentity.DocumentChunkEsEntity;
import com.shulex.qabot.client.dto.esentity.LabelTagEsEntity;
import com.shulex.qabot.client.req.ReplyCompareParseReq;
import com.shulex.qabot.client.res.*;
import com.shulex.qabot.es.EsSearchService;
import com.shulex.qabot.es.EsService;
import com.shulex.qabot.client.req.TagQueryParam;
import com.shulex.qabot.util.FreeMakerUtil;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.search.aggregations.bucket.composite.ParsedComposite;
import org.elasticsearch.search.aggregations.bucket.filter.ParsedFilter;
import org.elasticsearch.search.aggregations.bucket.terms.ParsedStringTerms;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@Service
@Slf4j
public class LabelService {
    @Autowired
    AlgClient algClient;
    @Autowired
    EsService esService;
    @Autowired
    EsSearchService esSearchService;

    public ReplyCompareRes replyCompare(ReplyCompareParseReq req) {
        ReplyCompareReq replyCompareReq = new ReplyCompareReq();
        replyCompareReq
                .setClient(req.getClient())
                .setTenant_id(req.getTenantId())
                .setRequest_id(req.getRequestId())
                .setModel_id(req.getModel())
                .setQuestion(req.getQuestion())
                .setOrigin_answer(req.getOriginAnswer())
                .setAi_answer(req.getAiAnswer())
                .setEval_criteria(req.getEvalCriteria());
        return algClient.replyCompare(replyCompareReq).join();
    }

    public void insertLabelTagEsEntity(List<LabelTagEsEntity> labelTagEsEntities) {
        if (CollectionUtil.isEmpty(labelTagEsEntities)) {
            return;
        }
        Map<String, String> id2JsonData = labelTagEsEntities.stream().collect(Collectors.toMap(LabelTagEsEntity::generateId, JSON::toJSONString, (v1, v2) -> v2));
        esService.insert(LabelTagEsEntity.indexName, id2JsonData);
        log.info("labelTagEsEntity add relation success, chatIds={}", id2JsonData.keySet());
    }

    public void deleteLabelTagEsEntity(List<LabelTagEsEntity> labelTagEsEntities) {
        if (CollectionUtil.isEmpty(labelTagEsEntities)) {
            return;
        }
        List<String> chatIds = labelTagEsEntities.stream().map(LabelTagEsEntity::generateId).collect(Collectors.toList());
        esService.delete(LabelTagEsEntity.indexName, chatIds, true);
        log.info("labelTagEsEntity delete relation success, chatIds={}", chatIds);
    }

    public void updateLabelTagEsEntity(List<LabelTagEsEntity> labelTagEsEntities) {
        if (CollectionUtil.isEmpty(labelTagEsEntities)) {
            return;
        }
        Map<String, String> id2JsonData = labelTagEsEntities.stream().collect(Collectors.toMap(LabelTagEsEntity::generateId, JSON::toJSONString, (v1, v2) -> v2));
        esService.update(LabelTagEsEntity.indexName, id2JsonData, true);
        log.info("labelTagEsEntity update relation success, chatIds={}", id2JsonData.keySet());
    }

    //
    @SneakyThrows
    public List<TagQueryRes> getDistribution(TagQueryParam param) {
        List<TagQueryRes> result = new ArrayList<>();
        CompletableFuture<SearchResponse> searchSingleFuture = tagQuerySearchEs(param, false);
        CompletableFuture<SearchResponse> searchMultiFuture = tagQuerySearchEs(param, true);

        SearchResponse searchSingleResponse = searchSingleFuture.get();
        SearchResponse searchMultiResponse = searchMultiFuture.get();
        Map<String, Boolean> aiReplyMap = new HashMap<>();

        ParsedFilter countAggregation = searchSingleResponse.getAggregations().get("all_count");
        long sum = countAggregation.getDocCount();
        ParsedComposite multiFieldAgg = searchSingleResponse.getAggregations().get("multi_field_agg");
        List<ParsedComposite.ParsedBucket> buckets = multiFieldAgg.getBuckets();
        List<TagQueryRes> tagInfoList = new ArrayList<>();
        for (ParsedComposite.ParsedBucket parsedBucket : buckets) {
            Map<String, Object> keyMap = parsedBucket.getKey();
            Long treeId = Long.valueOf(String.valueOf(keyMap.get("treeId")));
            String treeName = String.valueOf(keyMap.get("treeName"));
            String tagIdPath = String.valueOf(keyMap.get("tagIdPath"));
            if (EmptyUtil.isEmpty(tagIdPath)) {
                continue;
            }
            String tagNamePath = String.valueOf(keyMap.get("tagNamePath"))
                    .replace("[", "[\"")
                    .replace("]", "\"]")
                    .replace(", ", "\", \"");
            List<Long> tagIdList = JSON.parseArray(tagIdPath, Long.class);
            List<String> tagNameList = JSON.parseArray(tagNamePath, String.class);

            Set<String> chatIds = new HashSet<>();
            ParsedStringTerms chatAgg = parsedBucket.getAggregations().get("chatIds");
            if (EmptyUtil.isNotEmpty(chatAgg)) {
                List<? extends Terms.Bucket> chatBuckets = chatAgg.getBuckets();
                if (EmptyUtil.isNotEmpty(chatBuckets)) {
                    for (Terms.Bucket chatBucket : chatBuckets) {
                        boolean isAiReply = false;
                        chatIds.add(chatBucket.getKeyAsString());
                        ParsedStringTerms aiReplyAggregations = chatBucket.getAggregations().get("isAiReply");
                        List<? extends Terms.Bucket> aiReplyBuckets = aiReplyAggregations.getBuckets();
                        if(EmptyUtil.isNotEmpty(aiReplyBuckets)){
                            isAiReply = Boolean.parseBoolean(aiReplyBuckets.get(0).getKeyAsString());
                        }
                        aiReplyMap.put(chatBucket.getKeyAsString(), isAiReply);
                    }
                }
            }
            long docCount = parsedBucket.getDocCount();
            tagInfoList.add(new TagQueryRes()
                    .setTagId(tagIdList.get(tagIdList.size() - 1))
                    .setTag(tagNameList.get(tagNameList.size() - 1))
                    .setTagIds(tagIdList)
                    .setTagNames(tagNameList)
                    .setTreeId(treeId)
                    .setChatIds(chatIds)
                    .setTreeName(treeName)
                    .setCount(docCount)
            );
        }

        Map<String, TagQueryRes> tagMap = tagInfoList.stream().collect(Collectors.toMap(p -> p.getTagIds().toString(), e -> e, (e1, e2) -> e1));


        //多标
        ParsedComposite multiFieldAgg2 = searchMultiResponse.getAggregations().get("multi_field_agg");
        List<ParsedComposite.ParsedBucket> buckets2 = multiFieldAgg2.getBuckets();
        for (ParsedComposite.ParsedBucket parsedBucket : buckets2) {
            Map<String, Object> keyMap = parsedBucket.getKey();
            Long treeId = Long.valueOf(String.valueOf(keyMap.get("treeId")));
            String treeName = String.valueOf(keyMap.get("treeName"));
            String tagIdPath = String.valueOf(keyMap.get("tagIdPath"));
            if (EmptyUtil.isEmpty(tagIdPath)) {
                continue;
            }
            String tagNamePath = String.valueOf(keyMap.get("tagNamePath")).replaceAll("(?<=\\[|, )([^\\[\\],]+)(?=\\]|,)", "\"$1\"");

            Set<String> chatIds = new HashSet<>();
            ParsedStringTerms chatAgg = parsedBucket.getAggregations().get("chatIds");
            if (EmptyUtil.isNotEmpty(chatAgg)) {
                List<? extends Terms.Bucket> chatBuckets = chatAgg.getBuckets();
                if (EmptyUtil.isNotEmpty(chatBuckets)) {
                    for (Terms.Bucket chatBucket : chatBuckets) {
                        boolean isAiReply = false;
                        chatIds.add(chatBucket.getKeyAsString());
                        ParsedStringTerms aiReplyAggregations = chatBucket.getAggregations().get("isAiReply");
                        List<? extends Terms.Bucket> aiReplyBuckets = aiReplyAggregations.getBuckets();
                        if(EmptyUtil.isNotEmpty(aiReplyBuckets)){
                            isAiReply = Boolean.parseBoolean(aiReplyBuckets.get(0).getKeyAsString());
                        }
                        aiReplyMap.put(chatBucket.getKeyAsString(), isAiReply);
                    }
                }
            }

            List<List<Long>> tagIdList = JSON.parseObject(tagIdPath, new TypeReference<List<List<Long>>>() {
            });
            List<List<String>> tagNameList = JSON.parseObject(tagNamePath, new TypeReference<List<List<String>>>() {
            });
            long docCount = parsedBucket.getDocCount();

            for (int i = 0; i < tagIdList.size(); i++) {
                List<Long> subTagIdList = tagIdList.get(i);
                List<String> subTagNameList = tagNameList.get(i);
                if (tagMap.containsKey(subTagIdList.toString())) {
                    TagQueryRes tagQueryRes = tagMap.get(subTagIdList.toString());
                    long newCount = docCount + tagQueryRes.getCount();
                    tagQueryRes.setCount(newCount);
                } else {
                    tagMap.put(subTagIdList.toString(), new TagQueryRes()
                            .setTagId(subTagIdList.get(subTagIdList.size() - 1))
                            .setTag(subTagNameList.get(subTagNameList.size() - 1))
                            .setTagIds(subTagIdList)
                            .setChatIds(chatIds)
                            .setTagNames(subTagNameList)
                            .setTreeId(treeId)
                            .setTreeName(treeName)
                            .setCount(docCount));
                }
            }
        }

        List<TagQueryRes> newTagInfoList = new ArrayList<>(tagMap.values());
        Map<Long, List<TagQueryRes>> newTagMap = newTagInfoList.stream().collect(Collectors.groupingBy(TagQueryRes::getTreeId));
        for (Long treeId : newTagMap.keySet()) {
            List<TagQueryRes> tagQueryResList = newTagMap.get(treeId);
            TagQueryRes root = new TagQueryRes();
            for (TagQueryRes tagQueryRes : tagQueryResList) {
                buildPath(root, tagQueryRes, sum);
            }
            root.computeTotalCount(sum);
            result.addAll(root.getChildren());
        }
        result.removeIf(p-> "other".equalsIgnoreCase(p.getTag()));
        sortChildrenByCountDesc(result, aiReplyMap);
        return result;
    }

    private void sortChildrenByCountDesc(List<TagQueryRes> tagQueryResList, Map<String, Boolean> aiReplyMap) {
        if (tagQueryResList == null || tagQueryResList.isEmpty()) {
            return;
        }
        tagQueryResList.sort((o1, o2) -> Long.compare(o2.getCount(), o1.getCount()));

        for (TagQueryRes tagQueryRes : tagQueryResList) {
            double aiReplyRate = 0.0;
            Set<String> chatIds = tagQueryRes.getChatIds();
            if (EmptyUtil.isNotEmpty(chatIds)){
                long aiReplyCount = chatIds.stream().filter(p -> Boolean.TRUE.equals(aiReplyMap.get(p))).count();
                aiReplyRate = NumberUtil.div(aiReplyCount, chatIds.size(), 2);
            }
            tagQueryRes.setAiReplyRate(aiReplyRate);


            // 对当前节点的 children 按 count 倒排
            tagQueryRes.getChildren().sort((o1, o2) -> Long.compare(o2.getCount(), o1.getCount()));

            // 递归处理子节点
            sortChildrenByCountDesc(tagQueryRes.getChildren(), aiReplyMap);
        }
    }

    private void buildPath(TagQueryRes current, TagQueryRes tag, Long sum) {
        List<Long> tagIds = tag.getTagIds();
        List<String> tagNames = tag.getTagNames();

        for (int i = 0; i < tagIds.size(); i++) {
            Long currentId = tagIds.get(i);
            String currentName = tagNames.get(i);

            // 检查是否存在当前路径的子节点
            TagQueryRes nextNode = findChildByTagId(current, currentId);
            if (nextNode == null) {
                // 如果没有子节点则创建
                nextNode = new TagQueryRes()
                        .setTreeId(tag.getTreeId())
                        .setTreeName(tag.getTreeName())
                        .setTag(currentName)
                        .setTagId(currentId)
//                        .setChatIds(new HashSet<>(tag.getChatIds()))
                        .setTagIds(tagIds.subList(0, i + 1))
                        .setTagNames(tagNames.subList(0, i + 1));
                current.addChild(nextNode);
            }
//            else {
//                nextNode.getChatIds().addAll(tag.getChatIds());
//            }
            if (i == tagIds.size() - 1) {
                nextNode.setChatIds(new HashSet<>());
                nextNode.getChatIds().addAll(tag.getChatIds());
                nextNode.setCount(tag.getCount());
            }
            // 继续向下递归
            current = nextNode;
        }
    }

    // 查找子节点
    private TagQueryRes findChildByTagId(TagQueryRes parent, Long tagId) {
        for (TagQueryRes child : parent.getChildren()) {
            if (Objects.equals(child.getTagId(), tagId)) {
                return child;
            }
        }
        return null;
    }


    //查询单标
    public CompletableFuture<SearchResponse> tagQuerySearchEs(TagQueryParam param, Boolean isMultiTag) {
        String dslVmPath = "es_query_dsl/tag_query.ftljson";
        Map<String, Object> params = new HashMap<>();
        if (isMultiTag) {
            params.put("isMultiTag", true);
        } else {
            params.put("isSingleTag", true);
        }
//        if (param.getSize() != null) params.put("size", param.getSize());
        if (param.getTreeId() != null) params.put("treeId", param.getTreeId());
        if (param.getTenantId() != null) params.put("tenantId", param.getTenantId());
        if (param.getProjectId() != null) params.put("projectId", param.getProjectId());
        if (param.getTaskId() != null) {
            params.put("taskId", param.getTaskId());
        } else {
            params.put("exceptTask", true);
        }
        if (param.getStartTime() != null) params.put("startTime", param.getStartTime().toString());
        if (param.getEndTime() != null) params.put("endTime", param.getEndTime());
        if (param.getBeginTimestamp() != null) params.put("startTime", param.getBeginTimestamp().toString());
        if (param.getEndTimestamp() != null) params.put("endTime", param.getEndTimestamp());

        String dsl = FreeMakerUtil.render(dslVmPath, params);
        JSONObject dslJson = JSON.parseObject(dsl);
        SearchRequest searchRequest = esSearchService.getSearchRequest(LabelTagEsEntity.indexName, dslJson.toJSONString());
        return esSearchService.search(searchRequest);
    }


    @SneakyThrows
    public List<CategorySampleEsEntity> searchCategory(String input) {
        List<EsSearchResult<CategorySampleEsEntity>> res = esSearchService.wildQueryPrefixAllFieldAsync(CategorySampleEsEntity.indexName, 20, null, "endCategory", "categoryPath", input, CategorySampleEsEntity.class).get();
        return res.stream().map(p -> {
            CategorySampleEsEntity data = p.getData();
            List<String> categoryPath = data.getCategoryPath();
            if (categoryPath.size() == 1) {
                data.setSelectedRes(categoryPath.get(0));
            } else {
                data.setSelectedRes(String.join("-", categoryPath));
            }
            return data;
        }).collect(Collectors.toList());
    }

    //自动生成describe
    @SneakyThrows
    public TagDescriptionRes getAutoDescribe(TagDescriptionReq tagDescriptionReq) {
        return algClient.getTagDescription(tagDescriptionReq).get();
    }



    // 获取指定chatId的标签数据
    @SneakyThrows
    public List<GetLabelTagRes> getLabelTags(Long tenantId, Long projectId, String chatId, List<String> messageIds) {
        List<GetLabelTagRes> result = new ArrayList<>();
        if (EmptyUtil.isNotEmpty(chatId) && EmptyUtil.isNotEmpty(messageIds)) {
            List<Pair<String, Object>> termQueryParam = new ArrayList<>();
            termQueryParam.add(Pair.of(LambdaUtil.getFieldName(LabelTagEsEntity::getTenantId), tenantId));
            termQueryParam.add(Pair.of(LambdaUtil.getFieldName(LabelTagEsEntity::getProjectId), projectId));
            termQueryParam.add(Pair.of(LambdaUtil.getFieldName(LabelTagEsEntity::getChatId)+ ".keyword", chatId));
            termQueryParam.add(Pair.of(LambdaUtil.getFieldName(LabelTagEsEntity::getMessageId)+ ".keyword", messageIds));
            List<EsSearchResult<LabelTagEsEntity>> esSearchResults = esSearchService.termQueryAsync(LabelTagEsEntity.indexName, messageIds.size(), termQueryParam, Collections.singletonList(LambdaUtil.getFieldName(LabelTagEsEntity::getTaskId)), Collections.singletonList(LambdaUtil.getFieldName(LabelTagEsEntity::getContent)), LabelTagEsEntity.class).get();
            esSearchResults.forEach(esSearchResult->{
                LabelTagEsEntity data = esSearchResult.getData();
                if (data != null) {
                    GetLabelTagRes labelTagRes = new GetLabelTagRes();
                    labelTagRes.setTenantId(data.getTenantId());
                    labelTagRes.setProjectId(data.getProjectId());
                    labelTagRes.setChatId(data.getChatId());
                    labelTagRes.setMessageId(data.getMessageId());
                    labelTagRes.setTreeId(data.getTreeId());
                    labelTagRes.setTreeName(data.getTreeName());
                    if (EmptyUtil.isNotEmpty(data.getTagNamePath())){
                        labelTagRes.setTagNamePath(Collections.singletonList(data.getTagNamePath()));
                        labelTagRes.setTagIdPath(Collections.singletonList(data.getTagIdPath()));
                        String endTags = data.getTagNamePath().get(data.getTagNamePath().size() - 1);
                        labelTagRes.setEndTags(Collections.singletonList(endTags));
                    } else if (EmptyUtil.isNotEmpty(data.getMultiTagNamePath())) {
                        labelTagRes.setTagNamePath(data.getMultiTagNamePath());
                        labelTagRes.setTagIdPath(data.getMultiTagIdPath());
                        List<String> endTags = data.getMultiTagNamePath().stream().map(p -> p.get(p.size() - 1)).collect(Collectors.toList());
                        labelTagRes.setEndTags(endTags);
                    }
                    result.add(labelTagRes);
                }
            });
        }
        return result;
    }


}
