package com.shulex.qabot.es.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
public class ProductSearchParam {
    Integer from;
    Integer size;
    Long tenantId;
    Long projectId;
    Long documentId;
    Boolean enable;
    List<String> productIds;
    List<String> titleList;
    List<String> describeList;
    Double minSkuPrice;
    Double maxSkuPrice;
}
