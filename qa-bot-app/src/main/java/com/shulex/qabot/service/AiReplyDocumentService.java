package com.shulex.qabot.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.lang.func.LambdaUtil;
import com.alibaba.fastjson.JSON;
import com.shulex.gpt.apisdk.IKBApi;
import com.shulex.gpt.apisdk.dto.GetDocumentResponse;
import com.shulex.gpt.apisdk.dto.MixedQuery;
import com.shulex.gpt.apisdk.dto.VideoInfo;
import com.shulex.qabot.alg.AlgClient;
import com.shulex.qabot.alg.dto.RelevantScoreReq;
import com.shulex.qabot.alg.dto.TextExtractXReq;
import com.shulex.qabot.alg.dto.TextExtractXRes;
import com.shulex.qabot.client.dto.EsSearchResult;
import com.shulex.qabot.client.dto.esentity.DocumentChunkEsEntity;
import com.shulex.qabot.client.dto.esentity.KuEsEntity;
import com.shulex.qabot.config.SpecialNeedConfig;
import com.shulex.qabot.es.EsSearchService;
import com.shulex.qabot.es.EsService;
import com.shulex.qabot.client.req.DocumentQaReq;
import com.shulex.qabot.client.res.DocumentQaContext;
import com.shulex.qabot.config.PromptConfig;
import com.shulex.qabot.config.QaChatConfig;
import com.shulex.qabot.mysql.repository.*;
import com.shulex.qabot.util.ParallelUtil;
import com.shulex.qabot.util.TenantUtil;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.index.query.MatchQueryBuilder;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.search.MatchQuery;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
@Slf4j
public class AiReplyDocumentService {
    @Autowired
    BedrockService bedrockService;
    @Autowired
    EsSearchService esSearchService;
    @Autowired
    QaChatConfig qaChatConfig;
    @Autowired
    SpecialNeedConfig specialNeedConfig;
    @Autowired
    IKBApi kbApi;
    @Autowired
    DocumentChunkRepository documentChunkRepository;
    @Autowired
    PromptConfig promptConfig;
    @Autowired
    AlgClient algClient;

    public DocumentQaContext getDocumentQaContext(DocumentQaReq documentQaReq) {
        int wordsLimit = documentQaReq.getWordsLimit() != null ? documentQaReq.getWordsLimit() : qaChatConfig.getDocumentRespWordsLimit();
        // 特殊的一些意图不进行chunk搜索
        if ("greeting".equalsIgnoreCase(documentQaReq.getIntent()) || "Customer Say Good".equalsIgnoreCase(documentQaReq.getIntent())) {
            String systemContent = promptConfig.getSystemContent(documentQaReq.getTenantId(), documentQaReq.getAiReplyPromptInfo(), documentQaReq.getRoleDescription(), documentQaReq.getLanguage().name().toLowerCase(), wordsLimit, documentQaReq.getModelType());
            return new DocumentQaContext().setSystemContent(systemContent);
        }
        // 如果租户没有文档chunk直接不回答
        if (!documentChunkRepository.existChunk(documentQaReq.getTenantId())) {
            return null;
        }
        String chatId = documentQaReq.getChatId();
        MixedQuery mixedQuery = documentQaReq.getMixedQuery();
        String userQuestion = IntentService.joinTitleContent(mixedQuery.getOriginTitle(), mixedQuery.getOriginContent());
        // search chunk
        List<Pair<String, Object>> termQueryFieldAndValue = new ArrayList<>();
        termQueryFieldAndValue.add(Pair.of(LambdaUtil.getFieldName(DocumentChunkEsEntity::getTenantId), documentQaReq.getTenantId()));
        termQueryFieldAndValue.add(Pair.of(LambdaUtil.getFieldName(DocumentChunkEsEntity::getProjectId), documentQaReq.getProjectId()));
        termQueryFieldAndValue.add(Pair.of(LambdaUtil.getFieldName(DocumentChunkEsEntity::getEnable), true));
        if (CollectionUtil.isNotEmpty(documentQaReq.getKuLabelDimensionId2ValueIds())) {
            documentQaReq.getKuLabelDimensionId2ValueIds().forEach((dimensionId, valueIds) -> {
                if (CollectionUtil.isNotEmpty(valueIds)) {
                    termQueryFieldAndValue.add(Pair.of(LambdaUtil.getFieldName(DocumentChunkEsEntity::getKuLabelValueIds), valueIds));
                }
            });
        }
        TextExtractXRes textExtractXRes;
        boolean enableNewDocSearch = specialNeedConfig.getEnableNewDocSearchBy(documentQaReq.getTenantId());
        String chunkFromIntent = null;
        if (enableNewDocSearch) {
            TextExtractXReq textExtractXReq = new TextExtractXReq()
                    .setTenant_id(documentQaReq.getTenantId())
                    .setClient(TenantUtil.getClientType(documentQaReq.getTenantId()))
                    .setChat_id(documentQaReq.getChatId())
                    .setData_type("string")
                    .setEntity_types(Arrays.asList("common_entity"))
                    .setContent(String.format("%s %s", userQuestion, mixedQuery.getUserQuery()));
            textExtractXRes = algClient.textExtractX(textExtractXReq).join();
            if (StringUtils.hasText(documentQaReq.getIntent())) {
                String intent = documentQaReq.getIntent();
                intent = intent.replaceAll(" ", "");
                chunkFromIntent = specialNeedConfig.getEnableNewDocSearchIntentChunk().get(intent.toLowerCase());
            }
        } else {
            textExtractXRes = null;
        }
        Function<List<BigDecimal>, List<EsSearchResult<DocumentChunkEsEntity>>> searchChunkFunc = embedding -> {
            Integer size = qaChatConfig.getDocumentChunkContentEsSearchSizeBy(documentQaReq.getTenantId());
            if (enableNewDocSearch) {
                log.info("documentQA[{}]: textExtractXRes={}", chatId, textExtractXRes);
                List<String> commonEntities = textExtractXRes.getResult().getCommon_entity()
                        != null ? textExtractXRes.getResult().getCommon_entity() : new ArrayList<>();
//                if (StringUtils.hasText(documentQaReq.getIntent())) {
//                    commonEntities.add(documentQaReq.getIntent());
//                }
                log.info("documentQA[{}]: commonEntities={}", chatId, commonEntities);
                return esSearchService.embeddingSearchAsync(
                        chatId,
                        DocumentChunkEsEntity.indexName,
                        embedding,
                        LambdaUtil.getFieldName(DocumentChunkEsEntity::getContentEmbedding),
                        size,
                        qaChatConfig.getEsTextScoreMax(),
                        qaChatConfig.getEsTextScoreMin(),
                        qaChatConfig.getEmbeddingScaleMax(),
                        qaChatConfig.getEmbeddingScaleMin(),
                        boolQueryBuilder -> {
                            EsSearchService.addTermQueryToBuilder(termQueryFieldAndValue, boolQueryBuilder);
                            if (CollectionUtil.isNotEmpty(commonEntities)) {
                                List<String> fieldNames = Arrays.asList(
                                        LambdaUtil.getFieldName(DocumentChunkEsEntity::getBrief),
                                        LambdaUtil.getFieldName(DocumentChunkEsEntity::getTags),
                                        LambdaUtil.getFieldName(DocumentChunkEsEntity::getContent)
                                );
                                for (String commonEntity : commonEntities) {
                                    for (String fieldName : fieldNames) {
                                        MatchQueryBuilder matchQueryBuilder = new MatchQueryBuilder(fieldName, commonEntity);
                                        Float boost = qaChatConfig.getFieldBoostMap().get(fieldName);
                                        if (boost != null) {
                                            matchQueryBuilder.boost(boost);
                                        }
                                        boolQueryBuilder.should(matchQueryBuilder);
                                    }
                                }
                                if (StringUtils.hasText(documentQaReq.getIntent())) {
                                    for (String fieldName : fieldNames) {
                                        MatchQueryBuilder matchQueryBuilder = new MatchQueryBuilder(fieldName, documentQaReq.getIntent());
                                        Float boost = qaChatConfig.getFieldBoostMap().get(fieldName);
                                        if (boost != null) {
                                            matchQueryBuilder.boost(boost * qaChatConfig.getIntentBoostScale());
                                        }
                                        boolQueryBuilder.should(matchQueryBuilder);
                                    }
                                    // 购买咨询排除
                                    if (documentQaReq.getIntent().equalsIgnoreCase("Purchase Inquiry")) {
                                        MatchQueryBuilder matchQueryBuilder = new MatchQueryBuilder(LambdaUtil.getFieldName(DocumentChunkEsEntity::getTags), "Troubleshooting");
                                        boolQueryBuilder.mustNot(matchQueryBuilder);
                                    }
                                }
                                boolQueryBuilder.minimumShouldMatch(0);
                            }
                        },
                        EsService.getEmbeddingFieldNames(DocumentChunkEsEntity.class),
                        DocumentChunkEsEntity.class
                ).join();
            } else {
                return esSearchService.embeddingCosSearchAsync(
                        DocumentChunkEsEntity.indexName,
                        embedding,
                        LambdaUtil.getFieldName(DocumentChunkEsEntity::getContentEmbedding),
                        size,
                        termQueryFieldAndValue,
                        EsService.getEmbeddingFieldNames(DocumentChunkEsEntity.class),
                        DocumentChunkEsEntity.class
                ).join();
            }
        };
        List<BigDecimal> userEnQueryEmbedding = bedrockService.getCohereEmbeddingAsync(mixedQuery.getUserEnQuery()).join();
        List<EsSearchResult<DocumentChunkEsEntity>> esResult1 = searchChunkFunc.apply(userEnQueryEmbedding);
        List<EsSearchResult<DocumentChunkEsEntity>> esResult2;
        if (mixedQuery.getUserQuery() != null && !mixedQuery.getUserQuery().equals(mixedQuery.getUserEnQuery())) {
            List<BigDecimal> userQueryEmbedding = bedrockService.getCohereEmbeddingAsync(mixedQuery.getUserQuery()).join();
            esResult2 = searchChunkFunc.apply(userQueryEmbedding);
        } else {
            esResult2 = new ArrayList<>();
        }
        List<BigDecimal> userQuestionEmbedding = bedrockService.getCohereEmbeddingAsync(userQuestion).join();
        List<EsSearchResult<DocumentChunkEsEntity>> esResult3 = searchChunkFunc.apply(userQuestionEmbedding);
        List<EsSearchResult<DocumentChunkEsEntity>> esSearchChunks = Stream.concat(Stream.concat(esResult1.stream(), esResult2.stream()), esResult3.stream()).collect(Collectors.toList());
        log.info("documentQA[{}]: esSearchChunks={}", chatId, JSON.toJSONString(esSearchChunks));
        List<DocumentChunkEsEntity> searchedChunks = esSearchChunks.stream().filter(e -> e.getScore() >= qaChatConfig.getDocumentChunkContentThresholdBy(documentQaReq.getTenantId())).map(EsSearchResult::getData).collect(Collectors.toList());
        log.info("documentQA[{}]: searchedChunks_id={}", chatId, searchedChunks.stream().map(DocumentChunkEsEntity::getId).collect(Collectors.toList()));
        if (CollectionUtil.isEmpty(searchedChunks)) {
            // 没有符合阈值的chunk
            return new DocumentQaContext().setSearchChunkEsResults(esSearchChunks);
        }
        LinkedHashMap<Long, DocumentChunkEsEntity> searchedChunksDistinct = new LinkedHashMap<>();
        searchedChunks.forEach(searchedChunk -> {
            searchedChunksDistinct.putIfAbsent(searchedChunk.getId(), searchedChunk);
        });
        searchedChunks = new ArrayList<>(searchedChunksDistinct.values());
        log.info("documentQA[{}]: after distinct searchedChunks_id={}", chatId, searchedChunks.stream().map(DocumentChunkEsEntity::getId).collect(Collectors.toList()));
        // relevance judge
        List<EsSearchResult<DocumentChunkEsEntity>> relevanceChunks = new ArrayList<>();
        AtomicReference<Double> minRelevanceValue = new AtomicReference<>();
        boolean needRelevance = CollectionUtil.isNotEmpty(qaChatConfig.getNeedDocumentRelevanceTenantIds()) && qaChatConfig.getNeedDocumentRelevanceTenantIds().contains(documentQaReq.getTenantId());
        if (needRelevance) {
            ParallelUtil.parallelAsyncCall(5, false, searchedChunks, chunk -> {
                RelevantScoreReq relevantScoreReq = new RelevantScoreReq()
                        .setTenant_id(documentQaReq.getTenantId())
                        .setChat_id(documentQaReq.getChatId())
                        .setQuestion(mixedQuery.getUserEnQuery())
                        .setContext(chunk.getContent());
                return algClient.relevantScore(relevantScoreReq).thenAccept(relevantScoreRes -> {
                    Float relevantScore = relevantScoreRes.getResult().getRelevant_score();
                    minRelevanceValue.set(relevantScoreRes.getResult().getRelevant_min_score().doubleValue());
                    relevanceChunks.add(new EsSearchResult<DocumentChunkEsEntity>().setData(chunk).setScore(Double.valueOf(relevantScore)));
                });
            });
        }
        if (CollectionUtil.isNotEmpty(relevanceChunks)) {
            relevanceChunks.sort((e1, e2) -> e2.getScore().compareTo(e1.getScore()));
            searchedChunks = relevanceChunks.stream().filter(e -> e.getScore() >= minRelevanceValue.get()).map(EsSearchResult::getData).collect(Collectors.toList());
        }
        if (CollectionUtil.isEmpty(searchedChunks)) {
            // 没有符合relevance的chunk
            return new DocumentQaContext().setSearchChunkEsResults(esSearchChunks).setRelevanceChunks(relevanceChunks);
        }
        // expand chunk
        int expandSize = qaChatConfig.getDocumentChunkExpandSizeBy(documentQaReq.getTenantId());
        List<List<DocumentChunkEsEntity>> expandedChunks = searchedChunks.stream().map(chunk -> {
                    if (expandSize > 0) {
                        Integer currentSeq = chunk.getChunkSeq();
                        List<Integer> querySeq = new ArrayList<>();
                        for (int i = 1; i <= expandSize; i++) {
                            querySeq.add(currentSeq - i);
                            querySeq.add(currentSeq + i);
                        }
                        List<Pair<String, Object>> expandChunkSearchQuery = new ArrayList<>();
                        expandChunkSearchQuery.add(Pair.of(LambdaUtil.getFieldName(DocumentChunkEsEntity::getTenantId), documentQaReq.getTenantId()));
                        expandChunkSearchQuery.add(Pair.of(LambdaUtil.getFieldName(DocumentChunkEsEntity::getProjectId), documentQaReq.getProjectId()));
                        expandChunkSearchQuery.add(Pair.of(LambdaUtil.getFieldName(DocumentChunkEsEntity::getDocumentId), chunk.getDocumentId()));
                        expandChunkSearchQuery.add(Pair.of(LambdaUtil.getFieldName(DocumentChunkEsEntity::getResourceId), chunk.getResourceId()));
                        expandChunkSearchQuery.add(Pair.of(LambdaUtil.getFieldName(DocumentChunkEsEntity::getChunkSeq), querySeq));
                        return esSearchService.termQueryAsync(DocumentChunkEsEntity.indexName, querySeq.size(), expandChunkSearchQuery, EsService.getEmbeddingFieldNames(DocumentChunkEsEntity.class),
                                DocumentChunkEsEntity.class).thenApply(esResults -> {
                            List<DocumentChunkEsEntity> expandChunks = esResults.stream().map(EsSearchResult::getData).collect(Collectors.toList());
                            return Pair.of(chunk, expandChunks);
                        });
                    } else {
                        List<DocumentChunkEsEntity> expandChunks = new ArrayList<>();
                        return CompletableFuture.completedFuture(Pair.of(chunk, expandChunks));
                    }
                }).collect(Collectors.toList())
                .stream().map(f -> {
                    Pair<DocumentChunkEsEntity, List<DocumentChunkEsEntity>> pair = f.join();
                    DocumentChunkEsEntity chunk = pair.getKey();
                    List<DocumentChunkEsEntity> expandChunks = pair.getValue();
                    if (needRelevance) {
                        List<DocumentChunkEsEntity> relevanceExpandChunks = new ArrayList<>();
                        ParallelUtil.parallelAsyncCall(3, false, expandChunks, expandChunk -> {
                            if (expandChunk.getChunkSeq() > chunk.getChunkSeq()) {
                                relevanceExpandChunks.add(expandChunk);
                                return CompletableFuture.completedFuture(null);
                            } else {
                                RelevantScoreReq relevantScoreReq = new RelevantScoreReq()
                                        .setTenant_id(documentQaReq.getTenantId())
                                        .setChat_id(documentQaReq.getChatId())
                                        .setQuestion(mixedQuery.getUserEnQuery())
                                        .setContext(expandChunk.getContent());
                                return algClient.relevantScore(relevantScoreReq).thenAccept(relevantScoreRes -> {
                                    Float relevantScore = relevantScoreRes.getResult().getRelevant_score();
                                    Float relevantMinScore = relevantScoreRes.getResult().getRelevant_min_score();
                                    if (relevantScore >= relevantMinScore) {
                                        relevanceExpandChunks.add(expandChunk);
                                    }
                                });
                            }
                        });
                        expandChunks = relevanceExpandChunks;
                    }
                    expandChunks.add(chunk);
                    expandChunks.sort(Comparator.comparing(DocumentChunkEsEntity::getChunkSeq));
                    return expandChunks;
                }).filter(CollectionUtil::isNotEmpty).collect(Collectors.toList());
        LinkedHashMap<Long, List<List<DocumentChunkEsEntity>>> documentId2ExpandedChunks = new LinkedHashMap<>();
        for (List<DocumentChunkEsEntity> expandedChunk : expandedChunks) {
            Long documentId = expandedChunk.get(0).getDocumentId();
            documentId2ExpandedChunks.computeIfAbsent(documentId, k -> new ArrayList<>()).add(expandedChunk);
        }
        List<List<DocumentChunkEsEntity>> mergedExpandChunks = new ArrayList<>();
        for (Map.Entry<Long, List<List<DocumentChunkEsEntity>>> entry : documentId2ExpandedChunks.entrySet()) {
            List<List<DocumentChunkEsEntity>> multiExpandChunks = entry.getValue();
            Map<Integer, DocumentChunkEsEntity> seq2Chunk = new HashMap<>();
            for (List<DocumentChunkEsEntity> expandChunk : multiExpandChunks) {
                for (DocumentChunkEsEntity chunk : expandChunk) {
                    seq2Chunk.put(chunk.getChunkSeq(), chunk);
                }
            }
            List<DocumentChunkEsEntity> mergedExpandChunkList = seq2Chunk.values().stream().sorted(Comparator.comparing(DocumentChunkEsEntity::getChunkSeq)).collect(Collectors.toList());
            mergedExpandChunks.add(mergedExpandChunkList);
        }
        // 根据分数排序并取topN
        Map<Long, Double> documentId2Score = new HashMap<>();
        for (EsSearchResult<DocumentChunkEsEntity> esResult : Stream.concat(esSearchChunks.stream(), relevanceChunks.stream()).collect(Collectors.toList())) {
            Double score = esResult.getScore();
            Long documentId = esResult.getData().getDocumentId();
            documentId2Score.putIfAbsent(documentId, score);
            if (score > documentId2Score.get(documentId)) {
                documentId2Score.put(documentId, score);
            }
        }
        mergedExpandChunks.sort((e1, e2) -> documentId2Score.get(e2.get(0).getDocumentId()).compareTo(documentId2Score.get(e1.get(0).getDocumentId())));
        mergedExpandChunks = mergedExpandChunks.subList(0, Math.min(mergedExpandChunks.size(), qaChatConfig.getDocumentRelevanceCountBy(documentQaReq.getTenantId())));
        // 查询document名字
        List<Long> documentIds = searchedChunks.stream().map(DocumentChunkEsEntity::getDocumentId).distinct().collect(Collectors.toList());
        List<GetDocumentResponse> documents = kbApi.getDocuments(documentQaReq.getTenantId(), documentQaReq.getProjectId(), null, documentIds, null);
        Map<Long, GetDocumentResponse> id2Document = documents.stream().collect(Collectors.toMap(GetDocumentResponse::getId, e -> e, (e1, e2) -> e1));
        // 拼接prompt
        List<String> chunkContentList = new ArrayList<>();
        if (StringUtils.hasText(chunkFromIntent)) {
            chunkContentList.add(String.format("## Chunk 0.\n\n#### Content\n\n%s", chunkFromIntent));
        }
        for (int i = 0; i < mergedExpandChunks.size(); i++) {
            StringBuilder chunkContent = new StringBuilder();
            chunkContent.append("## Chunk ").append(i + 1).append(".\n\n");
            List<DocumentChunkEsEntity> entities = mergedExpandChunks.get(i);
            Long documentId = entities.get(0).getDocumentId();
            GetDocumentResponse document = id2Document.get(documentId);
            if (document == null) {
                log.warn("documentQA[{}]: documentId[id={}] not found, maybe document is deleted in mysql but not delete chunk in es because of kafka consume slowly", chatId, documentId);
            }
            chunkContent.append("### ").append(document == null ? "document " + (i + 1) : document.getFileName()).append("\n\n");
            if (document != null && StringUtils.hasText(document.getAiReplyRequiredContent())) {
                chunkContent.append(document.getAiReplyRequiredContent()).append("\n\n");
            }
            chunkContent.append("#### Content\n\n");
            String content = entities.stream().map(DocumentChunkEsEntity::getContent).collect(Collectors.joining("\n"));
            chunkContent.append(content);
            if (document != null && CollectionUtil.isNotEmpty(document.getVideos())) {
                List<VideoInfo> videos = document.getVideos().stream()
                        .filter(e -> e != null && StringUtils.hasText(e.getTitle()) && StringUtils.hasText(e.getUrl()))
                        .collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(videos)) {
                    chunkContent.append("\n\n#### Video\n\n");
                    List<String> videoContents = new ArrayList<>();
                    for (int j = 0; j < videos.size(); j++) {
                        VideoInfo videoInfo = videos.get(j);
                        int seq = j + 1;
                        videoContents.add(String.format("##### VideoIntroduction %s.\n%s\n\n##### VideoLink %s.\n%s", seq, videoInfo.getTitle(), seq, videoInfo.getUrl()));
                    }
                    String videoContent = String.join("\n\n", videoContents);
                    chunkContent.append(videoContent);
                }
            }
            chunkContentList.add(chunkContent.toString());
        }
        String chunkContents = String.join("\n\n------\n\n", chunkContentList);
        String systemContent = promptConfig.getSystemContent(documentQaReq.getTenantId(), documentQaReq.getAiReplyPromptInfo(), chunkContents, documentQaReq.getLanguage().name().toLowerCase(), wordsLimit, documentQaReq.getModelType());
        return new DocumentQaContext()
                .setSearchChunkEsResults(esSearchChunks)
                .setRelevanceChunks(relevanceChunks)
                .setUsedChunks(mergedExpandChunks)
                .setUsedDocuments(mergedExpandChunks.stream().map(e -> id2Document.get(e.get(0).getDocumentId())).collect(Collectors.toList()))
                .setChunkContents(chunkContents)
                .setSystemContent(systemContent);
    }
}
