package com.shulex.qabot.alg.dto;

import com.alibaba.fastjson.JSONObject;
import com.shulex.gpt.apisdk.dto.LLMUsages;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class RelevantScoreRes {
    Result result;
    LLMUsages usages;

    @Data
    @Accessors(chain = true)
    public static class Result {
        private Float relevant_min_score;
        private Float relevant_score;
        private JSONObject meta_data;
    }
}
