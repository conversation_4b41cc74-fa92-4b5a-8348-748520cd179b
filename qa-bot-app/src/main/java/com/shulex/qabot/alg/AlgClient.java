package com.shulex.qabot.alg;

import cn.hutool.cache.impl.TimedCache;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.shulex.gpt.apisdk.dto.LLMUsages;
import com.shulex.qabot.alg.dto.*;
import com.shulex.qabot.client.dto.AiAgentRes;
import com.shulex.qabot.client.res.*;
import com.shulex.qabot.config.AlgConfig;
import com.shulex.qabot.util.AsyncHttpUtil;
import com.shulex.qabot.util.MetadataUtil;
import com.shulex.qabot.util.ParallelUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.concurrent.CompletableFuture;

@Service
@Slf4j
public class AlgClient {
    @Autowired
    AlgConfig algConfig;
    int retryCount = 3;
    TimedCache<String, Boolean> chatId2IsV2 = new TimedCache<>(1000 * 60 * 5);

    public CompletableFuture<TextEmbedRes> textEmbed(String text) {
        TextEmbedReq textEmbedReq = new TextEmbedReq().setText(text);
        return textEmbed(textEmbedReq);
    }

    public CompletableFuture<TextEmbedRes> textEmbed(TextEmbedReq textEmbedReq) {
        return ParallelUtil.retry(() -> AsyncHttpUtil.post(algConfig.getHost() + "/text_embed", textEmbedReq, new TypeReference<TextEmbedRes>() {
        }), retryCount);
    }

    public CompletableFuture<QaChatRes> aiReply(AiReplyReq aiReplyReq) {
        return ParallelUtil.retry(() -> AsyncHttpUtil.post(algConfig.getHost() + "/ai_reply", aiReplyReq, new TypeReference<QaChatRes>() {
        }).thenApply(res -> {
            res.setUsages(LLMUsages.of(JSON.toJSONString(res.getUsages())));
            return res;
        }), retryCount);
    }

    public CompletableFuture<SummaryRes> summary(SummaryReq summaryReq) {
        String path = "/v2/summary";
        return ParallelUtil.retry(() -> AsyncHttpUtil.post(algConfig.getHost() + path, summaryReq, new TypeReference<SummaryRes>() {
        }).thenApply(res -> {
            res.setUsages(LLMUsages.of(JSON.toJSONString(res.getUsages())));
            return res;
        }), retryCount);
    }

    public CompletableFuture<ReplyCompareRes> replyCompare(ReplyCompareReq replyCompareReq) {
        String path = "/ops_tool/reply_compare";
        return ParallelUtil.retry(() -> AsyncHttpUtil.post(algConfig.getHost() + path, replyCompareReq, new TypeReference<ReplyCompareRes>() {
        }).thenApply(res -> {
            res.setUsages(LLMUsages.of(JSON.toJSONString(res.getUsages())));
            return res;
        }), retryCount);
    }

    public CompletableFuture<TagDescriptionRes> getTagDescription(TagDescriptionReq tagDescriptionReq) {
        String path = "/tag_description";
        return ParallelUtil.retry(() -> AsyncHttpUtil.post(algConfig.getHost() + path, tagDescriptionReq, new TypeReference<TagDescriptionRes>() {
        }).thenApply(res -> {
            res.setUsages(LLMUsages.of(JSON.toJSONString(res.getUsages())));
            return res;
        }), retryCount);
    }

    public CompletableFuture<CustomizedPromptRes> customizedPrompt(CustomizedPromptReq customizedPromptReq) {
        String path = "/ops_tool/customized_prompt";
        return ParallelUtil.retry(() -> AsyncHttpUtil.post(algConfig.getHost() + path, customizedPromptReq, new TypeReference<CustomizedPromptRes>() {
        }).thenApply(res -> {
            res.setUsages(LLMUsages.of(JSON.toJSONString(res.getUsages())));
            return res;
        }), retryCount);
    }

    public CompletableFuture<IntentIdentifyRes> intentIdentify(IntentIdentifyReq intentIdentifyReq) {
        String path = "/v2/intent_identify";
        Boolean algOffline = MetadataUtil.getFirstValue(intentIdentifyReq.getMetadata(), "ALG_OFFLINE", Boolean.class);
        String host = Boolean.TRUE.equals(algOffline) ? algConfig.getTagHost() : algConfig.getHost();
        log.info("intentIdentify[{}]: algOffline={}, host={}", intentIdentifyReq.getChat_id(), algOffline, host);
        return ParallelUtil.retry(() -> AsyncHttpUtil.post(host + path, intentIdentifyReq, new TypeReference<IntentIdentifyRes>() {
        }).thenApply(res -> {
            res.setUsages(LLMUsages.of(JSON.toJSONString(res.getUsages())));
            return res;
        }), retryCount);
    }

    public CompletableFuture<IntentIdentifyRes> tagIdentify(IntentIdentifyReq intentIdentifyReq, String path) {
        return ParallelUtil.retry(() -> AsyncHttpUtil.post(algConfig.getTagHost() + path, intentIdentifyReq, new TypeReference<IntentIdentifyRes>() {
        }).thenApply(res -> {
            res.setUsages(LLMUsages.of(JSON.toJSONString(res.getUsages())));
            return res;
        }), retryCount);
    }

    public CompletableFuture<IntentPureRes> intentPrue(IntentPureReq intentPureReq) {
        return ParallelUtil.retry(() -> AsyncHttpUtil.post(algConfig.getHost() + "/intent_pure", intentPureReq, new TypeReference<IntentPureRes>() {
        }).thenApply(res -> {
            res.setUsages(LLMUsages.of(JSON.toJSONString(res.getUsages())));
            return res;
        }), retryCount);
    }

    public CompletableFuture<AiAgentRes> aiAgent(AiAgentReq aiAgentReq) {
        return ParallelUtil.retry(() -> AsyncHttpUtil.post(algConfig.getHost() + "/ai_agent", aiAgentReq, new TypeReference<AiAgentRes>() {
        }).thenApply(res -> {
            res.setUsages(LLMUsages.of(JSON.toJSONString(res.getUsages())));
            return res;
        }), retryCount);
    }

    public CompletableFuture<TranslateRes> translate(TranslateReq translateReq) {
        return ParallelUtil.retry(() -> AsyncHttpUtil.post(algConfig.getHost() + "/translate", translateReq, new TypeReference<TranslateRes>() {
        }).thenApply(res -> {
            res.setUsages(LLMUsages.of(JSON.toJSONString(res.getUsages())));
            return res;
        }), retryCount);
    }

    public CompletableFuture<AskUserRes> askUser(AskUserReq askUserReq) {
        return ParallelUtil.retry(() -> AsyncHttpUtil.post(algConfig.getHost() + "/ask_user", askUserReq, new TypeReference<AskUserRes>() {
        }).thenApply(res -> {
            res.setUsages(LLMUsages.of(JSON.toJSONString(res.getUsages())));
            return res;
        }), retryCount);
    }

    public CompletableFuture<RankRes> rank(RankReq rankReq) {
        return ParallelUtil.retry(() -> AsyncHttpUtil.post(algConfig.getHost() + "/rank", rankReq, new TypeReference<RankRes>() {
        }), retryCount);
    }

    public CompletableFuture<RelevantScoreRes> relevantScore(RelevantScoreReq relevantScoreReq) {
        return ParallelUtil.retry(() -> AsyncHttpUtil.post(algConfig.getHost() + "/relevant_score", relevantScoreReq, new TypeReference<RelevantScoreRes>() {
        }).thenApply(res -> {
            res.setUsages(LLMUsages.of(JSON.toJSONString(res.getUsages())));
            return res;
        }), retryCount);
    }

    public CompletableFuture<TextExtractXRes> textExtractX(TextExtractXReq req) {
        return ParallelUtil.retry(() -> AsyncHttpUtil.post(algConfig.getHost() + "/ner/text_extract_x", req, new TypeReference<TextExtractXRes>() {
        }).thenApply(res -> {
            res.setUsages(LLMUsages.of(JSON.toJSONString(res.getUsages())));
            return res;
        }), retryCount);
    }
}
