package com.shulex.qabot.service.product.aiagent;

import com.alibaba.fastjson.JSONObject;
import com.shulex.qabot.client.dto.esentity.ProductEsEntity;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public abstract class AiAgentTool {
    public static final Map<String, AiAgentTool> toolName2Bean = new HashMap<>();

    protected final String toolName;

    public AiAgentTool(String toolName) {
        this.toolName = toolName;
        toolName2Bean.put(this.toolName, this);
    }

    public static AiAgentTool getToolByName(String toolName) {
        return toolName2Bean.get(toolName);
    }

    public abstract List<ProductEsEntity> findProduct(JSONObject toolInput);
}
