<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <include resource="org/springframework/boot/logging/logback/defaults.xml"/>
    <springProperty scope="context" name="springAppName" source="spring.application.name"/>
    <springProfile name="dev">
        <appender name="console" class="ch.qos.logback.core.ConsoleAppender">
            <encoder>
                <pattern>%boldYellow(%d{yyyy-MM-dd'T'HH:mm:ss.SSS}) %highlight(%-5level) %cyan([%thread,%logger{15},%X{traceId},%X{spanId}]) %msg%n
                </pattern>
                <charset>utf8</charset>
            </encoder>
        </appender>
        <root level="INFO">
            <appender-ref ref="console"/>
        </root>
    </springProfile>
    <springProfile name="test">
        <property name="LOG_FILE" value="logs/${springAppName}/${hostName}"/>
        <appender name="file" class="ch.qos.logback.core.rolling.RollingFileAppender">
            <file>${LOG_FILE}.json</file>
            <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
                <fileNamePattern>${LOG_FILE}.log.%d{yyyy-MM-dd}.%i.gz</fileNamePattern>
                <maxHistory>30</maxHistory>
                <maxFileSize>1024MB</maxFileSize>
                <totalSizeCap>3GB</totalSizeCap>
            </rollingPolicy>
            <encoder class="net.logstash.logback.encoder.LoggingEventCompositeJsonEncoder">
                <providers>
                    <timestamp>
                        <timeZone>UTC</timeZone>
                    </timestamp>
                    <pattern>
                        <pattern>{"severity":"%level","service":"${springAppName:-}","trace":"%X{traceId:-}","span":"%X{spanId:-}","parent":"%X{parentSpanId:-}","exportable":"%X{spanExport:-}","pid":"${PID:-}","thread":"%thread","class":"%logger{40}","rest":"%message","error":"%exception"}</pattern>
                    </pattern>
                </providers>
            </encoder>
        </appender>
        <root level="INFO">
            <appender-ref ref="file"/>
        </root>
    </springProfile>
    <springProfile name="staging">
        <property name="LOG_FILE" value="logs/${springAppName}/${hostName}"/>
        <appender name="file" class="ch.qos.logback.core.rolling.RollingFileAppender">
            <file>${LOG_FILE}.json</file>
            <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
                <fileNamePattern>${LOG_FILE}.log.%d{yyyy-MM-dd}.%i.gz</fileNamePattern>
                <maxHistory>30</maxHistory>
                <maxFileSize>1024MB</maxFileSize>
                <totalSizeCap>3GB</totalSizeCap>
            </rollingPolicy>
            <encoder class="net.logstash.logback.encoder.LoggingEventCompositeJsonEncoder">
                <providers>
                    <timestamp>
                        <timeZone>UTC</timeZone>
                    </timestamp>
                    <pattern>
                        <pattern>{"severity":"%level","service":"${springAppName:-}","trace":"%X{traceId:-}","span":"%X{spanId:-}","parent":"%X{parentSpanId:-}","exportable":"%X{spanExport:-}","pid":"${PID:-}","thread":"%thread","class":"%logger{40}","rest":"%message","error":"%exception"}</pattern>
                    </pattern>
                </providers>
            </encoder>
        </appender>
        <root level="INFO">
            <appender-ref ref="file"/>
        </root>
    </springProfile>
    <springProfile name="prod">
        <property name="LOG_FILE" value="logs/${springAppName}/${hostName}"/>
        <appender name="file" class="ch.qos.logback.core.rolling.RollingFileAppender">
            <file>${LOG_FILE}.json</file>
            <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
                <fileNamePattern>${LOG_FILE}.log.%d{yyyy-MM-dd}.%i.gz</fileNamePattern>
                <maxHistory>30</maxHistory>
                <maxFileSize>1024MB</maxFileSize>
                <totalSizeCap>3GB</totalSizeCap>
            </rollingPolicy>
            <encoder class="net.logstash.logback.encoder.LoggingEventCompositeJsonEncoder">
                <providers>
                    <timestamp>
                        <timeZone>UTC</timeZone>
                    </timestamp>
                    <pattern>
                        <pattern>{"severity":"%level","service":"${springAppName:-}","trace":"%X{traceId:-}","span":"%X{spanId:-}","parent":"%X{parentSpanId:-}","exportable":"%X{spanExport:-}","pid":"${PID:-}","thread":"%thread","class":"%logger{40}","rest":"%message","error":"%exception"}</pattern>
                    </pattern>
                </providers>
            </encoder>
        </appender>
        <root level="INFO">
            <appender-ref ref="file"/>
        </root>
    </springProfile>
</configuration>
