stages:
  - build
  - deploy

.build:
  stage: build
  cache:
    key: qa-bot
    paths:
      - ${CI_PROJECT_DIR}/.m2
  script:
    - echo "$ID_RSA" >  ~/.ssh/id_rsa && chmod 600 -R ~/.ssh/
    - export JAVA_HOME=$JAVA_8_HOME; export JDK_HOME=$JDK_8_HOME; export JRE_HOME=$JRE_8_HOME; mvn -U -s "$MVN_SETTINGS_XML" clean package -DskipTests=true -Dmaven.repo.local=${CI_PROJECT_DIR}/.m2/repository -pl ${MODULE} -am
    - |
      for DST_ITEM in $DST
      do
        ssh ${DST_ITEM} -o StrictHostKeyChecking=no "ls -l"
        ssh ${DST_ITEM} -o StrictHostKeyChecking=no "mkdir -p /app/${JAR}/"
        scp target/${JAR}.jar ${DST_ITEM}:/app/${JAR}/
        scp ./Dockerfile ${DST_ITEM}:/app/${JAR}/
      done

.deploy:
  stage: deploy
  script:
    - echo "$ID_RSA" >  ~/.ssh/id_rsa && chmod 600 -R ~/.ssh/
    - |
      ssh -i ~/.ssh/id_rsa_abc -o StrictHostKeyChecking=no ${DST} << EOF
        cd /app/${JAR}
        sudo docker build ./ -t ${JAR}:${TAG} --build-arg JAR_NAME=${JAR}
        sudo docker stop ${JAR}
        sudo docker rm ${JAR}
        sudo docker run -d --restart unless-stopped --memory ${CONTAINER_MEMORY} --memory-swap ${CONTAINER_MEMORY_SWAP} -p ${PORT}:8080 -p ${MPORT}:8081 -p ${XPORT}:${XPORT} --hostname ${HOST} --env PROFILE=${ENV} --env OPT='$OPT' --env OPT_EXT='-Dxxl.job.executor.ip=${HOST} -Dxxl.job.executor.port=${XPORT} -Dspring.cloud.nacos.discovery.ip=${HOST} -Dspring.cloud.nacos.discovery.port=${PORT} -Dspring.cloud.nacos.discovery.metadata.${TAG}=Go' -v /app/logs:/app/logs --name ${JAR} ${JAR}:${TAG}
      EOF

test-qa-bot-build:
  tags:
    - test
  extends:
    - .build
  variables:
    MODULE: qa-bot-app
    JAR: qa-bot
    DST: ubuntu@**********
  rules:
    - if: $CI_COMMIT_REF_NAME =~ /^test/

test-qa-bot-deploy-10-7-4-208:
  tags:
    - test
  extends:
    - .deploy
  variables:
    DST: ubuntu@**********
    JAR: qa-bot
    TAG: ${CI_COMMIT_REF_NAME}
    HOST: **********
    ENV: test
    OPT: -Xmx1536m -Xms1536m -Xmn768m
    CONTAINER_MEMORY: 2048m
    CONTAINER_MEMORY_SWAP: 2048m
    PORT: 13000
    MPORT: 13002
    XPORT: 13001
  rules:
    - if: $CI_COMMIT_REF_NAME =~ /^test/


prod-qa-bot-build:
  tags:
    - prod
  extends:
    - .build
  variables:
    MODULE: qa-bot-app
    JAR: qa-bot
    DST: ubuntu@************ ubuntu@************
  rules:
    - if: $CI_COMMIT_REF_NAME =~ /main/

prod-qa-bot-deploy-10-161-3-117:
  tags:
    - prod
  extends:
    - .deploy
  variables:
    DST: ubuntu@************
    JAR: qa-bot
    TAG: ${CI_COMMIT_REF_NAME}
    HOST: ************
    ENV: prod
    OPT: -Xmx1800m -Xms1800m -Xmn900m
    CONTAINER_MEMORY: 2200m
    CONTAINER_MEMORY_SWAP: 3200m
    PORT: 13000
    MPORT: 13002
    XPORT: 13001
  rules:
    - if: $CI_COMMIT_REF_NAME =~ /main/
      when: manual
    - when: never

prod-qa-bot-deploy-10-161-3-225:
  tags:
    - prod
  extends:
    - .deploy
  variables:
    DST: ubuntu@************
    JAR: qa-bot
    TAG: ${CI_COMMIT_REF_NAME}
    HOST: ************
    ENV: prod
    OPT: -Xmx1800m -Xms1800m -Xmn900m
    CONTAINER_MEMORY: 2200m
    CONTAINER_MEMORY_SWAP: 3200m
    PORT: 13000
    MPORT: 13002
    XPORT: 13001
  rules:
    - if: $CI_COMMIT_REF_NAME =~ /main/
      when: manual
    - when: never
