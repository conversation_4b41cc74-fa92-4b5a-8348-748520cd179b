package com.shulex.qabot.alg.dto;

import com.shulex.gpt.apisdk.dto.Message;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;
import java.util.Map;

@Data
@Accessors(chain = true)
public class IntentIdentifyReq {
    Long tenant_id;
    Long bot_id;
    String chat_id;
    String client;
    List<List<String>> default_intents;
    List<List<String>> custom_intents;
    List<Intent> custom_intents_obj;
    String user_en_query;
    String model;
    String touch_point;
    List<Message> messages;
    String title;
    String content;
    String product;
    List<Example> examples;
    String tenant_version;
    Boolean is_multi_intent;
    Boolean is_tagging;
    Map<String, List<Object>> metadata;
    Boolean add_other_category;
    String tags_description;

    @Data
    @Accessors(chain = true)
    public static class Example {
        String category;
        List<Sample> samples;
    }

    @Data
    @Accessors(chain = true)
    public static class Sample {
        String text;
        Double score;
    }

    @Data
    @Accessors(chain = true)
    public static class Intent {
        String intent_name;
        String intent_description;
        String intent_ai_name;
    }
}
