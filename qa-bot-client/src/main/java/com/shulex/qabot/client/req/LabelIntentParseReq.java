package com.shulex.qabot.client.req;

import com.shulex.gpt.apisdk.dto.Message;
import com.shulex.gpt.apisdk.enums.BotLanguageEnum;
import com.shulex.qabot.client.dto.LabelExampleDTO;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.ArrayList;
import java.util.List;

@Data
@Accessors(chain = true)
public class LabelIntentParseReq {
    Long treeId;
    String chatId;
    Long tenantId;
    String tenantVersion;
    Long projectId;
    String clientType;
    List<List<String>> customIntents;
    String userEnQuery; //调用summary接口会返回
    String summaryModel;
    String model;
    String touchPoint;
    String title;
    String content;
    List<Message> messages = new ArrayList<>();
    BotLanguageEnum userLanguage = BotLanguageEnum.ENGLISH;
    BotLanguageEnum knowledgeLanguage = BotLanguageEnum.ENGLISH;
    List<LabelExampleDTO> exampleDTOS = new ArrayList<>();
    Boolean isTagging = true;
    Boolean isMultiIntent = false;
    private String product;
    Boolean addOtherCategory;
    String tagsDescription;
}
