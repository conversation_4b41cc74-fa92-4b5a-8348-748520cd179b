package com.shulex.qabot.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.lang.reflect.Array;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Data
@Component
@ConfigurationProperties(prefix = "label")
public class LabelConfig {
    Boolean isOld = true;
    String oldTagPath = "/v2/intent_identify";
    String newTagPath = "/tag_identify";
    List<Long> summaryConciseTreeId = Arrays.asList(168L);
    List<Long> skipSummaryTreeId = new ArrayList<>();
}
