package com.shulex.qabot.client.dto.esentity;

import cn.hutool.core.bean.BeanUtil;
import com.shulex.qabot.client.dto.mysqlentity.LabelChatRelation;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;

@Data
@Accessors(chain = true)
@NoArgsConstructor
public class LabelTagEsEntity {
    public static final String indexName = "label_tag";
    private String chatId;
    private String messageId;
    private Long taskId;
    private List<Long> tagIdPath;
    private List<String> tagNamePath;
    private Integer level;
    private String parentTagName;
    private Long parentTagId;
    private Long tagId;
    private String tagName;
    private Long tenantId;
    private Long projectId;
    private Long treeId;
    private String treeName;
    private String ticketId;
    private String platform;
    private String ticketSource;
    private String title;
    private String content;
    private Boolean isAiReply;
    private Boolean debug;
    private String aiContent;
    private LocalDateTime ticketCreatedAt;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    private List<List<Long>> multiTagIdPath;
    private List<List<String>> multiTagNamePath;
    private Boolean isMultiTag;

    public LabelTagEsEntity(LabelChatRelation labelChatRelation) {
        BeanUtil.copyProperties(labelChatRelation, this);
    }
    public String generateId() {
        return String.format("%s_%s_%s_%s_%s_%s_%s_%s_%s", tenantId, projectId, treeId, ticketId, messageId, ticketSource, platform, taskId, chatId);
    }
}
