package com.shulex.qabot.client.dto.mysqlentity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.shulex.gpt.apisdk.enums.ProjectTypeEnum;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@TableName("project")
public class Project {
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    private String name;
    private ProjectTypeEnum type;
    /**
     * 文本embedding模型
     * AZURE_OPENAI  -> AZURE_OPENAI,ALL_MPNET_BASE_V2
     */
    private String textEmbedding;
    /**
     * 时区
     */
    private String timezone;
    private VectorStoreTypeEnum vectorStore;
    private String metadata;
    private Long tenantId;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
}
