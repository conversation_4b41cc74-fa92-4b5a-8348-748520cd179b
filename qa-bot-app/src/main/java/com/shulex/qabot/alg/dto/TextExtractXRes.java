package com.shulex.qabot.alg.dto;

import com.shulex.gpt.apisdk.dto.LLMUsages;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
public class TextExtractXRes {
    Result result;
    LLMUsages usages;

    @Data
    @Accessors(chain = true)
    public static class Result {
        List<String> encountered_issue;
        List<String> product_name;
        List<String> order_number;
        List<String> common_entity;
    }
}
