package com.shulex.qabot.client.req;

import com.shulex.gpt.apisdk.dto.AiReplyPromptInfo;
import com.shulex.gpt.apisdk.dto.Message;
import com.shulex.gpt.apisdk.dto.MixedQuery;
import com.shulex.gpt.apisdk.enums.BotLanguageEnum;
import com.shulex.gpt.apisdk.enums.LLMTypeEnum;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;
import java.util.Map;
import java.util.Set;


@Data
@Accessors(chain = true)
public class DocumentQaReq {
    String chatId;
    Long tenantId;
    Long projectId;
    MixedQuery mixedQuery;
    BotLanguageEnum language;
    List<Message> historyMessages;
    String brandName;
    String roleDescription;
    String intent;
    Integer wordsLimit;
    LLMTypeEnum modelType;
    Map<Long, Set<Long>> kuLabelDimensionId2ValueIds;
    AiReplyPromptInfo aiReplyPromptInfo;
}
