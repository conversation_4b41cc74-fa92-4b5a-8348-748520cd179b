package com.shulex.qabot.es;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Pair;
import com.alibaba.fastjson.JSON;
import com.shulex.qabot.client.dto.esentity.EsEmbeddingField;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.action.DocWriteRequest;
import org.elasticsearch.action.bulk.BulkItemResponse;
import org.elasticsearch.action.bulk.BulkRequest;
import org.elasticsearch.action.bulk.BulkResponse;
import org.elasticsearch.action.delete.DeleteRequest;
import org.elasticsearch.action.get.*;
import org.elasticsearch.action.index.IndexRequest;
import org.elasticsearch.action.support.WriteRequest;
import org.elasticsearch.action.update.UpdateRequest;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.common.xcontent.XContentType;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.reindex.BulkByScrollResponse;
import org.elasticsearch.index.reindex.DeleteByQueryRequest;
import org.elasticsearch.index.reindex.UpdateByQueryRequest;
import org.elasticsearch.script.Script;
import org.elasticsearch.search.fetch.subphase.FetchSourceContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.lang.reflect.Field;
import java.util.*;
import java.util.stream.Collectors;

import static java.util.Collections.emptyMap;

@Service
@Slf4j
public class EsService {
    @Autowired
    RestHighLevelClient restHighLevelClient;

    public void insert(String index, Map<String, String> id2JsonData) {
        if (ObjectUtils.isEmpty(index) || ObjectUtils.isEmpty(id2JsonData)) {
            return;
        }
        List<IndexRequest> indexRequests = new ArrayList<>();
        id2JsonData.forEach((id, jsonData) -> {
            IndexRequest indexRequest = new IndexRequest(index);
            indexRequest.id(id);
            indexRequest.source(jsonData, XContentType.JSON);
            indexRequests.add(indexRequest);
        });
        BulkResponse bulkResponse = bulk(indexRequests);
        if (bulkResponse != null) {
            StringBuilder failMsg = new StringBuilder();
            BulkItemResponse[] bulkResponseItems = bulkResponse.getItems();
            for (BulkItemResponse bulkResponseItem : bulkResponseItems) {
                String failureMessage = bulkResponseItem.getFailureMessage();
                if (StringUtils.hasLength(failureMessage)) {
                    failMsg.append(failureMessage);
                }
            }
            String failMsgString = failMsg.toString();
            if (StringUtils.hasLength(failMsgString)) {
                throw new RuntimeException(failMsgString);
            }
        }
    }

    @SneakyThrows
    public Map<String, Boolean> exist(String index, List<String> ids) {
        if (CollectionUtil.isEmpty(ids)) {
            return emptyMap();
        }
        MultiGetRequest multiGetRequest = new MultiGetRequest();
        for (String id : ids) {
            MultiGetRequest.Item item = new MultiGetRequest.Item(index, id).fetchSourceContext(FetchSourceContext.DO_NOT_FETCH_SOURCE);
            multiGetRequest.add(item);
        }
        MultiGetResponse multiGetItemResponses = restHighLevelClient.mget(multiGetRequest, RequestOptions.DEFAULT);
        MultiGetItemResponse[] responses = multiGetItemResponses.getResponses();
        Map<String, Boolean> result = new HashMap<>();
        for (int i = 0; i < ids.size(); i++) {
            String id = ids.get(i);
            MultiGetItemResponse response = responses[i];
            boolean exist = response.getResponse().isExists();
            result.put(id, exist);
        }
        return result;
    }

    @SneakyThrows
    public <T> T getById(String index, String id, Class<T> resClass, List<String> excludeFields) {
        GetRequest getRequest = new GetRequest(index, id);
        if (CollectionUtil.isNotEmpty(excludeFields)) {
            getRequest.fetchSourceContext(new FetchSourceContext(true, null, excludeFields.toArray(new String[0])));
        }
        GetResponse getResponse = restHighLevelClient.get(getRequest, RequestOptions.DEFAULT);
        if (getResponse.isExists()) {
            return JSON.parseObject(getResponse.getSourceAsString(), resClass);
        }
        return null;
    }

    @SneakyThrows
    public <T> Map<String, T> getByIds(String index, List<String> ids, Class<T> resClass, List<String> excludeFields) {
        if (CollectionUtil.isEmpty(ids)) {
            return emptyMap();
        }
        MultiGetRequest multiGetRequest = new MultiGetRequest();
        for (String id : ids) {
            MultiGetRequest.Item item = new MultiGetRequest.Item(index, id).fetchSourceContext(new FetchSourceContext(true, null, excludeFields.toArray(new String[0])));
            multiGetRequest.add(item);
        }
        MultiGetResponse multiGetItemResponses = restHighLevelClient.mget(multiGetRequest, RequestOptions.DEFAULT);
        MultiGetItemResponse[] responses = multiGetItemResponses.getResponses();
        Map<String, T> result = new HashMap<>();
        for (int i = 0; i < ids.size(); i++) {
            String id = ids.get(i);
            MultiGetItemResponse response = responses[i];
            T t = JSON.parseObject(response.getResponse().getSourceAsString(), resClass);
            result.put(id, t);
        }
        return result;
    }

    public void update(String index, Map<String, String> id2updateJsonData, boolean ignoreMissingDocException) {
        if (ObjectUtils.isEmpty(index) || ObjectUtils.isEmpty(id2updateJsonData)) {
            return;
        }
        List<UpdateRequest> updateRequests = new ArrayList<>();
        id2updateJsonData.forEach((id, updateJsonData) -> {
            UpdateRequest updateRequest = new UpdateRequest(index, id);
            updateRequest.doc(updateJsonData, XContentType.JSON);
            updateRequests.add(updateRequest);
        });
        BulkResponse bulkResponse = bulk(updateRequests);
        if (bulkResponse != null) {
            StringBuilder failMsg = new StringBuilder();
            BulkItemResponse[] bulkResponseItems = bulkResponse.getItems();
            for (BulkItemResponse bulkResponseItem : bulkResponseItems) {
                String failureMessage = bulkResponseItem.getFailureMessage();
                if (StringUtils.hasLength(failureMessage)) {
                    if (ignoreMissingDocException && failureMessage.contains("type=document_missing_exception")) {
                        log.warn("ignore missing doc, {}", failureMessage);
                    } else {
                        failMsg.append(failureMessage);
                    }
                }
            }
            String failMsgString = failMsg.toString();
            if (StringUtils.hasLength(failMsgString)) {
                throw new RuntimeException(failMsgString);
            }
        }
    }

    @SneakyThrows
    public void updateByQuery(String index, List<Pair<String, Object>> termQueryFieldAndValue, String script) {
        if (CollectionUtil.isEmpty(termQueryFieldAndValue) || !StringUtils.hasLength(script)) {
            return;
        }
        UpdateByQueryRequest updateByQueryRequest = new UpdateByQueryRequest(index);
        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
        EsSearchService.addTermQueryToBuilder(termQueryFieldAndValue, boolQueryBuilder);
        updateByQueryRequest.setQuery(boolQueryBuilder);
        updateByQueryRequest.setRefresh(true);
        updateByQueryRequest.setScript(new Script(script));
        BulkByScrollResponse bulkByScrollResponse = restHighLevelClient.updateByQuery(updateByQueryRequest, RequestOptions.DEFAULT);
    }

    public void delete(String index, List<String> ids, boolean ignoreMissingDocException) {
        if (ObjectUtils.isEmpty(index) || ObjectUtils.isEmpty(ids)) {
            return;
        }
        List<DeleteRequest> deleteRequests = new ArrayList<>();
        ids.forEach(id -> {
            DeleteRequest deleteRequest = new DeleteRequest(index, id);
            deleteRequests.add(deleteRequest);
        });
        BulkResponse bulkResponse = bulk(deleteRequests);
        if (bulkResponse != null) {
            StringBuilder failMsg = new StringBuilder();
            BulkItemResponse[] bulkResponseItems = bulkResponse.getItems();
            for (BulkItemResponse bulkResponseItem : bulkResponseItems) {
                String failureMessage = bulkResponseItem.getFailureMessage();
                if (StringUtils.hasLength(failureMessage)) {
                    if (ignoreMissingDocException && failureMessage.contains("type=document_missing_exception")) {
                        log.warn("ignore missing doc, {}", failureMessage);
                    } else {
                        failMsg.append(failureMessage);
                    }
                }
            }
            String failMsgString = failMsg.toString();
            if (StringUtils.hasLength(failMsgString)) {
                throw new RuntimeException(failMsgString);
            }
        }
    }

    @SneakyThrows
    public void deleteByQuery(String index, List<Pair<String, Object>> termQueryFieldAndValue) {
        if (CollectionUtil.isEmpty(termQueryFieldAndValue)) {
            return;
        }
        DeleteByQueryRequest deleteByQueryRequest = new DeleteByQueryRequest(index);
        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
        EsSearchService.addTermQueryToBuilder(termQueryFieldAndValue, boolQueryBuilder);
        deleteByQueryRequest.setQuery(boolQueryBuilder);
        BulkByScrollResponse bulkByScrollResponse = restHighLevelClient.deleteByQuery(deleteByQueryRequest, RequestOptions.DEFAULT);
    }

    @SneakyThrows
    private BulkResponse bulk(List<? extends DocWriteRequest<?>> docWriteRequests) {
        if (ObjectUtils.isEmpty(docWriteRequests)) {
            return null;
        }
        BulkRequest bulkRequest = new BulkRequest();
        docWriteRequests.forEach(bulkRequest::add);
        bulkRequest.setRefreshPolicy(WriteRequest.RefreshPolicy.IMMEDIATE);
        BulkResponse bulkResponse = restHighLevelClient.bulk(bulkRequest, RequestOptions.DEFAULT);
        return bulkResponse;
    }

    public static List<String> getEmbeddingFieldNames(Class<?> esEntityClass) {
        List<Field> embeddingFields = new ArrayList<>();
        Field[] declaredFields = esEntityClass.getDeclaredFields();
        for (Field declaredField : declaredFields) {
            EsEmbeddingField annotation = declaredField.getAnnotation(EsEmbeddingField.class);
            if (annotation != null) {
                embeddingFields.add(declaredField);
            }
        }
        return embeddingFields.stream().map(Field::getName).collect(Collectors.toList());
    }
}
