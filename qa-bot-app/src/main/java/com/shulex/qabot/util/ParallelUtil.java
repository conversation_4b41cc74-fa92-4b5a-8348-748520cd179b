package com.shulex.qabot.util;

import cn.hutool.core.collection.CollectionUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.*;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;

@Slf4j
public class ParallelUtil {
    public static <T> void parallelCall(ExecutorService threadPool, boolean ignoreError, List<T> dataList, Consumer<T> dataConsumer) {
        if (CollectionUtil.isEmpty(dataList) || dataConsumer == null) {
            return;
        }
        List<CompletableFuture<Void>> futures = dataList.stream().map(data -> CompletableFuture.runAsync(() -> dataConsumer.accept(data), threadPool)).collect(Collectors.toList());
        for (CompletableFuture<Void> future : futures) {
            try {
                future.get();
            } catch (InterruptedException | ExecutionException e) {
                if (ignoreError) {
                    log.warn("ignore error: ", e);
                    continue;
                }
                throw new RuntimeException(e);
            }
        }
    }

    public static <T> void parallelCall(int parallelCount, boolean ignoreError, List<T> dataList, Consumer<T> dataConsumer) {
        if (CollectionUtil.isEmpty(dataList) || dataConsumer == null) {
            return;
        }
        parallelCount = Math.min(dataList.size(), parallelCount);
        ExecutorService threadPool = Executors.newFixedThreadPool(parallelCount);
        try {
            parallelCall(threadPool, ignoreError, dataList, dataConsumer);
        } finally {
            threadPool.shutdown();
        }
    }

    public static <T> void parallelAsyncCall(int parallelCount, boolean ignoreError, List<T> dataList, Function<T, CompletableFuture> dataAsyncConsumer) {
        if (CollectionUtil.isEmpty(dataList) || dataAsyncConsumer == null) {
            return;
        }
        parallelCount = Math.min(dataList.size(), parallelCount);
        List<CompletableFuture> futures = new ArrayList<>();
        for (T data : dataList) {
            CompletableFuture future = dataAsyncConsumer.apply(data);
            futures.add(future);
            if (futures.size() >= parallelCount) {
                for (CompletableFuture completableFuture : futures) {
                    try {
                        completableFuture.get();
                    } catch (InterruptedException | ExecutionException e) {
                        if (ignoreError) {
                            log.warn("ignore error: ", e);
                            continue;
                        }
                        throw new RuntimeException(e);
                    }
                }
                futures.clear();
            }
        }
        if (!futures.isEmpty()) {
            for (CompletableFuture completableFuture : futures) {
                try {
                    completableFuture.get();
                } catch (InterruptedException | ExecutionException e) {
                    if (ignoreError) {
                        log.warn("ignore error: ", e);
                        continue;
                    }
                    throw new RuntimeException(e);
                }
            }
            futures.clear();
        }
    }

    public static <T> CompletableFuture<T> retry(Supplier<CompletableFuture<T>> supplier, int retryCount) {
        CompletableFuture<T> future = supplier.get();
        for (int i = 0; i < retryCount; i++) {
            future = future
                    .exceptionally(throwable -> null)
                    .thenCompose(res -> res == null ? supplier.get() : CompletableFuture.completedFuture(res));
        }
        return future;
    }
}
