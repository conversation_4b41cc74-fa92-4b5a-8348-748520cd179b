package com.shulex.qabot.util;

import com.baomidou.mybatisplus.core.conditions.AbstractLambdaWrapper;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;

public class MybatisPlusColumnResolver<T> extends AbstractLambdaWrapper<T, MybatisPlusColumnResolver<T>> {
    private static final MybatisPlusColumnResolver resolver = new MybatisPlusColumnResolver();

    @Override
    protected MybatisPlusColumnResolver<T> instance() {
        return null;
    }

    public static <K> String getColumnString(SFunction<K, ?> column) {
        return resolver.columnToString(column);
    }
}
