package com.shulex.qabot.mysql.repository;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.shulex.gpt.apisdk.enums.KuTypeEnum;
import com.shulex.qabot.client.dto.mysqlentity.KuLabelRelation;
import com.shulex.qabot.mysql.mapper.KuLabelRelationMapper;
import com.shulex.qabot.util.MybatisPlusColumnResolver;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static java.lang.String.format;

@Repository
public class KuLabelRelationRepository extends ServiceImpl<KuLabelRelationMapper, KuLabelRelation> {
    public List<KuLabelRelation> getRelations(long tenantId, long projectId, KuTypeEnum kuType, List<Long> kuIds, List<Long> kuLabelValueIds) {
        return lambdaQuery()
                .eq(KuLabelRelation::getTenantId, tenantId)
                .eq(KuLabelRelation::getProjectId, projectId)
                .eq(KuLabelRelation::getKuType, kuType)
                .in(CollectionUtil.isNotEmpty(kuIds), KuLabelRelation::getKuId, kuIds)
                .in(CollectionUtil.isNotEmpty(kuLabelValueIds), KuLabelRelation::getKuLabelValueId, kuLabelValueIds)
                .list();
    }
}
