package com.shulex.qabot.client.req;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Pair;
import com.shulex.gpt.apisdk.dto.AiReplyPromptInfo;
import com.shulex.gpt.apisdk.dto.MixedQuery;
import com.shulex.gpt.apisdk.enums.BotLanguageEnum;
import com.shulex.gpt.apisdk.enums.LLMTypeEnum;
import com.shulex.qabot.client.dto.AiAgentMessage;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
public class RecommendProductReq {
    Long tenantId;
    String clientType;
    Long projectId;
    String touchPoint;
    String botName;
    String chatId;
    String intent;
    MixedQuery mixedQuery;
    List<AiAgentMessage> historyAiAgentMessages;
    BotLanguageEnum language;
    Integer turn;
    ProductSearchParam productSearchParam;
    Integer wordsLimit;
    LLMTypeEnum modelType;
    String brandName;
    String roleDescription;
    AiReplyPromptInfo aiReplyPromptInfo;

    @Data
    @Accessors(chain = true)
    public static class ProductSearchParam {
        List<String> product;
        List<String> requirement;
        String summary;
        List<String> subqueries;
        List<Double> price_range;

        public Pair<Double, Double> getMinAndMaxPrice() {
            if (CollectionUtil.isEmpty(price_range) || price_range.size() != 2) {
                return Pair.of(null, null);
            }
            return Pair.of(price_range.get(0), price_range.get(1));
        }
    }
}
