package com.shulex.qabot.feign.gpt;

import com.shulex.gpt.apisdk.IIntentApi;
import com.shulex.gpt.apisdk.IInternalApi;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Profile;

@FeignClient(name = "shulex-gpt", contextId = "IInternalApiFeignClient")
@Profile({"test", "prod"})
public interface IInternalApiFeignClient extends IInternalApi {
}

@FeignClient(name = "shulex-gpt", contextId = "IInternalApiFeignClientDev", url = "http://10.7.4.9:8008")
@Profile({"dev"})
interface IInternalApiFeignClientDev extends IInternalApi {
}
