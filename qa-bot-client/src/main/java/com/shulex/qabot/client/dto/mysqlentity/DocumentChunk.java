package com.shulex.qabot.client.dto.mysqlentity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;

@Data
@Accessors(chain = true)
public class DocumentChunk {
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    private Boolean enable;
    private Boolean indexed;
    private Long tenantId;
    private Long projectId;
    private Long documentId;
    private Long resourceId;
    private Integer chunkSeq;
    private String content;
    private List<Integer> offset;
}
