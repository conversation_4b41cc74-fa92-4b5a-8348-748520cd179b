package com.shulex.qabot.mysql.repository;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.shulex.qabot.client.dto.mysqlentity.KuLabelValue;
import com.shulex.qabot.mysql.mapper.KuLabelValueMapper;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;

import static java.lang.String.format;

@Repository
public class KuLabelValueRepository extends ServiceImpl<KuLabelValueMapper, KuLabelValue> {
    public List<KuLabelValue> getKuLabelValuesByName(long tenantId, long projectId, List<String> names) {
        if (CollectionUtil.isEmpty(names)) {
            return new ArrayList<>();
        }
        return lambdaQuery()
                .eq(KuLabelValue::getTenantId, tenantId)
                .eq(KuLabelValue::getProjectId, projectId)
                .in(KuLabelValue::getName, names)
                .list();
    }
}
