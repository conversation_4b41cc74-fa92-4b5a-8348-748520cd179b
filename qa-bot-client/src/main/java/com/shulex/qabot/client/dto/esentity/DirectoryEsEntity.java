package com.shulex.qabot.client.dto.esentity;

import cn.hutool.core.bean.BeanUtil;
import com.shulex.gpt.apisdk.enums.DirectoryTypeEnum;
import com.shulex.qabot.client.dto.mysqlentity.Directory;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

@Data
@Accessors(chain = true)
@NoArgsConstructor
public class DirectoryEsEntity {
    public static final String indexName = "qa-directory";

    private Long id;
    private String name;
    private DirectoryTypeEnum type;
    private String metadata;
    private Long tenantId;
    private Long projectId;
    private Long parentId;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;

    public DirectoryEsEntity(Directory directory) {
        BeanUtil.copyProperties(directory, this);
    }
}
