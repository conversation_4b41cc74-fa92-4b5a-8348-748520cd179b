package com.shulex.qabot.service.product;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;

import com.alibaba.otter.canal.protocol.CanalEntry;
import com.shulex.gpt.apisdk.IKBApi;
import com.shulex.gpt.apisdk.dto.GetKuProductResponse;
import com.shulex.gpt.apisdk.dto.ProductGetReq;
import com.shulex.gpt.apisdk.enums.KuTypeEnum;
import com.shulex.qabot.client.dto.esentity.ProductChunkEsEntity;
import com.shulex.qabot.client.dto.esentity.ProductEsEntity;
import com.shulex.qabot.client.dto.mysqlentity.ProductDoc;
import com.shulex.qabot.es.EsSearchService;
import com.shulex.qabot.es.EsService;
import com.shulex.qabot.client.dto.mysqlentity.KuProduct;
import com.shulex.qabot.client.dto.mysqlentity.ProductChunk;
import com.shulex.qabot.mysql.repository.KuProductRepository;
import com.shulex.qabot.mysql.repository.ProductChunkRepository;
import com.shulex.qabot.mysql.repository.ProductDocRepository;
import com.shulex.qabot.service.BedrockService;
import com.shulex.qabot.service.KuLabelRelationService;
import com.shulex.qabot.util.ParallelUtil;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.MultiValueMap;
import org.springframework.util.StringUtils;
import org.springframework.web.util.UriComponents;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

import static java.lang.String.format;

@Service
@Slf4j
public class ProductService {
    @Autowired
    BedrockService bedrockService;
    @Autowired
    EsService esService;
    @Autowired
    EsSearchService esSearchService;
    @Autowired
    KafkaTemplate<String, byte[]> kafkaTemplate;
    @Autowired
    KuProductRepository kuProductRepository;
    @Autowired
    ProductChunkRepository productChunkRepository;
    @Autowired
    IKBApi kbApi;
    @Autowired
    KuLabelRelationService kuLabelRelationService;
    @Autowired
    ProductDocRepository productDocRepository;

    @SneakyThrows
    public void insertKuProductToEs(List<KuProduct> kuProducts) {
        if (CollectionUtil.isEmpty(kuProducts)) {
            return;
        }
        List<ProductEsEntity> productEsEntities = kuProducts.stream().map(ProductEsEntity::new).collect(Collectors.toList());
        setProductTags(productEsEntities);
        // generate embedding
        embeddingKuProducts(productEsEntities);
        // save to es
        Map<String, String> id2JsonData = productEsEntities.stream().collect(Collectors.toMap(ProductEsEntity::getId, JSON::toJSONString, (v1, v2) -> v2));
        esService.insert(ProductEsEntity.indexName, id2JsonData);
        // success indexed
        kuProductRepository.indexSucceed(kuProducts.stream().map(e -> e.getId()).collect(Collectors.toSet()));
        log.info("insertKuProductToEs, ids={}", id2JsonData.keySet());
    }

    public void updateKuProductInEs(List<KuProduct> kuProducts, boolean needEmbedding) {
        if (CollectionUtil.isEmpty(kuProducts)) {
            return;
        }
        List<ProductEsEntity> productEsEntities = kuProducts.stream().map(ProductEsEntity::new).collect(Collectors.toList());
        setProductTags(productEsEntities);
        if (needEmbedding) {
            embeddingKuProducts(productEsEntities);
        }
        Map<String, String> id2UpdateJsonData = productEsEntities.stream().collect(Collectors.toMap(ProductEsEntity::getId, JSON::toJSONString, (v1, v2) -> v2));
        esService.update(ProductEsEntity.indexName, id2UpdateJsonData, true);
        log.info("updateKuProductInEs, needEmbedding={}, ids={}", needEmbedding, id2UpdateJsonData.keySet());
    }

    private void embeddingKuProducts(List<ProductEsEntity> productEsEntities) {
        if (CollectionUtil.isEmpty(productEsEntities)) {
            return;
        }
        ParallelUtil.parallelAsyncCall(2, false, productEsEntities, productEsEntity -> {
            String productDetailString = format("%s\n%s\n%s", productEsEntity.getCategory(), productEsEntity.getTitle(), productEsEntity.getDescribe());
            String title = productEsEntity.getTitle() == null ? "" : productEsEntity.getTitle();
            String describe = productEsEntity.getDescribe() == null ? "" : productEsEntity.getDescribe();
            return CompletableFuture.allOf(
                    bedrockService.getCohereEmbeddingAsync(productDetailString).thenAccept(productEsEntity::setProductDetailEmbedding),
                    bedrockService.getCohereEmbeddingAsync(title).thenAccept(productEsEntity::setTitleEmbedding),
                    bedrockService.getCohereEmbeddingAsync(describe).thenAccept(productEsEntity::setDescribeEmbedding)
            );
        });
    }

    public void deleteKuProductInEs(List<KuProduct> kuProducts) {
        if (CollectionUtil.isEmpty(kuProducts)) {
            return;
        }
        List<String> ids = kuProducts.stream().filter(e -> e.getId() != null).map(e -> e.getId().toString()).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(ids)) {
            return;
        }
        esService.delete(ProductEsEntity.indexName, ids, true);
        log.info("deleteKuProductInEs, ids={}", ids);
    }


    public void insertProductChunkToEs(List<ProductChunk> productChunks) {
        if (CollectionUtil.isEmpty(productChunks)) {
            return;
        }
        Set<Long> kuProductIds = productChunks.stream().map(ProductChunk::getKuProductId).collect(Collectors.toSet());
        Map<Long, KuProduct> kuProductMap = kuProductRepository.listByIds(kuProductIds).stream().collect(Collectors.toMap(e -> e.getId(), e -> e, (v1, v2) -> v2));
        List<ProductChunkEsEntity> productChunkEsEntities = productChunks.stream()
                .filter(productChunk -> {
                    KuProduct kuProduct = kuProductMap.get(productChunk.getKuProductId());
                    if (kuProduct == null) {
                        log.warn("insertProductChunkToEs: productChunk[id={}] can't find kuProduct[id={}]", productChunk.getId(), productChunk.getKuProductId());
                    }
                    return true;
                })
                .map(productChunk -> new ProductChunkEsEntity(productChunk, kuProductMap.get(productChunk.getKuProductId()))).collect(Collectors.toList());

        setProductChunkTags(productChunkEsEntities);
        // generate embedding
        embeddingProductChunkEsEntities(productChunkEsEntities);
        // save to es
        Map<String, String> id2JsonData = productChunkEsEntities.stream().collect(Collectors.toMap(ProductChunkEsEntity::getId, JSON::toJSONString, (v1, v2) -> v2));
        esService.insert(ProductChunkEsEntity.indexName, id2JsonData);
        // indexed success
        productChunkRepository.indexSucceed(productChunks.stream().map(e -> e.getId()).collect(Collectors.toSet()));
        log.info("insertProductChunkToEs, ids={}", id2JsonData.keySet());
        List<Long> kuProductIdList = productChunks.stream().map(ProductChunk::getKuProductId).distinct().collect(Collectors.toList());
        for (Long kuProductId : kuProductIdList) {
            kuLabelRelationService.retryUpdateKuLabelRelationToEs(KuTypeEnum.PRODUCT, kuProductId, CanalEntry.EventType.INSERT);
        }
        log.info("insertProductChunkToEs: add relation success, ids={}", id2JsonData.keySet());
    }

    public void updateProductChunkInEs(List<ProductChunk> productChunks, boolean needEmbedding) {
        if (CollectionUtil.isEmpty(productChunks)) {
            return;
        }
        Set<Long> kuProductIds = productChunks.stream().map(ProductChunk::getKuProductId).collect(Collectors.toSet());
        Map<Long, KuProduct> kuProductMap = kuProductRepository.listByIds(kuProductIds).stream().collect(Collectors.toMap(e -> e.getId(), e -> e, (v1, v2) -> v2));
        List<ProductChunkEsEntity> productChunkEsEntities = productChunks.stream()
                .filter(productChunk -> {
                    KuProduct kuProduct = kuProductMap.get(productChunk.getKuProductId());
                    if (kuProduct == null) {
                        log.warn("updateProductChunkInEs: productChunk[id={}] can't find kuProduct[id={}]", productChunk.getId(), productChunk.getKuProductId());
                        return false;
                    }
                    return true;
                })
                .map(productChunk -> new ProductChunkEsEntity(productChunk, kuProductMap.get(productChunk.getKuProductId()))).collect(Collectors.toList());
        setProductChunkTags(productChunkEsEntities);
        if (needEmbedding) {
            embeddingProductChunkEsEntities(productChunkEsEntities);
        }
        Map<String, String> id2UpdateJsonData = productChunkEsEntities.stream().collect(Collectors.toMap(ProductChunkEsEntity::getId, JSON::toJSONString, (v1, v2) -> v2));
        esService.update(ProductChunkEsEntity.indexName, id2UpdateJsonData, true);
        log.info("updateProductChunkInEs, needEmbedding={}, ids={}", needEmbedding, id2UpdateJsonData.keySet());
    }

    private void embeddingProductChunkEsEntities(List<ProductChunkEsEntity> productChunkEsEntities) {
        if (CollectionUtil.isEmpty(productChunkEsEntities)) {
            return;
        }
        ParallelUtil.parallelAsyncCall(2, false, productChunkEsEntities, productChunkEsEntity -> {
            String content = productChunkEsEntity.getContent() == null ? "" : productChunkEsEntity.getContent();
            productChunkEsEntity.setIndexed(true);
            return bedrockService.getCohereEmbeddingAsync(content).thenAccept(embedding -> productChunkEsEntity.setChunkContentEmbedding(embedding));
        });
    }

    public void deleteProductChunkInEs(List<ProductChunk> productChunks) {
        if (CollectionUtil.isEmpty(productChunks)) {
            return;
        }
        List<String> ids = productChunks.stream().filter(e -> e.getId() != null).map(e -> e.getId().toString()).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(ids)) {
            return;
        }
        esService.delete(ProductChunkEsEntity.indexName, ids, true);
        log.info("deleteProductChunkInEs, ids={}", ids);
    }


    private void setProductTags(List<ProductEsEntity> productEsEntities) {
        try {
            if (CollectionUtil.isNotEmpty(productEsEntities)) {
                List<Long> productIds = productEsEntities.stream().map(p -> Long.valueOf(p.getId())).distinct().collect(Collectors.toList());
                ProductGetReq productGetReq = new ProductGetReq();
                productGetReq.setTenantId(productEsEntities.get(0).getTenantId());
                productGetReq.setProjectId(productEsEntities.get(0).getProjectId());
                productGetReq.setProductIds(productIds);
                List<GetKuProductResponse> kuProducts = kbApi.getKuProducts(productGetReq);
                Map<Long, GetKuProductResponse> kuProductResponseMap = kuProducts.stream().collect(Collectors.toMap(GetKuProductResponse::getId, e -> e, (v1, v2) -> v2));
                for (ProductEsEntity productEsEntity : productEsEntities) {
                    GetKuProductResponse response = kuProductResponseMap.get(Long.valueOf(productEsEntity.getId()));
                    if (response != null) {
                        // 设置doc级别信息
                        productEsEntity.setSelfTags(response.getSelfTags()).setBrief(response.getBrief());
                    }
                }
                // 针对anker设置brand和storeName
                Set<Long> productDocIds = productEsEntities.stream().map(ProductEsEntity::getDocumentId).collect(Collectors.toSet());
                List<ProductDoc> productDocs = productDocRepository.listByIds(productDocIds);
                Map<Long, String> productDocId2Brand = new HashMap<>();
                Map<Long, String> productDocId2StoreName = new HashMap<>();
                for (ProductDoc productDoc : productDocs) {
                    if (productDoc.getSource().equalsIgnoreCase("ANKER")) {
                        String uri = productDoc.getUri();
                        if (StringUtils.hasText(uri)) {
                            UriComponents uriComponents = UriComponentsBuilder.fromUriString(uri).build();
                            if ("api.metabuf.com".equalsIgnoreCase(uriComponents.getHost())) {
                                MultiValueMap<String, String> queryParams = uriComponents.getQueryParams();
                                String brand = queryParams.getFirst("c");
                                String storeName = queryParams.getFirst("s");
                                productDocId2Brand.put(productDoc.getId(), brand);
                                productDocId2StoreName.put(productDoc.getId(), storeName);
                            }
                        }
                    }
                }
                for (ProductEsEntity productEsEntity : productEsEntities) {
                    productEsEntity
                            .setBrand(productDocId2Brand.get(productEsEntity.getDocumentId()))
                            .setStoreName(productDocId2StoreName.get(productEsEntity.getDocumentId()));
                }
            }
        } catch (Exception e) {
            log.error("setProductTags", e);
        }
    }


    private void setProductChunkTags(List<ProductChunkEsEntity> productChunkEsEntities) {
        if (CollectionUtil.isNotEmpty(productChunkEsEntities)) {
            List<Long> productIds = productChunkEsEntities.stream().map(ProductChunkEsEntity::getKuProductId).distinct().collect(Collectors.toList());
            ProductGetReq productGetReq = new ProductGetReq();
            productGetReq.setTenantId(productChunkEsEntities.get(0).getTenantId());
            productGetReq.setProjectId(productChunkEsEntities.get(0).getProjectId());
            productGetReq.setProductIds(productIds);
            List<GetKuProductResponse> kuProducts = kbApi.getKuProducts(productGetReq);
            Map<Long, GetKuProductResponse> kuProductResponseMap = kuProducts.stream().collect(Collectors.toMap(GetKuProductResponse::getId, e -> e, (v1, v2) -> v2));
            for (ProductChunkEsEntity productChunkEsEntity : productChunkEsEntities) {
                GetKuProductResponse response = kuProductResponseMap.get(productChunkEsEntity.getKuProductId());
                if (response != null) {
                    List<String> mergeTags = new ArrayList<>();
                    List<String> tags = response.getTags();
                    List<String> intents = response.getIntents();
                    if (CollectionUtil.isNotEmpty(tags)) {
                        mergeTags.addAll(tags);
                    }
                    if (CollectionUtil.isNotEmpty(intents)) {
                        mergeTags.addAll(intents);
                    }
                    // 设置doc级别信息
                    productChunkEsEntity.setSelfTags(mergeTags.stream().distinct().collect(Collectors.toList())).setBrief(response.getBrief() == null ? "" : response.getBrief());
                }
            }
            // 针对anker设置brand和storeName
            Set<Long> productDocIds = productChunkEsEntities.stream().map(ProductChunkEsEntity::getProductDocId).collect(Collectors.toSet());
            List<ProductDoc> productDocs = productDocRepository.listByIds(productDocIds);
            Map<Long, String> productDocId2Brand = new HashMap<>();
            Map<Long, String> productDocId2StoreName = new HashMap<>();
            for (ProductDoc productDoc : productDocs) {
                if (productDoc.getSource().equalsIgnoreCase("ANKER")) {
                    String uri = productDoc.getUri();
                    if (StringUtils.hasText(uri)) {
                        UriComponents uriComponents = UriComponentsBuilder.fromUriString(uri).build();
                        if ("api.metabuf.com".equalsIgnoreCase(uriComponents.getHost())) {
                            MultiValueMap<String, String> queryParams = uriComponents.getQueryParams();
                            String brand = queryParams.getFirst("c");
                            String storeName = queryParams.getFirst("s");
                            productDocId2Brand.put(productDoc.getId(), brand);
                            productDocId2StoreName.put(productDoc.getId(), storeName);
                        }
                    }
                }
            }
            for (ProductChunkEsEntity productChunkEsEntity : productChunkEsEntities) {
                productChunkEsEntity
                        .setBrand(productDocId2Brand.get(productChunkEsEntity.getProductDocId()))
                        .setStoreName(productDocId2StoreName.get(productChunkEsEntity.getProductDocId()));
            }
        }
    }
}
