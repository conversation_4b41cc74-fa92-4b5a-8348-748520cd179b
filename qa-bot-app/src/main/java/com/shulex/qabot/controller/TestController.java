package com.shulex.qabot.controller;

import cn.hutool.http.HttpUtil;
import com.alibaba.cloud.nacos.NacosDiscoveryProperties;
import com.alibaba.cloud.nacos.NacosServiceManager;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.shulex.common.user.token.Authed;
import com.shulex.gpt.apisdk.IIntentApi;
import com.shulex.gpt.apisdk.IKBApi;
import com.shulex.gpt.apisdk.dto.GetIntentResponse;
import com.shulex.gpt.apisdk.dto.LLMUsages;
import com.shulex.gpt.apisdk.dto.RetrievedKu;
import com.shulex.qabot.alg.AlgClient;
import com.shulex.qabot.alg.dto.RelevantScoreReq;
import com.shulex.qabot.client.dto.EsSearchResult;
import com.shulex.qabot.client.dto.esentity.DocumentChunkEsEntity;
import com.shulex.qabot.client.dto.esentity.ProductEsEntity;
import com.shulex.qabot.client.res.TranslateTagKeywordRes;
import com.shulex.qabot.es.EsSearchService;
import com.shulex.qabot.es.EsService;
import com.shulex.qabot.feign.gpt.GptHttpClient;
import com.shulex.qabot.mysql.repository.KuRepository;
import com.shulex.qabot.service.BedrockService;
import com.shulex.qabot.service.CohereService;
import com.shulex.qabot.service.KnowledgeService;
import com.shulex.qabot.service.KuLabelRelationService;
import com.shulex.qabot.util.AsyncHttpUtil;
import com.shulex.qabot.util.JsonUtil;
import com.shulex.qabot.util.ParallelUtil;
import groovy.lang.Tuple3;
import org.apache.commons.jexl3.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static java.lang.String.format;

@ApiIgnore
@RestController
public class TestController {
    public static Long lastHttpRequestTime = 0L;
    @Autowired
    private ApplicationContext applicationContext;
    @Autowired
    private KuRepository kuRepository;
    @Autowired
    private BedrockService bedrockService;
    @Autowired
    EsService esService;
    @Autowired
    AlgClient algClient;
    @Autowired
    IIntentApi iIntentApiFeignClient;
    @Autowired
    private NacosServiceManager nacosServiceManager;
    @Autowired
    private NacosDiscoveryProperties nacosDiscoveryProperties;
    @Autowired
    KuLabelRelationService kuLabelRelationService;
    @Autowired
    EsSearchService esSearchService;
    @Autowired
    GptHttpClient gptHttpClient;
    @Autowired
    IKBApi kbApi;
    @Autowired
    CohereService cohereService;
    @Autowired
    KnowledgeService knowledgeService;

    @Authed(allowInner = true)
    @GetMapping("test")
    public void test(@RequestBody JSONObject body) {
        List<DocumentChunkEsEntity> data = Stream.of(
                "Carson City is the capital city of the American state of Nevada. At the 2010 United States Census, Carson City had a population of 55,274.",
                "The Commonwealth of the Northern Mariana Islands is a group of islands in the Pacific Ocean that are a political division controlled by the United States. Its capital is Saipan.",
                "Charlotte Amalie is the capital and largest city of the United States Virgin Islands. It has about 20,000 people. The city is on the island of Saint Thomas.",
                "Washington, D.C. (also known as simply Washington or D.C., and officially as the District of Columbia) is the capital of the United States. It is a federal district. The President of the USA and many major national government offices are in the territory. This makes it the political center of the United States of America.",
                "Capital punishment (the death penalty) has existed in the United States since before the United States was a country. As of 2017, capital punishment is legal in 30 of the 50 states. The federal government (including the United States military) also uses capital punishment."
        ).map(e -> new DocumentChunkEsEntity().setContent(e)).collect(Collectors.toList());
        LLMUsages usages = new LLMUsages();
        List<EsSearchResult<DocumentChunkEsEntity>> reranks = cohereService.rerank("What is the capital of the United States?", data, e -> e.getContent(), usages).join();

//        List<EsSearchResult<KuEsEntity>> results = knowledgeService.hybridSearch(
//                "111",
//                5,
//                KuEsEntity.indexName,
//                Arrays.asList(Pair.of(LambdaUtil.getFieldName(KuEsEntity::getTenantId), 67757)),
//                LambdaUtil.getFieldName(KuEsEntity::getTitleEmbeddingCohere),
//                bedrockService.getCohereEmbedding("有多少人"),
//                Arrays.asList("美国", "人"),
//                "Greeting",
//                MapUtil.of("title", 1.0F),
//                KuEsEntity.class
//        ).join();
        System.out.println();
    }

    @Authed(allowInner = true)
    @GetMapping("testStream")
    public Object testStream(HttpServletResponse response) {
        response.setContentType("text/event-stream");
        response.setCharacterEncoding("utf-8");
        try {
            int a = 0;
            PrintWriter pw = response.getWriter();
            while (true) {
                if (pw.checkError()) {
                    System.out.println("客户端断开连接");
                    break;
                }
                Thread.sleep(1000);
                pw.write("data:Math.random()=" + Math.random() + "\n\n");
                pw.flush();
                a += 1;
                if (a > 3) {
                    break;
                }
            }
        } catch (IOException | InterruptedException e) {
            e.printStackTrace();
        }
        return null;
    }

    @Authed(allowInner = true)
    @GetMapping("/getByBeanName")
    public Object getByBeanName(@RequestParam String name) {
        return applicationContext.getBean(name);
    }

    @Authed(allowInner = true)
    @GetMapping("/getLastHttpRequestTime")
    Object getLastHttpRequestTime() {
        JSONObject res = new JSONObject();
        res.put("lastHttpRequestTime", lastHttpRequestTime);
        Instant instant = Instant.ofEpochMilli(lastHttpRequestTime);
        LocalDateTime localDateTime = LocalDateTime.ofInstant(instant, ZoneOffset.of("+8"));
        res.put("+8时间", localDateTime);
        return res;
    }

    @Authed(allowInner = true)
    @PostMapping("exceptionTest")
    public void exceptionTest(@RequestBody JSONObject body) {
        System.out.println(1 / 0);
    }

    @Authed(allowInner = true)
    @PostMapping("testEsSearchDsl")
    public Object testEsSearchDsl(@RequestParam String dslVmPath, @RequestParam String index, @RequestBody Map<String, Object> params) {
        return null;
    }

    @Authed(allowInner = true)
    @PostMapping("similarityCompare")
    public Object similarityCompare(@RequestBody JSONObject body) {
        String text1 = body.getString("text1");
        String text2 = body.getString("text2");
        String model = body.getString("model");
        Boolean embedding = body.getBoolean("embedding");
        if (model == null) {
            model = "mpnet";
        }
        List<String> models = Arrays.asList("mpnet", "cohere");
        if (!models.contains(model)) {
            return format("model参数取值范围: %s", models);
        }
        List<BigDecimal> text1Embedding = Collections.emptyList();
        List<BigDecimal> text2Embedding = Collections.emptyList();
        switch (model) {
            case "mpnet":
                text1Embedding = algClient.textEmbed(text1).join().getResult();
                text2Embedding = algClient.textEmbed(text2).join().getResult();
                break;
            case "cohere":
                text1Embedding = bedrockService.getCohereEmbedding(text1);
                text2Embedding = bedrockService.getCohereEmbedding(text2);
                break;
        }
        BigDecimal similarity = BigDecimal.ZERO;
        for (int i = 0; i < text1Embedding.size(); i++) {
            similarity = similarity.add(text1Embedding.get(i).multiply(text2Embedding.get(i)));
        }
        JSONObject res = new JSONObject();
        res.put("text1", text1);
        res.put("text2", text2);
        res.put("model", model);
        res.put("similarity", similarity);
        if (embedding != null && embedding) {
            res.put("text1Embedding", text1Embedding);
            res.put("text2Embedding", text2Embedding);
        }
        return res;
    }

    @Authed(allowInner = true)
    @GetMapping("cohereEmbeddingTest")
    public void cohereEmbeddingTest() {
//        List<String> list1 = Arrays.asList("hello", "hello", "你好");
//        List<BedrockService.CohereEmbeddingResult> list1R = bedrockService.getCohereEmbeddingsAsync(list1).join();
        List<String> list2 = new ArrayList<>();
        for (int i = 0; i <= 259; i++) {
            list2.add("hello" + (i % 128));
        }
        List<BedrockService.CohereEmbeddingResult> list2R = bedrockService.getCohereEmbeddingsAsync(list2).join();
        Map<String, List<BedrockService.CohereEmbeddingResult>> text2E = new HashMap<>();
        for (String text : list2) {
            List<BedrockService.CohereEmbeddingResult> collect = list2R.stream().filter(e -> e.getText().equals(text)).collect(Collectors.toList());
            text2E.put(text, collect);
        }
        AtomicBoolean hasNotEqual = new AtomicBoolean(false);
        text2E.forEach((text, eList) -> {
            for (BedrockService.CohereEmbeddingResult cohereEmbeddingResult : eList) {
                for (BedrockService.CohereEmbeddingResult embeddingResult : eList) {
                    List<BigDecimal> embedding = cohereEmbeddingResult.getEmbedding();
                    List<BigDecimal> embedding1 = embeddingResult.getEmbedding();
                    for (int i = 0; i < embedding.size(); i++) {
                        if (!embedding.get(i).equals(embedding1.get(i))) {
                            hasNotEqual.set(true);
                            System.out.println(text + "       not equal");
                        }
                    }
                }
            }
        });
        System.out.println();
    }

    @Authed(allowInner = true)
    @PostMapping("keywordCheck")
    public Object keywordCheck(@RequestBody JSONObject body) {
        long tenantId = body.getLong("tenantId");
        long projectId = body.getLong("projectId");
        String text = body.getString("text");
        JSONObject res = new JSONObject();
        List<GetIntentResponse> intents = iIntentApiFeignClient.getIntents(tenantId, projectId, null);
        JexlEngine jexlEngine = new JexlBuilder().create();
        for (GetIntentResponse intent : intents) {
            String keywordJexl = intent.getKeywordJexl();
            JexlExpression jexlExpression = jexlEngine.createExpression(keywordJexl);
            JexlContext mapContext = new MapContext();
            mapContext.set("userQuestion", text);
            Boolean result = (Boolean) jexlExpression.evaluate(mapContext);
            if (result) {
                res.put("matchedIntentName", intent.getName());
                res.put("matchedIntentId", intent.getId());
                res.put("matchedIntent", intent);
                return res;
            }
        }
        res.put("res", "未命中任何意图关键字");
        return res;
    }

    @Authed(allowInner = true)
    @PostMapping("rerankTest")
    public Object rerankTest(@RequestBody JSONObject body) {
        return bedrockService.rerankKnowledge(body.getString("chatId"),
                null,
                body.getString("query"),
                body.getJSONArray("kuList").toJavaList(RetrievedKu.class),
                JsonUtil.copyObjectByJson(body.get("chunkList"), new TypeReference<List<List<DocumentChunkEsEntity>>>() {
                }),
                body.getJSONArray("productList").toJavaList(ProductEsEntity.class),
                null
        );
    }

    @Authed(allowInner = true)
    @PostMapping("knowledgeRerankTest")
    public Object knowledgeRerankTest() {
        String query = "我想知道洛杉矶有多少人";
        List<RetrievedKu> kuList = new ArrayList<>();
        {
            RetrievedKu ku1 = new RetrievedKu();
            ku1.setTitle("洛杉矶GDP");
            ku1.setContent("3万亿美元");
            kuList.add(ku1);
            RetrievedKu ku2 = new RetrievedKu();
            ku2.setTitle("洛杉矶市长");
            ku2.setContent("普罗米修斯");
            kuList.add(ku2);
            RetrievedKu ku3 = new RetrievedKu();
            ku3.setTitle("洛杉矶人口普查");
            ku3.setContent("难民1万人，市民3千万");
            kuList.add(ku3);
        }
        List<List<DocumentChunkEsEntity>> chunkList = Arrays.asList(
                Arrays.asList(new DocumentChunkEsEntity().setContent("洛杉矶"), new DocumentChunkEsEntity().setContent("人口大约3500万人")),
                Arrays.asList(new DocumentChunkEsEntity().setContent("人口数量"), new DocumentChunkEsEntity().setContent("纽约人口"), new DocumentChunkEsEntity().setContent("5千万"))
        );
        List<ProductEsEntity> productList = Arrays.asList(
                new ProductEsEntity().setTitle("旧金山纪念T恤").setDescribe("为了纪念西部大开发历史事件，特此制作纪念T恤，售价100美元")
        );
        LLMUsages usages1 = new LLMUsages();
        LLMUsages usages2 = new LLMUsages();
        List<KnowledgeService.KnowledgeSortResult> knowledgeSortResults = cohereService.rerankKnowledge(query, kuList, chunkList, productList, usages1);
        List<KnowledgeService.KnowledgeSortResult> knowledgeSortResults1 = bedrockService.rerankKnowledge(null, null, query, kuList, chunkList, productList, usages2);
        return null;
    }

    public static void main(String[] args) {
        AsyncHttpUtil.testHttpStream();
//        String orderL = TagDTO.generateKeywordJexlWithOrOperation(getTrans("order"));
//        String paypalL = TagDTO.generateKeywordJexlWithOrOperation(getTrans("paypal"));
//        String chiefL = TagDTO.generateKeywordJexlWithOrOperation(getTrans("Chief Experience Officer"));
//        String forfreeL = TagDTO.generateKeywordJexlWithOrOperation(getTrans("for free"));
//        String jexl = format("((%s) && (%s)) || ((%s) && (%s))", orderL, paypalL, chiefL, forfreeL);
//        JSONObject jexlJson = new JSONObject() {{
//            put("keywordJexl", jexl);
//        }};
//        System.out.println(jexlJson.toJSONString());
//
//        JexlEngine jexlEngine = new JexlBuilder().create();
//        JexlExpression jexlExpression = jexlEngine.createExpression(jexl);
//        JexlContext mapContext = new MapContext();
//        mapContext.set("userQuestion", "order paypal");
//        Boolean result = (Boolean) jexlExpression.evaluate(mapContext);
//        System.out.println(result);
    }

    static List<String> getTrans(String text) {
        JSONObject body = new JSONObject();
        body.put("keywords", Arrays.asList(text));
        String resBody = HttpUtil.post("http://10.7.4.208:13000/translateTagKeyword", body.toJSONString());
        List<TranslateTagKeywordRes> translateTagKeywordRes = JSON.parseArray(resBody, TranslateTagKeywordRes.class);
        return translateTagKeywordRes.stream().map(TranslateTagKeywordRes::getToLanguageKeyword).collect(Collectors.toList());
    }
}
