package com.shulex.qabot.util;

import lombok.SneakyThrows;

public class RetryUtil {
    @SneakyThrows
    public static void retry(int retryCount, long delayMills, Runnable runnable) {
        if (runnable == null) {
            return;
        }
        if (retryCount <= 0) {
            retryCount = 1;
        }
        if (delayMills <= 0) {
            delayMills = 1000;
        }
        Exception exception = null;
        while (retryCount > 0) {
            try {
                runnable.run();
                return;
            } catch (Exception e) {
                exception = e;
                retryCount--;
                Thread.sleep(delayMills);
            }
        }
        throw exception;
    }
}
