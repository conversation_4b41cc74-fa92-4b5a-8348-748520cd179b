package com.shulex.qabot.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import org.asynchttpclient.*;
import io.netty.handler.codec.http.HttpHeaders;
import org.asynchttpclient.*;
import org.springframework.util.ObjectUtils;

import java.io.InputStream;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

import static java.lang.String.format;


public class AsyncHttpUtil {
    static AsyncHttpClient asyncHttpClient = Dsl.asyncHttpClient(
            Dsl.config()
                    .setMaxConnectionsPerHost(1000)
                    .setConnectTimeout(1000 * 5)
                    .setReadTimeout(1000 * 30)
                    .setRequestTimeout(1000 * 30)
    );

    public static <T> CompletableFuture<T> request(Request request, TypeReference<T> responseType) {
        return asyncHttpClient.executeRequest(request).toCompletableFuture().thenApply(response -> {
            if (response.getStatusCode() >= 200 && response.getStatusCode() < 300) {
                if (responseType == null || responseType.getType() == String.class) {
                    return (T) response.getResponseBody();
                }
                return JSON.parseObject(response.getResponseBody(), responseType);
            } else {
                throw new RuntimeException(format("async http call error: statusCode=[%s], request=[%s], response=[%s]", response.getStatusCode(), request.toString(), response.toString()));
            }
        });
    }

    public static <T> CompletableFuture<T> post(String url, Object body, TypeReference<T> responseType) {
        RequestBuilder requestBuilder = Dsl.post(url).setBody(JSON.toJSONString(body));
        Request request = requestBuilder.build();
        return request(request, responseType);
    }

    public static <T> CompletableFuture<T> get(String url, Map<String, Object> headers, TypeReference<T> responseType) {
        RequestBuilder requestBuilder = Dsl.get(url);
        Request request = requestBuilder.build();
        if (!ObjectUtils.isEmpty(headers)) {
            headers.forEach(requestBuilder::addHeader);
        }
        return request(request, responseType);
    }

    public static <T> CompletableFuture<T> post(String url, Object body, Map<String, Object> headers, TypeReference<T> responseType) {
        RequestBuilder requestBuilder = Dsl.post(url).setBody(JSON.toJSONString(body));
        if (!ObjectUtils.isEmpty(headers)) {
            headers.forEach(requestBuilder::addHeader);
        }
        Request request = requestBuilder.build();
        return request(request, responseType);
    }

    public static InputStream downloadFileAsStream(String url) {
        Response response = asyncHttpClient.executeRequest(Dsl.get(url)).toCompletableFuture().join();
        return response.getResponseBodyAsStream();
    }

    public static void testHttpStream() {
        RequestBuilder requestBuilder = Dsl.get("http://127.0.0.1:8080/testStream");
        Request request = requestBuilder.build();
        ListenableFuture<String> future = asyncHttpClient.executeRequest(request, new AsyncCompletionHandler<String>() {
            @Override
            public State onStatusReceived(HttpResponseStatus status) throws Exception {
                // Handle the status
                return super.onStatusReceived(status);
            }

            @Override
            public State onHeadersReceived(HttpHeaders headers) throws Exception {
                // Handle the headers
                return super.onHeadersReceived(headers);
            }

            @Override
            public State onBodyPartReceived(HttpResponseBodyPart bodyPart) {
                // Handle the body part
                byte[] bytes = bodyPart.getBodyPartBytes();
                String data = new String(bytes);
                System.out.println("Received: " + data);
                return State.CONTINUE;
            }

            @Override
            public String onCompleted(Response response) {
                // Handle completion
                System.out.println("Stream completed.");
                return null;
            }

            @Override
            public void onThrowable(Throwable t) {
                // Handle any errors
                t.printStackTrace();
            }
        });
        String join = future.toCompletableFuture().join();
        System.out.println("结束");
    }
}
