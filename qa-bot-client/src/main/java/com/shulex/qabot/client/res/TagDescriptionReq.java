package com.shulex.qabot.client.res;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.ArrayList;
import java.util.List;

@Data
@Accessors(chain = true)
public class TagDescriptionReq {
    private Long tenant_id;
    private List<Tag> tags = new ArrayList<>();
    private Boolean translate_name = false;
    private Boolean ai_generate = false;
    private Boolean use_origin_desc = true;


    @Data
    public static class Tag{
        private Long id;
        private String name;
        private String describe;
        private Integer level;
        private Long parent_id;
    }
}
