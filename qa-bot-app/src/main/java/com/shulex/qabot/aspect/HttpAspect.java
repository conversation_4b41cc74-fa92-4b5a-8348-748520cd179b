package com.shulex.qabot.aspect;

import com.shulex.qabot.controller.TestController;
import io.micrometer.core.instrument.ImmutableTag;
import io.micrometer.core.instrument.Metrics;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import cn.hutool.core.lang.Validator;
import cn.hutool.extra.servlet.ServletUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.shulex.common.util.interfaces.IAfterRequest;
import com.shulex.common.web.consts.MdcConst;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.*;
import org.slf4j.MDC;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.Enumeration;
import java.util.List;
import java.util.concurrent.TimeUnit;

import static java.lang.String.format;


@Aspect
@Component
@Order(1)
@Slf4j
public class HttpAspect {
    final String recordStartTimeKey = "recordStartTime";

    /**
     * Execute before the method is executed
     * Define the pointcut here, use regular matching to match the specific method
     */
    @Before("execution(public * com.shulex..controller.*.*(..))")
    public void logBefore(JoinPoint joinPoint) {
        TestController.lastHttpRequestTime = System.currentTimeMillis();
        HttpServletRequest request = getRequest();
        StringBuilder errmsg = new StringBuilder();
        if (request != null) {
            //This is the information to record the http request.
            String clientIp = ServletUtil.getClientIP(request);
            if (!Validator.isIpv4(clientIp) && !Validator.isIpv6(clientIp)) {
                clientIp = "";
            }
            errmsg.append("IP=").append(request.getRemoteAddr()).append("/").append(clientIp).append(",")
                    .append("URL=").append(request.getRequestURL()).append(",")
                    .append("REFER=").append(request.getHeader(HttpHeaders.REFERER)).append(",")
                    .append("TRACE_ID=").append(request.getHeader("trace-id"));
            request.setAttribute(recordStartTimeKey, System.currentTimeMillis());
        }
        log.info("HTTP_ASPECT:Request >>>> {}, METHOD= {}, ARGS= {}", errmsg,
                joinPoint.getSignature().getDeclaringTypeName() + "." + joinPoint.getSignature().getName(), JSON.toJSONString(joinPoint.getArgs()));
    }

    /**
     * Execute after the method is executed
     */
    @After("execution(public * com.shulex..controller.*.*(..))")
    public void logAfter() {
        // 不同Spring版本导致After与AfterReturning(或AfterThrowing)执行顺序不同，所以这里不做任何操作
        // 目前版本是先执行AfterReturning(或AfterThrowing)，再执行After
        HttpServletResponse response = getResponse();
        if (response != null) {
            response.setHeader("X-Trace-Id", MDC.get(MdcConst.TRACE_ID));
        }
    }

    /**
     * Get the return value of the method
     */
    @AfterReturning(value = "execution(public * com.shulex..controller.*.*(..))", returning = "object")
    public void logAfterReturn(Object object) {
        HttpServletRequest request = getRequest();
        StringBuilder msg = new StringBuilder();
        if (request != null) {
            String clientIp = ServletUtil.getClientIP(request);
            if (!Validator.isIpv4(clientIp) && !Validator.isIpv6(clientIp)) {
                clientIp = "";
            }
            Double consumeSeconds = null;
            Long consumeMillSeconds = null;
            Object recordStartTime = request.getAttribute(recordStartTimeKey);
            if (recordStartTime != null) {
                consumeSeconds = (System.currentTimeMillis() - (Long) recordStartTime) / 1000.0;
                consumeMillSeconds = System.currentTimeMillis() - (Long) recordStartTime;
            }
            msg.append("IP=").append(request.getRemoteAddr()).append("/").append(clientIp).append(",")
                    .append("URL=").append(request.getRequestURL()).append(",")
                    .append("REFER=").append(request.getHeader(HttpHeaders.REFERER)).append(",")
                    .append("TRACE_ID=").append(request.getHeader("trace-id")).append(",")
                    .append("ConsumeSeconds=").append(format("%.4fs", consumeSeconds));
            try {
                Metrics.timer("qaBotInterfaceTimer", Arrays.asList(new ImmutableTag("requestUri", request.getRequestURI()))).record(consumeMillSeconds, TimeUnit.MILLISECONDS);
            } catch (Exception e) {
                log.warn("", e);
            }
        }
        //This is the information returned by the record http request.
        log.info("HTTP_ASPECT:Response <<<< {}, BODY={}", msg, JSON.toJSONString(object));
        after();
    }

    @AfterThrowing(value = "execution(public * com.shulex..controller.*.*(..))", throwing = "ex")
    public void logAfterThrowing(JoinPoint joinPoint, Exception ex) {
        HttpServletRequest request = getRequest();
        StringBuilder errmsg = new StringBuilder("Error=").append(ex.getMessage()).append(",");
        if (request != null) {
            errmsg.append("RequestURL=").append(request.getRequestURL()).append(",");
            errmsg.append("QueryString=").append(request.getQueryString()).append(",");
            errmsg.append("ParameterMap=").append(JSON.toJSONString(request.getParameterMap())).append(",");
            errmsg.append("Header=");
            Enumeration<String> headerNames = request.getHeaderNames();
            while (headerNames.hasMoreElements()) {
                String headerName = headerNames.nextElement();
                errmsg.append(headerName).append(":").append(request.getHeader(headerName)).append(";");
            }
        }
        log.warn("HTTP_ASPECT:Throwing <<<< {}", errmsg, ex);
        after();
    }

    private static HttpServletRequest getRequest() {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes == null) {
            return null;
        }
        return attributes.getRequest();
    }

    private static HttpServletResponse getResponse() {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes == null) {
            return null;
        }
        return attributes.getResponse();
    }


    private void after() {
        MDC.remove(MdcConst.EXT_1);
        MDC.remove(MdcConst.EXT_2);
        MDC.remove(MdcConst.EXT_3);
        for (IAfterRequest after : SpringUtil.getBeansOfType(IAfterRequest.class).values()) {
            after.clear();
        }
    }

}
