package com.shulex.qabot.controller;

import com.shulex.common.user.token.Authed;
import com.shulex.common.user.utils.CactusUtil;
import com.shulex.qabot.client.req.GetCategoryReq;
import com.shulex.qabot.client.req.RecommendProductReq;
import com.shulex.qabot.client.res.GetCategoryRes;
import com.shulex.qabot.client.res.RecommendProductContext;
import com.shulex.qabot.client.res.RecommendProductRes;
import com.shulex.qabot.es.EsSearchService;
import com.shulex.qabot.feign.gpt.IInternalApiFeignClient;
import com.shulex.qabot.service.product.ProductRecommendService;
import com.shulex.qabot.service.product.ProductService;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import static com.shulex.gpt.apisdk.consts.GptConst.X_LANGUAGE;
import static com.shulex.gpt.apisdk.consts.GptConst.X_TOKEN;

@RestController
public class ProductController {
    @Autowired
    EsSearchService esSearchService;
    @Autowired
    ProductRecommendService productRecommendService;
    @Autowired
    ProductService productService;

    @ApiOperation(value = "推荐商品")
    @Authed(allowInner = true)
    @PostMapping("/recommendProduct")
    public RecommendProductRes recommendProduct(@RequestBody RecommendProductReq req) {
        return productRecommendService.recommendProduct(req);
    }

    @ApiOperation(value = "推荐商品上下文")
    @Authed(allowInner = true)
    @PostMapping("/getRecommendProductContext")
    public RecommendProductContext getRecommendProductContext(@RequestBody RecommendProductReq req) {
        return productRecommendService.getRecommendProductContext(req);
    }


    @ApiOperation(value = "getCategory")
    @Authed
    @PostMapping("/getCategory")
    public GetCategoryRes getCategory(@RequestBody GetCategoryReq req, @RequestHeader(value = X_LANGUAGE, required = false) String lang) {
        Long tenantId = CactusUtil.getTenantId();
        if (lang == null){
            req.setLang("en");
        }else {
            req.setLang(lang);
        }
        return productRecommendService.customizedPrompt(tenantId, req);
    }
}
