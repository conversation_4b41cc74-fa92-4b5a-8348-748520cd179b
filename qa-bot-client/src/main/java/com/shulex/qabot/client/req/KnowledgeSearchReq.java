package com.shulex.qabot.client.req;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Pair;
import com.shulex.gpt.apisdk.dto.AiReplyPromptInfo;
import com.shulex.gpt.apisdk.dto.Message;
import com.shulex.gpt.apisdk.dto.MixedQuery;
import com.shulex.gpt.apisdk.dto.RetrievedKu;
import com.shulex.gpt.apisdk.enums.BotLanguageEnum;
import com.shulex.gpt.apisdk.enums.LLMTypeEnum;
import com.shulex.qabot.client.dto.AiAgentMessage;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;
import java.util.Map;
import java.util.Set;

@Data
@Accessors(chain = true)
public class KnowledgeSearchReq {
    Boolean qamIsInnerUser = false;
    Boolean isLivechatPath;
    // control info
    Boolean useQa = true;
    Boolean useDoc = true;
    Boolean useProduct = false;
    // base info
    String chatId;
    Long tenantId;
    Long projectId;
    String clientType;
    String touchPoint;
    String intent;
    String originIntent;
    MixedQuery mixedQuery;
    Map<String, List<?>> entities;
    LLMTypeEnum modelType;
    String pdt;
    String botName;
    Integer wordsLimit;
    BotLanguageEnum language;
    Integer turn;
    List<String> extractTexts;
    AiReplyPromptInfo aiReplyPromptInfo;
    Map<Long, Set<Long>> kuLabelDimensionId2ValueIds;
    // qa info
    List<Message> summarizedMessages;
    AiReplyOption aiReplyOption;
    List<RetrievedKu> specifiedKuList;
    List<Long> directoryIds;
//    Map<Long, Set<Long>> qaKuLabelDimensionId2ValueIds;
    // product info
    ProductSearchParam productSearchParam;

    @Data
    public static class AiReplyOption {
        String reply_type;
        String llm_model;
        Integer top_k = 5;
        Double retrieve_min_score = 0.75;
        Integer words_limit;
        String language;
    }

    @Data
    @Accessors(chain = true)
    public static class ProductSearchParam {
        List<String> product;
        List<String> requirement;
        String summary;
        List<String> subqueries;
        List<Double> price_range;

        public Pair<Double, Double> getMinAndMaxPrice() {
            if (CollectionUtil.isEmpty(price_range) || price_range.size() != 2) {
                return Pair.of(null, null);
            }
            return Pair.of(price_range.get(0), price_range.get(1));
        }
    }
}
