{
  "size": 0,
  "query": {
    "bool": {
      <#if exceptTask??>
      "must_not": [
        {
          "exists": {
            "field": "taskId"
          }
        }
      ],
      "must": [
              {
                "exists": {
                  "field": "chatId"
                }
              }
            ]
      ,
      </#if>

      "filter": [
        <#if tenantId??>
        {
          "term": {
            "tenantId": ${tenantId}
          }
        }
        </#if>
        <#if projectId??>
        ,
        {
          "term": {
            "projectId": ${projectId}
          }
        }
        </#if>
        <#if taskId??>
        ,
        {
          "term": {
            "taskId": ${taskId}
          }
        }
        </#if>
        <#if treeId??>
        ,
        {
          "term": {
            "treeId": ${treeId}
          }
        }
        </#if>
        <#if startTime?? || endTime??>
        ,
        {
          "range": {
            "ticketCreatedAt": {
              <#if startTime??>
              "gte": "${startTime}"
              </#if>
              <#if startTime?? && endTime??>,</#if>
              <#if endTime??>
              "lte": "${endTime}"
              </#if>
            }
          }
        }
        </#if>
      ]
    }
  },
  "aggs": {
  "all_count": {
          "filter": {
            "exists": {
              "field": "tagName"
            }
          }
     },
    "multi_field_agg": {
      "composite": {
      "size": 10000,
        "sources": [
          {
            "treeId": {
              "terms": {
                "field": "treeId"
              }
            }
          },
          {
            "treeName": {
              "terms": {
                "field": "treeName"
              }
            }
          }
          ,
          <#if isSingleTag??>
            {
                "tagIdPath":
                {
                    "terms":
                    {
                        "script":
                        {
                            "source": "if (params['_source'].containsKey('tagIdPath') && params['_source']['tagIdPath'] != null) { params['_source']['tagIdPath'].toString() } else { '' }",
                            "lang": "painless"
                        }
                    }
                }
            },
            {
                "tagNamePath":
                {
                    "terms":
                    {
                        "script":
                        {
                            "source": "if (params['_source'].containsKey('tagNamePath') && params['_source']['tagNamePath'] != null) { params['_source']['tagNamePath'].toString() } else { '' }",
                            "lang": "painless"
                        }
                    }
                }
            }
          </#if>
          <#if isMultiTag??>
            {
                "tagIdPath":
                {
                    "terms":
                    {
                        "script":
                        {
                            "source": "if (params['_source'].containsKey('multiTagIdPath') && params['_source']['multiTagIdPath'] != null) { params['_source']['multiTagIdPath'].toString() } else { '' }",
                            "lang": "painless"
                        }
                    }
                }
            },
            {
                "tagNamePath":
                {
                    "terms":
                    {
                        "script":
                        {
                            "source": "if (params['_source'].containsKey('multiTagNamePath') && params['_source']['multiTagNamePath'] != null) { params['_source']['multiTagNamePath'].toString() } else { '' }",
                            "lang": "painless"
                        }
                    }
                }
            }
          </#if>
        ]
      },
      "aggs": {
              "chatIds": {
                "terms": {
                  "field": "chatId.keyword"
                },
                "aggs": {
                  "isAiReply": {
                    "terms": {
                      "script": {
                        "source": "doc.containsKey('isAiReply') ? (doc['isAiReply'].size() > 0 ? doc['isAiReply'].value : false) : false",
                        "lang": "painless"
                      }
                    }
                  }
                }
              }
            }
    }
  }
}