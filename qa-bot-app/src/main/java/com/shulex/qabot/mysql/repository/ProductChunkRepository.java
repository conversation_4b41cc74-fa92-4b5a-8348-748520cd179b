package com.shulex.qabot.mysql.repository;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.shulex.qabot.client.dto.mysqlentity.KuProduct;
import com.shulex.qabot.client.dto.mysqlentity.ProductChunk;
import com.shulex.qabot.mysql.mapper.ProductChunkMapper;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;

@Repository
public class ProductChunkRepository extends ServiceImpl<ProductChunkMapper, ProductChunk> {
    public void indexSucceed(Collection<Long> ids) {
        if (CollectionUtil.isNotEmpty(ids)) {
            lambdaUpdate()
                    .in(ProductChunk::getId, ids)
                    .set(ProductChunk::getIndexed, true)
                    .update();
        }
    }

    public List<ProductChunk> getChunks(long productId, SFunction<ProductChunk, ?>... columns) {
        if (columns == null || columns.length == 0) {
            return lambdaQuery().eq(ProductChunk::getKuProductId, productId).list();
        } else {
            return lambdaQuery().select(columns).eq(ProductChunk::getKuProductId, productId).list();
        }
    }
}
