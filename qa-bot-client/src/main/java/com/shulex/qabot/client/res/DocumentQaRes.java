package com.shulex.qabot.client.res;

import com.alibaba.fastjson.JSONObject;
import com.shulex.gpt.apisdk.dto.GetDocumentResponse;
import com.shulex.gpt.apisdk.dto.LLMUsages;
import com.shulex.qabot.client.dto.EsSearchResult;
import com.shulex.qabot.client.dto.esentity.DocumentChunkEsEntity;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;


@Data
@Accessors(chain = true)
public class DocumentQaRes {
    private Result result;
    private LLMUsages usages;

    @Data
    @Accessors(chain = true)
    public static class Result {
        private Boolean resValid;
        private String response;
        private String explain;
        private String gptOriginContent;
        private List<List<Long>> usedChunkIds;
        private List<GetDocumentResponse> usedDocuments;
        private String usedChunkContents;
        private JSONObject requestGptBody;
    }
}
