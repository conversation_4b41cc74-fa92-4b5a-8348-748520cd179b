package com.shulex.qabot.es;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.lang.func.LambdaUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.shulex.common.util.utils.EmptyUtil;
import com.shulex.qabot.client.dto.esentity.ProductEsEntity;
import com.shulex.qabot.es.dto.ProductEmbeddingSearchParam;
import com.shulex.qabot.es.dto.ProductSearchParam;
import com.shulex.qabot.util.FreeMakerUtil;
import com.shulex.qabot.util.StringUtil;
import lombok.Data;
import com.shulex.qabot.client.dto.EsSearchResult;
import lombok.SneakyThrows;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.action.ActionListener;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.common.settings.Settings;
import org.elasticsearch.common.xcontent.*;
import org.elasticsearch.index.query.*;
import org.elasticsearch.index.query.functionscore.ScriptScoreQueryBuilder;
import org.elasticsearch.script.Script;
import org.elasticsearch.script.ScriptType;
import org.elasticsearch.search.SearchModule;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Consumer;
import java.util.stream.Collectors;

@Service
@Slf4j
public class EsSearchService {
    @Autowired
    RestHighLevelClient restHighLevelClient;

    public CompletableFuture<SearchResponse> search(SearchRequest searchRequest) {
        CompletableFuture<SearchResponse> future = new CompletableFuture<>();
        restHighLevelClient.searchAsync(searchRequest, RequestOptions.DEFAULT, new ActionListener<SearchResponse>() {
            @Override
            public void onResponse(SearchResponse searchResponse) {
                future.complete(searchResponse);
            }

            @Override
            public void onFailure(Exception e) {
                future.completeExceptionally(e);
            }
        });
        return future;
    }

    public <T> CompletableFuture<List<EsSearchResult<T>>> search(SearchRequest searchRequest, Class<T> dataClass) {
        return search(searchRequest).thenApply(searchResponse -> searchResponseToEsResults(searchResponse, dataClass));
    }

    public <T> CompletableFuture<List<EsSearchResult<T>>> search(String index, String dsl, Class<T> dataClass) {
        SearchRequest request = getSearchRequest(index, dsl);
        return search(request, dataClass);
    }

    @SneakyThrows
    public SearchRequest getSearchRequest(String index, String dsl) {
        SearchModule searchModule = new SearchModule(Settings.EMPTY, false, Collections.emptyList());
        XContentParser parser = XContentFactory.xContent(XContentType.JSON).createParser(new NamedXContentRegistry(searchModule.getNamedXContents()), DeprecationHandler.THROW_UNSUPPORTED_OPERATION, dsl);
        SearchSourceBuilder searchSourceBuilder = SearchSourceBuilder.fromXContent(parser);
        String actualDsl = searchSourceBuilder.toString();
        return new SearchRequest()
                .indices(index)
                .source(searchSourceBuilder);
    }

    public <T> List<EsSearchResult<T>> searchResponseToEsResults(SearchResponse searchResponse, Class<T> dataClass) {
        return Arrays.stream(searchResponse.getHits().getHits())
                .map(hit -> {
                    T data = JSON.parseObject(hit.getSourceAsString(), dataClass);
                    double score = hit.getScore();
                    return new EsSearchResult<T>().setData(data).setScore(score);
                }).collect(Collectors.toList());
    }

    public <T> CompletableFuture<List<EsSearchResult<T>>> embeddingCosSearchAsync(String index, List<BigDecimal> vector, String vectorField, int size, List<Pair<String, Object>> termQueryFieldAndValue, List<String> excludeFields, Class<T> dataClass) {
        // bool query
        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
        addTermQueryToBuilder(termQueryFieldAndValue, boolQueryBuilder);
        // knn script
        Map<String, Object> param = new HashMap<>();
        param.put("space_type", "cosinesimil");
        param.put("field", vectorField);
        param.put("query_value", vector.stream().map(BigDecimal::doubleValue).collect(Collectors.toList()));
        Script script = new Script(ScriptType.INLINE, "knn", "knn_score", param);
        // script score
        ScriptScoreQueryBuilder scriptScoreQueryBuilder = new ScriptScoreQueryBuilder(boolQueryBuilder, script);
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder()
                .query(scriptScoreQueryBuilder)
                .size(size);
        if (!ObjectUtils.isEmpty(excludeFields)) {
            searchSourceBuilder.fetchSource(null, excludeFields.toArray(new String[0]));
        }
        String dsl = searchSourceBuilder.toString();
        SearchRequest searchRequest = new SearchRequest().indices(index).source(searchSourceBuilder);
        return search(searchRequest, dataClass).thenApply(esSearchResults -> {
            for (EsSearchResult<T> esSearchResult : esSearchResults) {
                esSearchResult.setScore(esSearchResult.getScore() - 1.0);
            }
            return esSearchResults;
        });
    }

    public <T> CompletableFuture<List<EsSearchResult<T>>> embeddingSearchAsync(String chatId, String index, List<BigDecimal> vector, String vectorField, int size, double esTextScoreMax, double esTextScoreMin, double embeddingScaleMax, double embeddingScaleMin, Consumer<BoolQueryBuilder> boolQueryBuilderConsumer, List<String> excludeFields, Class<T> dataClass) {
        // bool query
        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
        boolQueryBuilderConsumer.accept(boolQueryBuilder);
        // knn script
        Map<String, Object> param = new HashMap<>();
        param.put("query_value", vector.stream().map(BigDecimal::doubleValue).collect(Collectors.toList()));
        String source = String.format("double text_score = _score;double knn_score = cosineSimilarity(params.query_value, doc['%s']);if(text_score<=0){return knn_score;}else{return ((text_score - %s) / %s * %s + %s) * knn_score}", vectorField, esTextScoreMin, (esTextScoreMax - esTextScoreMin), (embeddingScaleMax - embeddingScaleMin), embeddingScaleMin);
        Script script = new Script(ScriptType.INLINE, "painless", source, param);
        // script score
        ScriptScoreQueryBuilder scriptScoreQueryBuilder = new ScriptScoreQueryBuilder(boolQueryBuilder, script);
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder()
                .query(scriptScoreQueryBuilder)
                .size(size);
        if (!ObjectUtils.isEmpty(excludeFields)) {
            searchSourceBuilder.fetchSource(null, excludeFields.toArray(new String[0]));
        }
        String dsl = searchSourceBuilder.toString();
        log.info("embeddingSearchAsync[{}]: index={}, dsl={}", chatId, index, dsl);
        SearchRequest searchRequest = new SearchRequest().indices(index).source(searchSourceBuilder);
        return search(searchRequest, dataClass);
    }

    public <T> CompletableFuture<List<EsSearchResult<T>>> matchAsync(String index, String matchText, String matchField, Integer size, List<Pair<String, Object>> termQueryFieldAndValue, List<String> excludeFields, Class<T> dataClass) {
        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
        boolQueryBuilder.must(new MatchQueryBuilder(matchField, matchText));
        addTermQueryToBuilder(termQueryFieldAndValue, boolQueryBuilder);
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder().query(boolQueryBuilder);
        if (size != null) {
            searchSourceBuilder.size(size);
        }
        if (!ObjectUtils.isEmpty(excludeFields)) {
            searchSourceBuilder.fetchSource(null, excludeFields.toArray(new String[0]));
        }
        String dsl = searchSourceBuilder.toString();
        SearchRequest searchRequest = new SearchRequest().indices(index).source(searchSourceBuilder);
        return search(searchRequest, dataClass);

    }

    public <T> CompletableFuture<List<EsSearchResult<T>>> matchPhraseAsync(String index, String matchText, String matchField, Integer size, List<Pair<String, Object>> termQueryFieldAndValue, List<String> excludeFields, Class<T> dataClass) {
        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
        boolQueryBuilder.must(new MatchPhraseQueryBuilder(matchField, matchText));
        addTermQueryToBuilder(termQueryFieldAndValue, boolQueryBuilder);
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder().query(boolQueryBuilder);
        if (size != null) {
            searchSourceBuilder.size(size);
        }
        if (!ObjectUtils.isEmpty(excludeFields)) {
            searchSourceBuilder.fetchSource(null, excludeFields.toArray(new String[0]));
        }
        String dsl = searchSourceBuilder.toString();
        SearchRequest searchRequest = new SearchRequest().indices(index).source(searchSourceBuilder);
        return search(searchRequest, dataClass);
    }

    public <T> CompletableFuture<List<EsSearchResult<T>>> termQueryAsync(String index, Integer size, List<Pair<String, Object>> termQueryFieldAndValue, List<String> excludeFields, Class<T> dataClass) {
        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
        addTermQueryToBuilder(termQueryFieldAndValue, boolQueryBuilder);
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder().query(boolQueryBuilder);
        if (size != null) {
            searchSourceBuilder.size(size);
        }
        if (!ObjectUtils.isEmpty(excludeFields)) {
            searchSourceBuilder.fetchSource(null, excludeFields.toArray(new String[0]));
        }
        String dsl = searchSourceBuilder.toString();
        SearchRequest searchRequest = new SearchRequest().indices(index).source(searchSourceBuilder);
        return search(searchRequest, dataClass);
    }


    public <T> CompletableFuture<List<EsSearchResult<T>>> termQueryAsync(String index, Integer size, List<Pair<String, Object>> termQueryFieldAndValue, List<String> noContainsFields, List<String> excludeFields, Class<T> dataClass) {
        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
        addTermQueryToBuilder(termQueryFieldAndValue, boolQueryBuilder);
        if (EmptyUtil.isNotEmpty(noContainsFields)) {
            for (String noContainsField : noContainsFields) {
                boolQueryBuilder.mustNot(QueryBuilders.existsQuery(noContainsField));
            }
        }
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder().query(boolQueryBuilder);
        if (size != null) {
            searchSourceBuilder.size(size);
        }
        if (!ObjectUtils.isEmpty(excludeFields)) {
            searchSourceBuilder.fetchSource(null, excludeFields.toArray(new String[0]));
        }
        String dsl = searchSourceBuilder.toString();
        SearchRequest searchRequest = new SearchRequest().indices(index).source(searchSourceBuilder);
        return search(searchRequest, dataClass);
    }

    public <T> CompletableFuture<List<EsSearchResult<T>>> wildQueryPrefixAsync(String index, Integer size, List<Pair<String, Object>> termQueryFieldAndValue, String filed, String value, Class<T> dataClass) {
        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
        addTermQueryToBuilder(termQueryFieldAndValue, boolQueryBuilder);
        boolQueryBuilder.filter(QueryBuilders.matchBoolPrefixQuery(filed, value.toLowerCase()));
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder().query(boolQueryBuilder);
        if (size != null) {
            searchSourceBuilder.size(size);
        }
        searchSourceBuilder.fetchSource(Collections.singletonList(filed).toArray(new String[0]), null);
        String dsl = searchSourceBuilder.toString();
        SearchRequest searchRequest = new SearchRequest().indices(index).source(searchSourceBuilder);
        return search(searchRequest, dataClass);
    }


    public <T> CompletableFuture<List<EsSearchResult<T>>> wildQueryPrefixAllFieldAsync(String index, Integer size, List<Pair<String, Object>> termQueryFieldAndValue, String filed, String filed1,String value, Class<T> dataClass) {
        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
        addTermQueryToBuilder(termQueryFieldAndValue, boolQueryBuilder);
        boolQueryBuilder.should(
                QueryBuilders.matchBoolPrefixQuery(filed, value.toLowerCase()).boost(0.9f)
        ).should(
                QueryBuilders.matchBoolPrefixQuery(filed1, value.toLowerCase()).boost(0.1f)
        );
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder().query(boolQueryBuilder);
        if (size != null) {
            searchSourceBuilder.size(size);
        }
        String dsl = searchSourceBuilder.toString();
        SearchRequest searchRequest = new SearchRequest().indices(index).source(searchSourceBuilder);
        return search(searchRequest, dataClass);
    }

    public static void addTermQueryToBuilder(List<Pair<String, Object>> termQueryFieldAndValue, BoolQueryBuilder builder) {
        if (CollectionUtil.isNotEmpty(termQueryFieldAndValue)) {
            for (Pair<String, Object> filedAndValuePair : termQueryFieldAndValue) {
                String field = filedAndValuePair.getKey();
                Object value = filedAndValuePair.getValue();
                if (value instanceof Iterable) {
                    Iterable<?> values = (Iterable<?>) value;
                    TermsQueryBuilder termsQueryBuilder = new TermsQueryBuilder(field, values);
                    builder.filter(termsQueryBuilder);
                } else {
                    TermQueryBuilder termQueryBuilder = new TermQueryBuilder(field, value);
                    builder.filter(termQueryBuilder);
                }
            }
        }
    }

    public <T> CompletableFuture<List<EsSearchResult<T>>> productEmbeddingSearch(ProductEmbeddingSearchParam param, Class<T> resultClass) {
        String dslVmPath = "es_query_dsl/product_embedding_search.ftljson";
        Map<String, Object> params = new HashMap<>();
        params.put("excludeFields", JSON.toJSONString(EsService.getEmbeddingFieldNames(resultClass)));
        params.put("size", param.getSize());
        params.put("tenantId", param.getTenantId());
        params.put("projectId", param.getProjectId());
        if (param.getProductDetailEmbedding() != null) {
            params.put("productDetailEmbedding", JSON.toJSONString(param.getProductDetailEmbedding()));
        } else if (param.getTitleEmbedding() != null) {
            params.put("titleEmbedding", JSON.toJSONString(param.getTitleEmbedding()));
        } else if (param.getDescribeEmbedding() != null) {
            params.put("describeEmbedding", JSON.toJSONString(param.getDescribeEmbedding()));
        } else if (param.getChunkContentEmbedding() != null) {
            params.put("chunkContentEmbedding", JSON.toJSONString(param.getChunkContentEmbedding()));
        }
        if (param.getMinSkuPrice() != null) {
            params.put("minSkuPrice", param.getMinSkuPrice());
        }
        if (param.getMaxSkuPrice() != null) {
            params.put("maxSkuPrice", param.getMaxSkuPrice());
        }
        List<MatchShould> matchShouldList = new ArrayList<>();
        boolean hasShould = false;
        if (CollectionUtil.isNotEmpty(param.getTitleList())) {
            hasShould = true;
            for (String title : param.getTitleList()) {
                matchShouldList.add(new MatchShould().setField(LambdaUtil.getFieldName(ProductEsEntity::getTitle)).setValue(title));
            }
        }
        if (CollectionUtil.isNotEmpty(param.getDescribeList())) {
            hasShould = true;
            for (String describe : param.getDescribeList()) {
                matchShouldList.add(new MatchShould().setField(LambdaUtil.getFieldName(ProductEsEntity::getDescribe)).setValue(describe));
            }
        }
        if (CollectionUtil.isNotEmpty(matchShouldList)) {
            params.put("matchShouldList", matchShouldList);
        }
        if (hasShould) {
            params.put("hasShould", true);
        }
        String dsl = FreeMakerUtil.render(dslVmPath, params);
        CompletableFuture<List<EsSearchResult<T>>> future = search(param.getIndex(), dsl, resultClass);
        return future.thenApply(esSearchResults -> {
            for (EsSearchResult<T> esSearchResult : esSearchResults) {
                esSearchResult.setScore(esSearchResult.getScore() - 1.0);
            }
            return esSearchResults;
        });
    }

    public CompletableFuture<SearchResponse> productSearch(ProductSearchParam param) {
        String dslVmPath = "es_query_dsl/product_search.ftljson";
        Map<String, Object> params = new HashMap<>();
        if (param.getFrom() != null) params.put("from", param.getFrom());
        if (param.getSize() != null) params.put("size", param.getSize());
        if (param.getTenantId() != null) params.put("tenantId", param.getTenantId());
        if (param.getProjectId() != null) params.put("projectId", param.getProjectId());
        if (param.getProductIds() != null) params.put("productIds", JSON.toJSONString(param.getProductIds()));
        if (param.getDocumentId() != null) params.put("documentId", param.getDocumentId());
        if (param.getEnable() != null) params.put("enable", param.getEnable().toString());
        if (param.getMinSkuPrice() != null) params.put("minSkuPrice", param.getMinSkuPrice());
        if (param.getMaxSkuPrice() != null) params.put("maxSkuPrice", param.getMaxSkuPrice());
        List<MatchShould> matchShouldList = new ArrayList<>();
        boolean hasShould = false;
        if (CollectionUtil.isNotEmpty(param.getTitleList())) {
            hasShould = true;
            for (String title : param.getTitleList()) {
                matchShouldList.add(new MatchShould().setField(LambdaUtil.getFieldName(ProductEsEntity::getTitle)).setValue(title));
            }
        }
        if (CollectionUtil.isNotEmpty(param.getDescribeList())) {
            hasShould = true;
            for (String describe : param.getDescribeList()) {
                matchShouldList.add(new MatchShould().setField(LambdaUtil.getFieldName(ProductEsEntity::getDescribe)).setValue(describe));
            }
        }
        if (CollectionUtil.isNotEmpty(matchShouldList)) {
            params.put("matchShouldList", matchShouldList);
        }
        if (hasShould) {
            params.put("hasShould", true);
        }
        String dsl = FreeMakerUtil.render(dslVmPath, params);
        JSONObject dslJson = JSON.parseObject(dsl);
        SearchRequest searchRequest = getSearchRequest(ProductEsEntity.indexName, dslJson.toJSONString());
        return search(searchRequest);
    }

    @Data
    @Accessors(chain = true)
    public static class MatchShould {
        String field;
        String value;

        public MatchShould setValue(String value) {
            this.value = StringUtil.stringToJsonString(value);
            return this;
        }
    }
}
