package com.shulex.qabot.client.dto;

import com.shulex.gpt.apisdk.dto.Message;
import com.shulex.gpt.apisdk.dto.MixedQuery;
import com.shulex.gpt.apisdk.enums.BotLanguageEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Data
@Accessors(chain = true)
public class LabelSampleDTO {
    @ApiModelProperty(value = "chatId")
    String chatId;
    @ApiModelProperty(value = "创建时间")
    Long createdAt;
    @ApiModelProperty(value = "tenantId")
    Long tenantId;
    @ApiModelProperty(value = "projectId")
    Long projectId;
    @ApiModelProperty(value = "标签树id")
    Long treeId;
    @ApiModelProperty(value = "标签树节点id")
    Long nodeId;
    @ApiModelProperty(value = "样本")
    String example;
    @ApiModelProperty(value = "样本正负性,true代表正样本,false代表负样本")
    Boolean pnLabel = true;

    String clientType;
    List<List<String>> customIntents;
    String userEnQuery; //调用summary接口会返回
    String model;
    String touchPoint;
    String title;
    String content;
    List<Message> messages = new ArrayList<>();
    BotLanguageEnum userLanguage = BotLanguageEnum.ENGLISH;
    BotLanguageEnum knowledgeLanguage = BotLanguageEnum.ENGLISH;
    List<LabelExampleDTO> exampleDTOS = new ArrayList<>();
    Boolean isTagging = true;
    Boolean isMultiIntent = false;
    String product;
}
