package com.shulex.qabot.client.dto;

import lombok.Data;
import org.springframework.cglib.core.Local;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZonedDateTime;
import java.util.List;

/**
 * shopify商品相关的数据对象，只列出需要的部分字段而非全部字段
 */
public abstract class ShopifyDTO {
    @Data
    public static class Product {
        Long id;
        String handle;
        String title;
        String body_html;
        String vendor;
        LocalDateTime published_at;
        List<String> tags;
        List<ProductSku> variants;
        List<ProductImage> images;
        List<ProductOption> options;
    }

    @Data
    public static class ProductSku {
        Long id;
        Long product_id;
        String title;
        BigDecimal price;
        BigDecimal compare_at_price;
        String sku;
        Boolean requires_shipping;
        Long image_id;
        LocalDateTime created_at;
        Long grams;
        Long inventory_quantity;
        Object option1;
        Object option2;
        Object option3;
        Object option4;
        Object option5;
        Object option6;
        Object option7;
        Object option8;
        Object option9;
    }

    @Data
    public static class ProductImage {
        Long id;
        String src;
    }

    @Data
    public static class ProductOption {
        String name;
        Long position;
        List<String> values;
    }


    @Data
    public static class Meta {
        String country;
        String currency;
        String domain;
    }

    @Data
    public static class Collection {
        Long id;
        String title;
    }

    @Data
    public static class CollectionProductRelation {
        Long collectionId;
        List<Long> productIds;
    }
}
