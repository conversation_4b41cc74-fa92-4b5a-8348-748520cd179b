package com.shulex.qabot.client.dto.esentity;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

@Data
@Accessors(chain = true)
@NoArgsConstructor
public class KuDocEsEntity {
    public static final String indexName = "ku-doc-test2";

    String id;
    Long tenantId;
    Long projectId;
    String content;
    String chunk;
//    @EsEmbeddingField
    private List<BigDecimal> chunkEmbedding;
}
