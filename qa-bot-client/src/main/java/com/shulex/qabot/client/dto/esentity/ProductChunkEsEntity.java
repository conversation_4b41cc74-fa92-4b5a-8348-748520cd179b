package com.shulex.qabot.client.dto.esentity;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.annotation.TableField;
import com.shulex.qabot.client.dto.mysqlentity.KuProduct;
import com.shulex.qabot.client.dto.mysqlentity.ProductChunk;
import com.shulex.qabot.client.dto.typehandler.ListIntegerTypeHandler;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import static java.lang.String.format;

@Data
@Accessors(chain = true)
@NoArgsConstructor
public class ProductChunkEsEntity {
    public static final String indexName = "ku-product-chunk";

    String id;
    Boolean enable;
    Boolean indexed;
    Long tenantId;
    Long projectId;
    Long productDocId;
    Long kuProductId;
    Integer chunkSeq;
    String content;
    List<Integer> offset;
    String chunkField;
    @EsEmbeddingField
    List<BigDecimal> chunkContentEmbedding;
    // product层面的信息
    Boolean mainPush;
    Long documentId;
    String productId;
    String handle;
    String title;
    String describe;
    String vendor;
    String category;
    LocalDateTime publishedAt;
    Boolean requiresShipping;
    List<String> tags;
    List<String> images;
    String source;
    String platform;
    String country;
    String currency;
    String spuUrl;
    List<ProductEsEntity.Sku> skuList;
    List<String> selfTags; // 加权tag
    Set<Long> kuLabelValueIds;
    String brief;
    Long directoryId;
    Boolean isVisible;
    String brand;
    String storeName;

    public ProductChunkEsEntity(ProductChunk productChunk, KuProduct kuProduct) {
        this.setId(productChunk.getId().toString())
                .setEnable(productChunk.getEnable())
                .setIndexed(true)
                .setTenantId(productChunk.getTenantId())
                .setProjectId(productChunk.getProjectId())
                .setProductDocId(productChunk.getProductDocId())
                .setKuProductId(productChunk.getKuProductId())
                .setChunkSeq(productChunk.getChunkSeq())
                .setContent(productChunk.getContent())
                .setOffset(productChunk.getOffset())
                .setChunkField(productChunk.getChunkField());
        if (kuProduct != null) {
            this.setMainPush(kuProduct.getMainPush())
                    .setDocumentId(kuProduct.getDocumentId())
                    .setProductId(kuProduct.getProductId())
                    .setHandle(kuProduct.getHandle())
                    .setTitle(kuProduct.getTitle())
                    .setDescribe(kuProduct.getDescribe())
                    .setVendor(kuProduct.getVendor())
                    .setCategory(kuProduct.getCategory())
                    .setPublishedAt(kuProduct.getPublishedAt())
                    .setRequiresShipping(kuProduct.getRequiresShipping())
                    .setTags(kuProduct.getTags())
                    .setImages(kuProduct.getImages())
                    .setSource(kuProduct.getSource())
                    .setPlatform(kuProduct.getPlatform() == null ? null : kuProduct.getPlatform().name())
                    .setCountry(kuProduct.getCountry())
                    .setCurrency(kuProduct.getCurrency())
                    .setSpuUrl(kuProduct.getSpuUrl())
                    .setDirectoryId(kuProduct.getDirectoryId())
                    .setIsVisible(kuProduct.getIsVisible())
                    .setSkuList(ProductEsEntity.convertFromKuProductSku(kuProduct.getSkuList()));
        }
    }
}
