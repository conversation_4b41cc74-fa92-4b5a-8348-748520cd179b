package com.shulex.qabot.feign.gpt;

import com.shulex.gpt.apisdk.IIntentApi;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Profile;

@FeignClient(name = "shulex-gpt", contextId = "IIntentApiFeignClient")
@Profile({"test", "prod"})
public interface IIntentApiFeignClient extends IIntentApi {
}

@FeignClient(name = "shulex-gpt", contextId = "IIntentApiFeignClientDev", url = "http://10.7.4.9:8008")
@Profile({"dev"})
interface IIntentApiFeignClientDev extends IIntentApi {
}
