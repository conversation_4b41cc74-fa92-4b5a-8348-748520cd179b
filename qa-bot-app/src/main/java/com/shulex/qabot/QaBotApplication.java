package com.shulex.qabot;

import cn.hutool.extra.spring.SpringUtil;
import com.shulex.common.web.aspect.HttpAspect;
import com.shulex.common.web.handler.GlobalExceptionHandler;
import com.shulex.qabot.config.NoticeConfig;
import com.shulex.qabot.util.FreeMakerUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;

import java.net.InetAddress;

import static java.lang.String.format;

@SpringBootApplication(exclude = {GlobalExceptionHandler.class, HttpAspect.class}, scanBasePackages = {"com.shulex.qabot"})
@EnableFeignClients
@Slf4j
public class QaBotApplication implements CommandLineRunner {
    @Autowired
    NoticeConfig noticeConfig;
    static Long startTime;

    public static void main(String[] args) {
        startTime = System.currentTimeMillis();
        SpringApplication.run(QaBotApplication.class, args);
    }

    @Override
    public void run(String... args) {
        try {
            double consumeSeconds = (System.currentTimeMillis() - startTime) / 1000.0;
            log.info("启动完成: {}s", format("%.2fs", consumeSeconds));
            if (SpringUtil.getActiveProfile().equalsIgnoreCase("dev")) {
                Runtime.getRuntime().exec(new String[]{"osascript", "-e", String.format("display notification \"qa-bot %.2fs\" with title \"启动完成\"", consumeSeconds)});
            } else {
                noticeConfig.sendNotice(format("启动完成,%.2fs", consumeSeconds));
            }
        } catch (Exception ignore) {
        }
    }
}
