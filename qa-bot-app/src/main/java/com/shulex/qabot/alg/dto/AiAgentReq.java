package com.shulex.qabot.alg.dto;

import com.shulex.gpt.apisdk.enums.BotLanguageEnum;
import com.shulex.qabot.alg.enmus.agent.AgentName;
import com.shulex.qabot.client.dto.AiAgentMessage;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
public class AiAgentReq {
    String client;
    Long tenant_id;
    String chat_id;
    String touch_point;
    Context context;

    @Data
    @Accessors(chain = true)
    public static class Context {
        AgentName agent_name;
        BotLanguageEnum language;
        List<AiAgentMessage> messages;
    }
}
