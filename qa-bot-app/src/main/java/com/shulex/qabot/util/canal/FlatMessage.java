package com.shulex.qabot.util.canal;

import lombok.Getter;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

@Getter
public class FlatMessage implements Serializable {

    private static final long serialVersionUID = 2611556444074013268L;

    private String database;                               // 数据库或schema
    private String table;                                  // 表名
    private List<String> pkNames;
    private Boolean isDdl;
    // 类型:INSERT/UPDATE/DELETE
    private String type;
    // binlog executeTime, 执行耗时
    private Long es;
    // dml build timeStamp, 同步时间
    private Long ts;
    // 执行的sql,dml sql为空
    private String sql;
    // 数据列表
    private List<Map<String, Object>> data;
    // 旧数据列表,用于update,size和data的size一一对应
    private List<Map<String, Object>> old;

    public void setDatabase(String database) {
        this.database = database;
    }

    public void setTable(String table) {
        this.table = table;
    }

    public void setPkNames(List<String> pkNames) {
        this.pkNames = pkNames;
    }

    public void setIsDdl(Boolean isDdl) {
        this.isDdl = isDdl;
    }

    public void setType(String type) {
        this.type = type;
    }

    public void setTs(Long ts) {
        this.ts = ts;
    }

    public void setSql(String sql) {
        this.sql = sql;
    }

    public void setData(List<Map<String, Object>> data) {
        this.data = data;
    }

    public void setOld(List<Map<String, Object>> old) {
        this.old = old;
    }

    public void setEs(Long es) {
        this.es = es;
    }

    @Override
    public String toString() {
        return "FlatMessage{" + "database='" + database + '\'' + ", table='" + table + '\'' + ", pkNames=" + pkNames
                + ", isDdl=" + isDdl + ", type='" + type + '\'' + ", es=" + es + ", ts=" + ts + ", sql='" + sql + '\''
                + ", data=" + data + ", old=" + old + '}';
    }
}