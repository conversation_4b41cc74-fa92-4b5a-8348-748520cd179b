package com.shulex.qabot.client.dto.mysqlentity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.shulex.gpt.apisdk.enums.DocumentStatusEnum;
import com.shulex.gpt.apisdk.enums.DocumentTypeEnum;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

@Data
@Accessors(chain = true)
@TableName("document")
public class Document {
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    private String fileName;
    private String fileType;
    private String uri;
    private DocumentStatusEnum status;
    private String error;
    private Boolean disabled;
    private String metadata;
    private Long tenantId;
    private Long directoryId;
    private Long projectId;
    private Long createdBy;
    private Long updatedBy;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    private DocumentTypeEnum type;
    private Boolean isVisible;
}
