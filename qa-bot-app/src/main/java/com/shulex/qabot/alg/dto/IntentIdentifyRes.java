package com.shulex.qabot.alg.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.shulex.gpt.apisdk.dto.LLMUsage;
import com.shulex.gpt.apisdk.dto.LLMUsages;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;
import java.util.Map;

@Data
@Accessors(chain = true)
public class IntentIdentifyRes {
    Result result;
    LLMUsages usages;

    @Data
    @Accessors(chain = true)
    public static class Result {
        private String intent;
        private String originIntent;
        @JSONField(name = "intent_list")
        private List<String> intentList;
    }
}
