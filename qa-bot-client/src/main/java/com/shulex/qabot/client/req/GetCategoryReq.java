package com.shulex.qabot.client.req;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Pair;
import com.shulex.gpt.apisdk.dto.AiReplyPromptInfo;
import com.shulex.gpt.apisdk.dto.MixedQuery;
import com.shulex.gpt.apisdk.enums.BotLanguageEnum;
import com.shulex.gpt.apisdk.enums.LLMTypeEnum;
import com.shulex.qabot.client.dto.AiAgentMessage;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
public class GetCategoryReq {
    Long docId;
    String lang;
}
