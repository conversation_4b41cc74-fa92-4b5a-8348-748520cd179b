package com.shulex.qabot.util;

import com.alibaba.fastjson.JSON;
import freemarker.template.Configuration;
import freemarker.template.Template;
import freemarker.template.Version;
import lombok.SneakyThrows;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.core.io.ResourceLoader;
import org.springframework.stereotype.Component;
import org.springframework.ui.freemarker.FreeMarkerConfigurationFactoryBean;
import org.springframework.util.ResourceUtils;

import java.io.File;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.StringWriter;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

public class FreeMakerUtil {
    public static Configuration configuration;

    static {
        FreeMarkerConfigurationFactoryBean factoryBean = new FreeMarkerConfigurationFactoryBean();
        factoryBean.setTemplateLoaderPath("classpath:/");
        factoryBean.setDefaultEncoding("UTF-8");
        try {
            configuration = factoryBean.createConfiguration();
            configuration.setNumberFormat("0");
        } catch (Exception e) {
            throw new RuntimeException("Failed to create FreeMarker configuration", e);
        }
    }

    @SneakyThrows
    public static String render(String templatePath, Map<String, Object> params) {
        Template template = configuration.getTemplate(templatePath);
        StringWriter out = new StringWriter();
        template.process(params, out);
        out.flush();
        out.close();
        return out.toString();
    }
}
