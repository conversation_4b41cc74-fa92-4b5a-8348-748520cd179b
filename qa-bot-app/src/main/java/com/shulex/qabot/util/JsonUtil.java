package com.shulex.qabot.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;

public class JsonUtil {
    public static boolean isJsonArray(String str) {
        try {
            JSON.parseArray(str);
            return true;
        } catch (Exception ignore) {
            return false;
        }
    }

    public static boolean isJsonObject(String str) {
        try {
            JSON.parseObject(str);
            return true;
        } catch (Exception ignore) {
            return false;
        }
    }

    public static <T> T copyObjectByJson(Object object, Class<T> tClass) {
        if (object == null) {
            return null;
        }
        return JSON.parseObject(JSON.toJSONString(object), tClass);
    }

    public static <T> T copyObjectByJson(Object object, TypeReference<T> type) {
        if (object == null) {
            return null;
        }
        return JSON.parseObject(JSON.toJSONString(object), type);
    }
}
