package com.shulex.qabot.config;

import com.shulex.gpt.apisdk.dto.AiReplyPromptInfo;
import com.shulex.gpt.apisdk.enums.LLMTypeEnum;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.HashMap;
import java.util.Map;

@Data
@Component
@ConfigurationProperties(prefix = "prompt")
public class PromptConfig {
    String gpt4SystemContentTemplate;
    String gpt4SystemContentTemplate2;
    String haikuSystemContentTemplate;
    Map<Long, String> gpt4TenantIdMap = new HashMap<>();
    Map<Long, String> gpt4TenantIdMap2 = new HashMap<>();

//    public String getSystemContent(Long tenantId, String roleDescription, String contextContents, String language, int wordsLimit, LLMTypeEnum modelType) {
//        if (LLMTypeEnum.HAIKU.equals(modelType)) {
//            return String.format(haikuSystemContentTemplate, roleDescription, contextContents, language, wordsLimit);
//        } else {
//            String template = gpt4TenantIdMap.getOrDefault(tenantId, gpt4SystemContentTemplate);
//            return String.format(template, roleDescription, contextContents, language, wordsLimit);
//        }
//    }

    public String getSystemContent(Long tenantId, AiReplyPromptInfo aiReplyPromptInfo, String contextContents, String language, int wordsLimit, LLMTypeEnum modelType) {
        if (LLMTypeEnum.HAIKU.equals(modelType)) {
            return String.format(haikuSystemContentTemplate, aiReplyPromptInfo.getRoleDescription(), contextContents, language, wordsLimit);
        } else {
            // 租户特殊配置
            if (StringUtils.hasText(aiReplyPromptInfo.getTaskDescription())) {
                if (gpt4TenantIdMap2.containsKey(tenantId)) {
                    return String.format(gpt4TenantIdMap2.get(tenantId), aiReplyPromptInfo.getRoleDescription(), contextContents, aiReplyPromptInfo.getTaskDescription(), language, wordsLimit);
                }
            }
            if (gpt4TenantIdMap.containsKey(tenantId)) {
                return String.format(gpt4TenantIdMap.get(tenantId), aiReplyPromptInfo.getRoleDescription(), contextContents, language, wordsLimit);
            }

            if (StringUtils.hasText(aiReplyPromptInfo.getTaskDescription())) {
                return String.format(gpt4SystemContentTemplate2, aiReplyPromptInfo.getRoleDescription(), contextContents, aiReplyPromptInfo.getTaskDescription(), language, wordsLimit);
            } else {
                return String.format(gpt4SystemContentTemplate, aiReplyPromptInfo.getRoleDescription(), contextContents, language, wordsLimit);
            }
        }
    }
}
