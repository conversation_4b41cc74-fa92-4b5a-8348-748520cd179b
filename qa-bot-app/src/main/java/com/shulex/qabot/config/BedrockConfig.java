package com.shulex.qabot.config;

import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;

@Data
@Component
@ConfigurationProperties(prefix = "bedrock")
public class BedrockConfig {
    List<AkSkConfig> akSkConfigs;

    @Data
    @Accessors(chain = true)
    public static class AkSkConfig {
        String ak;
        String sk;
        String region;
    }
}
