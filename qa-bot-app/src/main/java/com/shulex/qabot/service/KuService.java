package com.shulex.qabot.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.otter.canal.protocol.CanalEntry;
import com.shulex.gpt.apisdk.IKBApi;
import com.shulex.gpt.apisdk.IToolsApi;
import com.shulex.gpt.apisdk.dto.DetectLanguageRequest;
import com.shulex.gpt.apisdk.dto.DetectLanguageResponse;
import com.shulex.gpt.apisdk.dto.GetKuResponse;
import com.shulex.gpt.apisdk.enums.BotLanguageEnum;
import com.shulex.gpt.apisdk.enums.KuStatusEnum;
import com.shulex.gpt.apisdk.enums.KuTypeEnum;
import com.shulex.qabot.alg.AlgClient;
import com.shulex.qabot.alg.dto.TextEmbedRes;
import com.shulex.qabot.client.res.TranslateTagKeywordRes;
import com.shulex.qabot.config.InvokeGptConfig;
import com.shulex.qabot.es.EsService;
import com.shulex.qabot.client.dto.esentity.KuEsEntity;
import com.shulex.qabot.client.dto.mysqlentity.Ku;
import com.shulex.qabot.util.ParallelUtil;
import com.shulex.qabot.util.RetryUtil;
import com.shulex.qabot.util.StopWatchUtil;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StopWatch;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.*;
import java.util.function.Consumer;
import java.util.stream.Collectors;

@Service
@Slf4j
public class KuService {
    @Autowired
    BedrockService bedrockService;
    @Autowired
    AlgClient algClient;
    @Autowired
    EsService esService;
    @Autowired
    IToolsApi gptToolApiFeignClient;
    @Autowired
    InvokeGptConfig invokeGptConfig;
    @Autowired
    IKBApi kbApi;
    @Autowired
    KuLabelRelationService kuLabelRelationService;


    @SneakyThrows
    public void insertKuToEs(List<Ku> kuList, Integer threadPoolSize) {
        if (ObjectUtils.isEmpty(kuList)) {
            return;
        }
        // init fields
        List<KuEsEntity> kuEsEntityList = kuList.stream().map(KuEsEntity::new).collect(Collectors.toList());
        setKuTags(kuEsEntityList);
        // embedding
        Consumer<KuEsEntity> generateEmbeddingFunction = kuEsEntity -> {
            StopWatch sw = new StopWatch("generateEmbeddingFunction");
            sw.start("embeddingKuEsEntity");
            embeddingKuEsEntity(kuEsEntity);
            sw.stop();
            kuEsEntity.setStatus(KuStatusEnum.INDEXED);
            sw.start("esServiceInsert");
            RetryUtil.retry(3, 1000, () -> esService.insert(KuEsEntity.indexName, MapUtil.of(kuEsEntity.getId().toString(), JSON.toJSONString(kuEsEntity))));
            sw.stop();
//            log.info("{}", StopWatchUtil.summary(sw));
        };
        if (threadPoolSize == null) {
            kuEsEntityList.forEach(generateEmbeddingFunction);
        } else {
            ParallelUtil.parallelCall(threadPoolSize, false, kuEsEntityList, generateEmbeddingFunction);
        }
        // insert to es
//        Map<String, String> id2JsonData = kuEsEntityList.stream().collect(Collectors.toMap(e -> e.getId().toString(), JSON::toJSONString, (v1, v2) -> v2));
//        esService.insert(KuEsEntity.indexName, id2JsonData);
        log.info("insertKuToEs: embedding kuIds={} and insert to index {}", kuList.stream().map(Ku::getId).collect(Collectors.toList()), KuEsEntity.indexName);
        callShulexGptKuIndexed(kuList.stream().map(Ku::getId).collect(Collectors.toList()));
        for (Ku ku : kuList) {
            kuLabelRelationService.retryUpdateKuLabelRelationToEs(KuTypeEnum.QA, ku.getId(), CanalEntry.EventType.INSERT);
        }
        log.info("insertKuToEs: add relation success, ids={}", kuList.stream().map(Ku::getId).collect(Collectors.toList()));
    }

    public void insertKuToEs(List<Ku> kuList, Integer threadPoolSize, boolean notInsertIfExist) {
        if (CollectionUtil.isEmpty(kuList)) {
            return;
        }
        if (!notInsertIfExist) {
            insertKuToEs(kuList, threadPoolSize);
        } else {
            List<String> kuIds = kuList.stream().map(e -> e.getId().toString()).collect(Collectors.toList());
            Map<String, Boolean> existMap = esService.exist(KuEsEntity.indexName, kuIds);
            List<Ku> needInsertKus = kuList.stream().filter(ku -> {
                Boolean exist = existMap.get(ku.getId().toString());
                return !Boolean.TRUE.equals(exist);
            }).collect(Collectors.toList());
            insertKuToEs(needInsertKus, threadPoolSize);
        }
    }

    public void deleteKuInEs(List<Ku> updateKuList) {
        if (ObjectUtils.isEmpty(updateKuList)) {
            return;
        }
        List<Long> kuIds = updateKuList.stream().map(Ku::getId).filter(Objects::nonNull).collect(Collectors.toList());
        if (ObjectUtils.isEmpty(kuIds)) {
            return;
        }
        List<String> ids = kuIds.stream().map(Object::toString).collect(Collectors.toList());
        esService.delete(KuEsEntity.indexName, ids, true);
        log.info("deleteKuInEs: kuIds={}", ids);
    }

    public void updateKuInEs(List<Ku> updateKuList, boolean needReEmbedding) {
        if (ObjectUtils.isEmpty(updateKuList)) {
            return;
        }
        // init fields
        List<KuEsEntity> updateKuEsEntityList = updateKuList.stream().map(ku -> {
            KuEsEntity kuEsEntity = new KuEsEntity(ku);
            if (ku.getStatus().equals(KuStatusEnum.DELETING)) {
                kuEsEntity.setStatus(KuStatusEnum.DELETING);
            } else {
                kuEsEntity.setStatus(KuStatusEnum.INDEXED);
            }
            return kuEsEntity;
        }).collect(Collectors.toList());
        setKuTags(updateKuEsEntityList);
        // re embedding
        if (needReEmbedding) {
            for (KuEsEntity kuEsEntity : updateKuEsEntityList) {
                embeddingKuEsEntity(kuEsEntity);
            }
        }
        // update es
        Map<String, String> id2UpdateJsonData = updateKuEsEntityList.stream().collect(Collectors.toMap(e -> e.getId().toString(), JSON::toJSONString, (v1, v2) -> v2));
        esService.update(KuEsEntity.indexName, id2UpdateJsonData, true);
        if (needReEmbedding) {
            callShulexGptKuIndexed(updateKuList.stream().map(Ku::getId).collect(Collectors.toList()));
        }
        log.info("updateKuInEs: needReEmbedding={}, updated kuIds={}", needReEmbedding, id2UpdateJsonData.keySet());
    }

    public void embeddingKuEsEntity(KuEsEntity kuEsEntity) {
        String title = kuEsEntity.getTitle();
        String content = kuEsEntity.getContent();
        if (title == null) {
            title = "";
        }
        if (content == null) {
            content = "";
        }
        StopWatch sw = new StopWatch("embeddingKuEsEntity");
        // 翻译
        sw.start("translate-detectLang");
        BotLanguageEnum titleLanguage = BotLanguageEnum.AUTO;
        BotLanguageEnum contentLanguage = BotLanguageEnum.AUTO;
        try {
            DetectLanguageResponse titleLanguageRes = gptToolApiFeignClient.detectLanguage(new DetectLanguageRequest(title), invokeGptConfig.getXToken());
            DetectLanguageResponse contentLanguageRes = gptToolApiFeignClient.detectLanguage(new DetectLanguageRequest(content), invokeGptConfig.getXToken());
            titleLanguage = titleLanguageRes == null ? BotLanguageEnum.AUTO : titleLanguageRes.getLanguage();
            contentLanguage = contentLanguageRes == null ? BotLanguageEnum.AUTO : contentLanguageRes.getLanguage();
        } catch (Exception e) {
            log.warn("translate-detectLang, ", e);
        }
        sw.stop();
        String englishTitle = null, englishContent = null;
        sw.start("translate-trans");
        if (titleLanguage.equals(BotLanguageEnum.ENGLISH)) {
            englishTitle = title;
        } else {
            try {
                List<TranslateTagKeywordRes> translateList = TranslateService.translate(Arrays.asList(title), Arrays.asList(BotLanguageEnum.ENGLISH));
                if (CollectionUtil.isNotEmpty(translateList)) {
                    englishTitle = translateList.get(0).getToLanguageKeyword();
                }
            } catch (Exception e) {
                log.warn("edge翻译失败,", e);
            }
        }
        if (contentLanguage.equals(BotLanguageEnum.ENGLISH)) {
            englishContent = content;
        } else {
            try {
                List<TranslateTagKeywordRes> translateList = TranslateService.translate(Arrays.asList(content), Arrays.asList(BotLanguageEnum.ENGLISH));
                if (CollectionUtil.isNotEmpty(translateList)) {
                    englishContent = translateList.get(0).getToLanguageKeyword();
                }
            } catch (Exception e) {
                log.warn("edge翻译失败,", e);
            }
        }
        sw.stop();
        // 向量
        sw.start("embedding");
        CompletableFuture<TextEmbedRes> titleEmbeddingMpnetFuture = algClient.textEmbed(englishTitle == null ? title : englishTitle);
        CompletableFuture<TextEmbedRes> contentEmbeddingMpnetFuture = algClient.textEmbed(englishContent == null ? content : englishContent);
        CompletableFuture<List<BigDecimal>> titleEmbeddingCohereFuture = bedrockService.getCohereEmbeddingAsync(title);
        CompletableFuture<List<BigDecimal>> contentEmbeddingCohereFuture = bedrockService.getCohereEmbeddingAsync(content);

        List<BigDecimal> titleEmbeddingMpnet = titleEmbeddingMpnetFuture.join().getResult();
        List<BigDecimal> contentEmbeddingMpnet = contentEmbeddingMpnetFuture.join().getResult();
        List<BigDecimal> titleEmbeddingCohere = titleEmbeddingCohereFuture.join();
        List<BigDecimal> contentEmbeddingCohere = contentEmbeddingCohereFuture.join();
        sw.stop();
        log.info("{}", StopWatchUtil.summary(sw));
        kuEsEntity.setTitleEmbeddingCohere(titleEmbeddingCohere);
        kuEsEntity.setTitleEmbeddingMpnet(titleEmbeddingMpnet);
        kuEsEntity.setContentEmbeddingCohere(contentEmbeddingCohere);
        kuEsEntity.setContentEmbeddingMpnet(contentEmbeddingMpnet);
    }

    public void callShulexGptKuIndexed(List<Long> kuIdList) {
    }


    private void setKuTags(List<KuEsEntity> kuEsEntityList) {
        if (CollectionUtil.isNotEmpty(kuEsEntityList)) {
            List<Long> kuIds = kuEsEntityList.stream().map(KuEsEntity::getId).distinct().collect(Collectors.toList());
            List<GetKuResponse> kus = kbApi.getKus(kuEsEntityList.get(0).getTenantId(), kuEsEntityList.get(0).getProjectId(), kuIds);

            Map<Long, GetKuResponse> kuIdMap = kus.stream().collect(Collectors.toMap(GetKuResponse::getId, e -> e, (v1, v2) -> v2));
            for (KuEsEntity kuEsEntity : kuEsEntityList) {
                GetKuResponse response = kuIdMap.get(kuEsEntity.getId());
                if (response != null) {
                    List<String> mergeTags = new ArrayList<>();
                    List<String> tags = response.getTags();
                    List<String> intents = response.getIntents();
                    if (CollectionUtil.isNotEmpty(tags)) {
                        mergeTags.addAll(tags);
                    }
                    if (CollectionUtil.isNotEmpty(intents)) {
                        mergeTags.addAll(intents);
                    }
                    // 设置doc级别信息
                    kuEsEntity.setTags(mergeTags.stream().distinct().collect(Collectors.toList())).setBrief(response.getBrief() == null ? "" : response.getBrief());
                }
            }
        }
    }
}
