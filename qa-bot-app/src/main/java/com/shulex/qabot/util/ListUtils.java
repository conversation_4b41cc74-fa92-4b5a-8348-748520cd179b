package com.shulex.qabot.util;

import java.util.ArrayList;
import java.util.List;

public class ListUtils {
    @SafeVarargs
    public static <T> List<T> join(List<T>... lists) {
        List<T> result = new ArrayList<>();
        if (lists == null) {
            return result;
        }
        for (List<T> list : lists) {
            if (list != null) {
                result.addAll(list);
            }
        }
        return result;
    }
}
