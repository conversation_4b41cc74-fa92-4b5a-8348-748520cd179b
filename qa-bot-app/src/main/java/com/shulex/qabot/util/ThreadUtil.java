package com.shulex.qabot.util;

import java.util.concurrent.*;

public class ThreadUtil {
    public static ThreadPoolExecutor newExecutor(String namePrefix, int poolSize, int queueCapacity) {
        return new ThreadPoolExecutor(
                poolSize,
                poolSize,
                0,
                TimeUnit.SECONDS,
                new LinkedBlockingQueue<>(queueCapacity),
                r -> new Thread(r, namePrefix + r.hashCode()),
                new ThreadPoolExecutor.CallerRunsPolicy()
        );
    }
}
