package com.shulex.qabot.service;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.otter.canal.protocol.CanalEntry;
import com.shulex.gpt.apisdk.IKBApi;
import com.shulex.gpt.apisdk.dto.GetDocumentResponse;
import com.shulex.gpt.apisdk.enums.KuTypeEnum;
import com.shulex.qabot.client.dto.mysqlentity.Document;
import com.shulex.qabot.es.EsService;
import com.shulex.qabot.client.dto.esentity.DocumentChunkEsEntity;
import com.shulex.qabot.client.dto.mysqlentity.DocumentChunk;
import com.shulex.qabot.mysql.repository.DocumentChunkRepository;
import com.shulex.qabot.mysql.repository.DocumentRepository;
import com.shulex.qabot.util.ParallelUtil;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@Service
@Slf4j
public class DocumentChunkService {
    @Autowired
    EsService esService;
    @Autowired
    BedrockService bedrockService;
    @Autowired
    DocumentChunkRepository documentChunkRepository;
    @Autowired
    KuLabelRelationService kuLabelRelationService;
    @Autowired
    DocumentRepository documentRepository;
    @Autowired
    IKBApi kbApi;

    public void insertDocumentChunkToEs(List<DocumentChunk> documentChunks) {
        if (CollectionUtil.isEmpty(documentChunks)) {
            return;
        }
        List<DocumentChunkEsEntity> esEntities = documentChunks.stream().map(DocumentChunkEsEntity::new).collect(Collectors.toList());
        List<Long> documentIdList = documentChunks.stream().map(DocumentChunk::getDocumentId).distinct().collect(Collectors.toList());
        List<Document> documents = documentRepository.listByIds(documentIdList);
        Map<Long, Boolean> visibleMap = documents.stream().collect(Collectors.toMap(Document::getId, Document::getIsVisible, (v1, v2) -> v2));
        esEntities = esEntities.stream().map(p -> p.setIsVisible(visibleMap.getOrDefault(p.getDocumentId(), true))).collect(Collectors.toList());

        embedding(esEntities);
        setDocumentInfo(esEntities);
        Map<String, String> id2JsonData = esEntities.stream().collect(Collectors.toMap(e -> e.getId().toString(), JSON::toJSONString, (v1, v2) -> v2));
        esService.insert(DocumentChunkEsEntity.indexName, id2JsonData);
        documentChunkRepository.indexSucceed(documentChunks.stream().map(DocumentChunk::getId).collect(Collectors.toList()));
        log.info("insertDocumentChunkToEs: ids={}", id2JsonData.keySet());
        List<Long> kuIds = documentChunks.stream().map(DocumentChunk::getDocumentId).distinct().collect(Collectors.toList());
        for (Long kuId : kuIds) {
            kuLabelRelationService.retryUpdateKuLabelRelationToEs(KuTypeEnum.TC, kuId, CanalEntry.EventType.INSERT);
        }
        log.info("insertDocumentChunkToEs: add relation success, ids={}", id2JsonData.keySet());
    }

    public void deleteDocumentChunkInEs(List<DocumentChunk> documentChunks) {
        if (CollectionUtil.isEmpty(documentChunks)) {
            return;
        }
        List<String> ids = documentChunks.stream().map(DocumentChunk::getId).filter(Objects::nonNull).map(e -> e.toString()).collect(Collectors.toList());
        esService.delete(DocumentChunkEsEntity.indexName, ids, true);
        log.info("deleteDocumentChunkInEs: ids={}", ids);
    }

    public void updateDocumentChunkInEs(List<DocumentChunk> documentChunks, boolean needEmbedding) {
        if (CollectionUtil.isEmpty(documentChunks)) {
            return;
        }
        List<DocumentChunkEsEntity> esEntities = documentChunks.stream().map(DocumentChunkEsEntity::new).collect(Collectors.toList());
        List<Long> documentIdList = documentChunks.stream().map(DocumentChunk::getDocumentId).distinct().collect(Collectors.toList());
        List<Document> documents = documentRepository.listByIds(documentIdList);
        Map<Long, Boolean> visibleMap = documents.stream().collect(Collectors.toMap(Document::getId, Document::getIsVisible, (v1, v2) -> v2));
        esEntities = esEntities.stream().map(p -> p.setIsVisible(visibleMap.getOrDefault(p.getDocumentId(), true))).collect(Collectors.toList());
        if (needEmbedding) {
            embedding(esEntities);
        }
        setDocumentInfo(esEntities);
        Map<String, String> id2JsonData = esEntities.stream().collect(Collectors.toMap(e -> e.getId().toString(), JSON::toJSONString, (v1, v2) -> v2));
        esService.update(DocumentChunkEsEntity.indexName, id2JsonData, true);
        log.info("updateDocumentChunkInEs: needEmbedding={}, ids={}", needEmbedding, id2JsonData.keySet());
    }


    @SneakyThrows
    private void embedding(List<DocumentChunkEsEntity> esEntities) {
        if (CollectionUtil.isEmpty(esEntities)) {
            return;
        }
        Exception exception = null;
        int retry = 3;
        while (retry > 0) {
            try {
                ParallelUtil.parallelAsyncCall(2, false, esEntities, esEntity -> {
                    if (esEntity.getContentEmbedding() == null) {
                        String content = esEntity.getContent() == null ? "" : esEntity.getContent();
                        return bedrockService.getCohereEmbeddingAsync(content).thenAccept(esEntity::setContentEmbedding);
                    } else {
                        return CompletableFuture.completedFuture(null);
                    }
                });
                return;
            } catch (Exception e) {
                exception = e;
                retry--;
                Thread.sleep(2000);
            }
        }
        throw new RuntimeException(exception);
    }

    private void setDocumentInfo(List<DocumentChunkEsEntity> documentChunks) {
        if (CollectionUtil.isNotEmpty(documentChunks)) {
            List<Long> docIds = documentChunks.stream().map(DocumentChunkEsEntity::getDocumentId).distinct().collect(Collectors.toList());
            List<GetDocumentResponse> docs = kbApi.getDocuments(documentChunks.get(0).getTenantId(), documentChunks.get(0).getProjectId(), null, docIds, null);
            Map<Long, GetDocumentResponse> docIdMap = docs.stream().collect(Collectors.toMap(GetDocumentResponse::getId, e -> e, (v1, v2) -> v2));
            for (DocumentChunkEsEntity chunk : documentChunks) {
                GetDocumentResponse doc = docIdMap.get(chunk.getDocumentId());
                if (doc != null) {
                    chunk.setDirectoryId(doc.getDirectoryId());
                    if (doc.getDisabled() != null) {
                        chunk.setEnable(!doc.getDisabled());
                    }
                    // 设置doc级别信息
                    List<String> mergeTags = new ArrayList<>();
                    List<String> tags = doc.getTags();
                    List<String> intents = doc.getIntents();
                    if (CollectionUtil.isNotEmpty(tags)) {
                        mergeTags.addAll(tags);
                    }
                    if (CollectionUtil.isNotEmpty(intents)) {
                        mergeTags.addAll(intents);
                    }
                    chunk.setTags(mergeTags.stream().distinct().collect(Collectors.toList())).setBrief(doc.getBrief() == null ? "" : doc.getBrief());
                }
            }
        }
    }
}
